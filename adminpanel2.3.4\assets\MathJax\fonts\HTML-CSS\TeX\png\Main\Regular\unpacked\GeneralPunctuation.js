/*************************************************************
 *
 *  MathJax/fonts/HTML-CSS/TeX/png/Main/Regular/GeneralPunctuation.js
 *  
 *  Defines the image size data needed for the HTML-CSS OutputJax
 *  to display mathematics using fallback images when the fonts
 *  are not available to the client browser.
 *
 *  ---------------------------------------------------------------------
 *
 *  Copyright (c) 2009-2013 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 *
 */

MathJax.OutputJax["HTML-CSS"].defineImageData({
  "MathJax_Main": {
    0x2002: [  // ??
      [1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],
      [1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0]
    ],
    0x2003: [  // ??
      [1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],
      [1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0]
    ],
    0x2004: [  // ??
      [1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],
      [1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0]
    ],
    0x2005: [  // ??
      [1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],
      [1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0]
    ],
    0x2006: [  // ??
      [1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],
      [1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0]
    ],
    0x2009: [  // ??
      [1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],
      [1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0]
    ],
    0x200A: [  // ??
      [1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],
      [1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0]
    ],
    0x2013: [  // EN DASH
      [4,1,-1],[5,1,-2],[5,1,-2],[6,1,-2],[7,1,-3],[9,1,-4],[10,1,-5],[12,1,-6],
      [14,2,-6],[17,3,-8],[20,3,-9],[24,2,-12],[28,3,-14],[33,3,-17]
    ],
    0x2014: [  // EM DASH
      [7,1,-1],[9,1,-2],[10,1,-2],[12,1,-2],[14,1,-3],[17,1,-4],[20,1,-5],[24,1,-6],
      [28,2,-6],[33,3,-8],[40,3,-9],[47,2,-12],[56,3,-14],[66,3,-17]
    ],
    0x2018: [  // LEFT SINGLE QUOTATION MARK
      [2,2,-3],[2,4,-2],[2,4,-4],[3,4,-4],[3,4,-5],[4,6,-6],[4,7,-7],[5,8,-9],
      [6,9,-11],[7,11,-12],[8,13,-15],[10,15,-18],[11,18,-21],[14,21,-25]
    ],
    0x2019: [  // RIGHT SINGLE QUOTATION MARK
      [2,3,-2],[2,3,-3],[3,4,-4],[3,4,-4],[3,5,-4],[4,6,-6],[5,7,-7],[5,8,-9],
      [6,10,-10],[7,11,-12],[9,13,-15],[10,15,-18],[12,18,-21],[14,22,-25]
    ],
    0x201C: [  // LEFT DOUBLE QUOTATION MARK
      [4,2,-3],[4,4,-2],[5,4,-4],[6,4,-4],[7,4,-5],[8,6,-6],[10,7,-7],[11,8,-9],
      [13,9,-11],[16,11,-12],[19,13,-15],[22,15,-18],[26,18,-21],[31,21,-25]
    ],
    0x201D: [  // RIGHT DOUBLE QUOTATION MARK
      [3,3,-2],[4,3,-3],[4,4,-4],[5,4,-4],[6,5,-4],[7,6,-6],[8,7,-7],[9,8,-9],
      [11,10,-10],[13,11,-12],[15,13,-15],[18,15,-18],[21,18,-21],[25,22,-25]
    ],
    0x2020: [  // DAGGER
      [3,6,1],[4,8,2],[4,10,2],[5,10,2],[6,12,3],[7,16,4],[8,18,4],[9,23,5],
      [11,27,6],[13,31,7],[16,37,8],[18,44,11],[22,52,12],[26,62,15]
    ],
    0x2021: [  // DOUBLE DAGGER
      [3,6,1],[4,8,2],[4,10,2],[5,10,2],[6,12,3],[7,16,4],[8,18,4],[10,23,5],
      [11,27,6],[13,31,7],[16,37,8],[19,43,9],[22,51,11],[26,61,13]
    ],
    0x2026: [  // HORIZONTAL ELLIPSIS
      [8,1,0],[10,2,0],[11,2,0],[13,2,0],[16,2,0],[19,3,0],[22,3,0],[26,3,0],
      [31,4,0],[36,5,0],[43,5,0],[51,6,0],[61,7,0],[72,9,0]
    ],
    0x2032: [  // PRIME
      [2,4,0],[3,5,0],[3,6,0],[4,6,0],[4,7,-1],[5,9,-1],[6,11,-1],[7,13,-1],
      [8,15,-1],[9,18,-1],[11,21,-2],[13,24,-2],[15,29,-2],[18,35,-3]
    ]
  }
});

MathJax.Ajax.loadComplete(MathJax.OutputJax["HTML-CSS"].imgDir+"/Main/Regular"+
                          MathJax.OutputJax["HTML-CSS"].imgPacked+"/GeneralPunctuation.js");
