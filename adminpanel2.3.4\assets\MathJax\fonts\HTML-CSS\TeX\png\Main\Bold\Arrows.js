/*
 *  /MathJax/fonts/HTML-CSS/TeX/png/Main/Bold/Arrows.js
 *  
 *  Copyright (c) 2010-2013 The MathJax Consortium
 *
 *  Part of the MathJax library.
 *  See http://www.mathjax.org for details.
 * 
 *  Licensed under the Apache License, Version 2.0;
 *  you may not use this file except in compliance with the License.
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 */

MathJax.OutputJax["HTML-CSS"].defineImageData({"MathJax_Main-bold":{8592:[[8,5,1],[9,5,0],[11,7,1],[13,7,1],[15,9,1],[18,10,1],[22,11,1],[26,13,1],[30,16,1],[36,18,1],[43,22,1],[51,25,1],[60,30,1],[72,36,2]],8593:[[5,7,2],[5,8,2],[6,9,2],[7,12,3],[9,13,3],[10,16,4],[11,18,4],[14,22,5],[16,26,6],[19,30,7],[22,36,8],[27,42,9],[31,50,11],[37,59,13]],8594:[[8,5,1],[9,5,0],[11,7,1],[13,7,1],[15,9,1],[18,10,1],[22,11,1],[26,13,1],[30,16,1],[36,18,1],[43,22,1],[51,26,1],[60,30,1],[72,36,1]],8595:[[5,7,2],[5,8,2],[6,9,2],[7,12,3],[9,13,3],[10,16,4],[11,18,4],[14,22,5],[16,26,6],[19,30,7],[22,36,8],[27,43,10],[31,50,11],[38,59,13]],8596:[[8,5,1],[9,5,0],[11,7,1],[13,7,1],[15,9,1],[18,10,1],[21,11,1],[26,13,1],[30,16,1],[36,18,1],[43,22,1],[51,25,1],[60,30,1],[71,36,2]],8597:[[5,8,2],[5,10,3],[6,11,3],[7,14,4],[9,15,4],[10,18,5],[11,21,6],[14,25,7],[16,30,8],[19,35,9],[22,42,11],[27,49,13],[31,58,15],[38,69,18]],8598:[[8,7,2],[9,9,2],[11,10,2],[13,12,3],[15,13,3],[18,17,4],[21,19,4],[26,22,5],[30,26,6],[36,31,7],[43,37,8],[51,43,9],[60,51,11],[72,61,13]],8599:[[8,8,2],[9,9,2],[11,10,2],[13,12,3],[15,13,3],[18,17,4],[22,19,4],[26,22,5],[30,26,6],[36,31,7],[43,37,8],[51,43,9],[60,52,11],[72,62,13]],8600:[[8,7,2],[9,9,3],[11,10,3],[13,12,3],[15,13,3],[18,16,4],[22,19,5],[26,23,6],[30,27,7],[36,31,8],[43,37,9],[51,44,11],[60,52,13],[72,61,15]],8601:[[8,7,2],[9,9,3],[11,10,3],[13,12,3],[15,13,3],[18,16,4],[22,19,5],[26,23,6],[30,26,6],[36,31,8],[43,37,9],[51,44,11],[60,52,13],[72,61,15]],8614:[[8,5,1],[9,5,0],[11,7,1],[13,7,1],[15,9,1],[18,9,0],[21,11,1],[26,14,1],[30,16,1],[36,18,1],[43,22,1],[51,26,1],[60,30,1],[71,36,1]],8617:[[9,5,1],[10,5,0],[12,7,1],[15,7,1],[17,9,1],[21,10,1],[24,11,1],[29,13,1],[34,16,1],[40,18,1],[48,22,1],[57,25,1],[68,30,1],[80,36,2]],8618:[[9,5,1],[11,5,0],[12,7,1],[15,7,1],[17,9,1],[21,10,1],[24,11,1],[29,14,1],[34,16,1],[40,19,1],[48,22,1],[57,26,1],[68,30,1],[80,36,1]],8636:[[8,3,-1],[9,3,-2],[11,4,-2],[13,4,-2],[15,5,-3],[18,6,-3],[22,6,-4],[26,7,-5],[30,9,-6],[36,10,-7],[43,13,-8],[51,15,-10],[60,17,-12],[72,20,-14]],8637:[[8,3,1],[9,3,0],[11,4,1],[13,5,1],[15,5,1],[18,6,1],[22,7,1],[26,8,1],[30,9,1],[36,11,1],[43,12,1],[51,15,1],[60,18,2],[72,21,2]],8640:[[8,3,-1],[9,3,-2],[11,4,-2],[13,4,-2],[15,5,-3],[18,6,-3],[22,6,-4],[26,7,-5],[30,9,-6],[36,10,-7],[43,13,-8],[51,15,-10],[60,17,-12],[72,20,-14]],8641:[[8,3,1],[9,3,0],[11,4,1],[13,5,1],[15,5,1],[18,6,1],[22,7,1],[26,8,1],[30,9,1],[36,11,1],[43,12,1],[51,15,1],[60,18,2],[72,21,2]],8652:[[8,7,1],[9,6,0],[11,9,1],[13,10,1],[15,12,1],[18,13,1],[22,15,1],[26,18,1],[30,21,1],[36,25,1],[43,30,1],[51,35,1],[60,42,2],[72,50,2]],8656:[[8,5,1],[9,6,1],[11,7,1],[13,8,1],[15,9,1],[18,11,1],[22,13,1],[26,15,2],[30,18,2],[36,21,2],[43,24,2],[51,29,3],[60,34,3],[72,41,4]],8657:[[5,7,2],[6,8,2],[7,9,2],[8,12,3],[10,13,3],[12,16,4],[13,18,4],[16,22,5],[19,26,6],[22,30,7],[26,36,8],[32,42,9],[37,50,11],[44,59,13]],8658:[[8,5,1],[9,6,1],[11,7,1],[13,8,1],[15,9,1],[18,11,1],[22,12,1],[26,14,1],[30,18,2],[36,21,2],[43,24,2],[51,28,2],[60,34,3],[72,40,3]],8659:[[5,7,2],[6,8,2],[7,9,2],[8,12,3],[10,13,3],[12,16,4],[14,18,4],[16,22,5],[19,26,6],[23,30,7],[27,36,8],[32,43,10],[38,50,11],[45,59,13]],8660:[[8,5,1],[10,6,1],[11,7,1],[13,8,1],[16,9,1],[19,11,1],[22,12,1],[26,15,2],[31,18,2],[37,21,2],[44,24,2],[52,29,3],[61,34,3],[73,41,4]],8661:[[5,8,2],[6,10,3],[7,11,3],[8,13,4],[10,15,4],[11,18,5],[14,21,6],[16,25,7],[19,30,8],[22,35,9],[27,41,11],[32,49,13],[38,58,15],[45,69,18]]}});MathJax.Ajax.loadComplete(MathJax.OutputJax["HTML-CSS"].imgDir+"/Main/Bold"+MathJax.OutputJax["HTML-CSS"].imgPacked+"/Arrows.js");

