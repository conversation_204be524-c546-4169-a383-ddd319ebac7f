/*
 *  /MathJax/fonts/HTML-CSS/TeX/png/Fraktur/Bold/Other.js
 *  
 *  Copyright (c) 2010-2013 The MathJax Consortium
 *
 *  Part of the MathJax library.
 *  See http://www.mathjax.org for details.
 * 
 *  Licensed under the Apache License, Version 2.0;
 *  you may not use this file except in compliance with the License.
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 */

MathJax.OutputJax["HTML-CSS"].defineImageData({"MathJax_Fraktur-bold":{160:[[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0]],8216:[[2,2,-3],[2,3,-3],[2,4,-4],[3,4,-5],[3,5,-6],[4,6,-7],[4,6,-8],[5,7,-10],[6,9,-11],[7,11,-14],[8,12,-17],[9,14,-19],[11,17,-23],[13,20,-27]],8217:[[2,2,-2],[2,3,-3],[2,4,-3],[3,4,-5],[3,5,-5],[4,6,-6],[4,6,-7],[5,7,-9],[6,9,-10],[7,11,-12],[8,12,-16],[9,14,-18],[11,17,-21],[13,20,-25]]}});MathJax.Ajax.loadComplete(MathJax.OutputJax["HTML-CSS"].imgDir+"/Fraktur/Bold"+MathJax.OutputJax["HTML-CSS"].imgPacked+"/Other.js");

