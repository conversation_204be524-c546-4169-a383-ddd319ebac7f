/*
 *  /MathJax/fonts/HTML-CSS/TeX/png/AMS/Regular/Latin1Supplement.js
 *  
 *  Copyright (c) 2010-2013 The MathJax Consortium
 *
 *  Part of the MathJax library.
 *  See http://www.mathjax.org for details.
 * 
 *  Licensed under the Apache License, Version 2.0;
 *  you may not use this file except in compliance with the License.
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 */

MathJax.OutputJax["HTML-CSS"].defineImageData({MathJax_AMS:{160:[[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0]],165:[[6,5,0],[7,6,0],[8,7,0],[9,8,0],[11,9,0],[13,11,0],[15,13,0],[18,16,0],[21,19,0],[25,22,0],[29,27,0],[35,32,0],[41,38,0],[49,45,0]],174:[[7,6,1],[8,8,2],[9,9,2],[11,10,2],[13,12,2],[16,15,3],[18,17,3],[22,20,4],[26,24,5],[31,29,6],[36,34,7],[43,41,8],[51,49,10],[61,59,12]],240:[[4,5,0],[5,6,0],[5,8,0],[6,9,0],[8,11,0],[9,13,0],[10,15,0],[12,18,0],[15,22,2],[17,25,1],[20,30,1],[24,36,1],[29,43,1],[34,51,1]]}});MathJax.Ajax.loadComplete(MathJax.OutputJax["HTML-CSS"].imgDir+"/AMS/Regular"+MathJax.OutputJax["HTML-CSS"].imgPacked+"/Latin1Supplement.js");

