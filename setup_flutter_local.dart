// Elite Quiz Flutter Local Development Configuration
// Run this script to configure Flutter app for local development

import 'dart:io';

void main() async {
  print('🚀 Setting up Elite Quiz Flutter App for Local Development');
  print('=' * 60);
  
  await setupLocalConfig();
  await createNetworkSecurityConfig();
  await updateAndroidManifest();
  await createLocalConfigFile();
  
  print('\n' + '=' * 60);
  print('🎉 Flutter app setup completed successfully!');
  print('\n📋 Next Steps:');
  print('1. Run: flutter pub get');
  print('2. Run: dart run flutter_launcher_icons');
  print('3. Add Firebase configuration files:');
  print('   - google-services.json → android/app/');
  print('   - GoogleService-Info.plist → ios/Runner/');
  print('4. Update your local server IP in config if using physical device');
  print('5. Run: flutter run');
  
  print('\n🔧 Configuration Details:');
  print('- Local API: http://********/elite_quiz_admin/Api (Android Emulator)');
  print('- Local API: http://localhost/elite_quiz_admin/Api (iOS Simulator)');
  print('- For physical device: Update IP to your computer\'s local IP');
  
  print('\n⚠️  Important Notes:');
  print('- Make sure local server is running');
  print('- Firebase project must be configured');
  print('- Network security config allows HTTP for local development');
}

Future<void> setupLocalConfig() async {
  print('\n📝 Creating local configuration...');
  
  const configContent = '''
// Local Development Configuration
// This file contains configuration for local development

const String panelUrl = 'http://********/elite_quiz_admin'; // Android Emulator
// const String panelUrl = 'http://localhost/elite_quiz_admin'; // iOS Simulator  
// const String panelUrl = 'http://*************/elite_quiz_admin'; // Physical Device (update IP)

const String kAppName = 'Elite Quiz Local';
const bool enableDebugMode = true;
const bool useLocalServer = true;

// API Endpoints
const String apiBaseUrl = '\$panelUrl/Api';

// Local Development Settings
const bool enableNetworkLogs = true;
const bool skipSslVerification = true;
const int apiTimeout = 30; // seconds

// Firebase Configuration (update with your project details)
class FirebaseConfig {
  static const String apiKey = 'your_firebase_api_key';
  static const String authDomain = 'your_project.firebaseapp.com';
  static const String projectId = 'your_project_id';
  static const String storageBucket = 'your_project.appspot.com';
  static const String messagingSenderId = 'your_sender_id';
  static const String appId = 'your_app_id';
}

// Development Helper Functions
class DevConfig {
  static String getApiUrl() {
    if (useLocalServer) {
      return apiBaseUrl;
    }
    return 'https://your-production-api.com/Api';
  }
  
  static bool isLocalDevelopment() {
    return useLocalServer && enableDebugMode;
  }
  
  static void printApiCall(String endpoint, Map<String, dynamic> data) {
    if (enableNetworkLogs && enableDebugMode) {
      print('🌐 API Call: \$endpoint');
      print('📤 Data: \$data');
    }
  }
}
''';

  final configFile = File('elite_quiz_app-2.3.4/lib/core/config/local_config.dart');
  await configFile.create(recursive: true);
  await configFile.writeAsString(configContent);
  
  print('✅ Local config created: lib/core/config/local_config.dart');
}

Future<void> createNetworkSecurityConfig() async {
  print('\n🔒 Creating network security configuration...');
  
  const networkSecurityContent = '''<?xml version="1.0" encoding="utf-8"?>
<network-security-config>
    <domain-config cleartextTrafficPermitted="true">
        <domain includeSubdomains="true">********</domain>
        <domain includeSubdomains="true">localhost</domain>
        <domain includeSubdomains="true">127.0.0.1</domain>
        <domain includeSubdomains="true">*************</domain>
        <domain includeSubdomains="true">*************</domain>
        <domain includeSubdomains="true">*************</domain>
        <domain includeSubdomains="true">*************</domain>
        <domain includeSubdomains="true">*************</domain>
        <domain includeSubdomains="true">*************</domain>
    </domain-config>
</network-security-config>''';

  final networkConfigDir = Directory('elite_quiz_app-2.3.4/android/app/src/main/res/xml');
  await networkConfigDir.create(recursive: true);
  
  final networkConfigFile = File('${networkConfigDir.path}/network_security_config.xml');
  await networkConfigFile.writeAsString(networkSecurityContent);
  
  print('✅ Network security config created: android/app/src/main/res/xml/network_security_config.xml');
}

Future<void> updateAndroidManifest() async {
  print('\n📱 Updating Android manifest...');
  
  final manifestFile = File('elite_quiz_app-2.3.4/android/app/src/main/AndroidManifest.xml');
  
  if (await manifestFile.exists()) {
    String content = await manifestFile.readAsString();
    
    // Add network security config and cleartext traffic permission
    if (!content.contains('android:networkSecurityConfig')) {
      content = content.replaceAll(
        '<application',
        '<application\n        android:usesCleartextTraffic="true"\n        android:networkSecurityConfig="@xml/network_security_config"'
      );
      
      await manifestFile.writeAsString(content);
      print('✅ Android manifest updated with network security config');
    } else {
      print('ℹ️  Android manifest already configured');
    }
  } else {
    print('⚠️  Android manifest not found, please update manually');
  }
}

Future<void> createLocalConfigFile() async {
  print('\n⚙️  Creating main config update...');
  
  const configUpdateContent = '''
// Update your existing lib/core/config/config.dart file with this content
// Or import local_config.dart and use those values

import 'local_config.dart' as local;

// Use local configuration when in development mode
const String panelUrl = local.useLocalServer ? local.panelUrl : 'https://your-production-url.com';
const String kAppName = local.useLocalServer ? local.kAppName : 'Elite Quiz';

// API Configuration
class ApiConfig {
  static String get baseUrl => local.DevConfig.getApiUrl();
  static bool get isLocal => local.DevConfig.isLocalDevelopment();
  static int get timeout => local.apiTimeout;
}

// Network Helper
class NetworkHelper {
  static void logApiCall(String endpoint, Map<String, dynamic> data) {
    local.DevConfig.printApiCall(endpoint, data);
  }
}
''';

  final configUpdateFile = File('config_update_instructions.dart');
  await configUpdateFile.writeAsString(configUpdateContent);
  
  print('✅ Config update instructions created: config_update_instructions.dart');
}
