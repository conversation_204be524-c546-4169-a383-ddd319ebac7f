/*************************************************************
 *
 *  MathJax/fonts/HTML-CSS/TeX/png/SansSerif/Regular/BasicLatin.js
 *  
 *  Defines the image size data needed for the HTML-CSS OutputJax
 *  to display mathematics using fallback images when the fonts
 *  are not available to the client browser.
 *
 *  ---------------------------------------------------------------------
 *
 *  Copyright (c) 2009-2013 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 *
 */

MathJax.OutputJax["HTML-CSS"].defineImageData({
  "MathJax_SansSerif": {
    0x20: [  // SPACE
      [1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],
      [1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0]
    ],
    0x21: [  // EXCLAMATION MARK
      [2,5,0],[2,6,0],[3,8,0],[3,9,0],[3,9,0],[4,12,0],[5,14,0],[5,17,0],
      [6,20,0],[7,23,0],[9,27,0],[10,32,0],[12,39,0],[14,45,0]
    ],
    0x22: [  // QUOTATION MARK
      [3,2,-3],[3,2,-4],[4,3,-5],[4,3,-6],[5,3,-6],[6,4,-8],[7,5,-9],[8,6,-11],
      [9,7,-13],[11,8,-15],[13,9,-18],[16,10,-22],[18,13,-26],[22,15,-30]
    ],
    0x23: [  // NUMBER SIGN
      [6,6,1],[7,8,2],[8,10,2],[10,12,3],[11,12,3],[13,15,3],[16,18,4],[19,22,5],
      [22,25,5],[26,29,6],[31,35,8],[37,41,9],[43,50,11],[52,58,13]
    ],
    0x24: [  // DOLLAR SIGN
      [4,6,0],[4,8,1],[5,9,0],[6,11,1],[7,11,1],[8,15,2],[9,16,1],[11,20,2],
      [13,23,2],[15,27,2],[18,32,3],[21,37,2],[25,46,4],[30,53,4]
    ],
    0x25: [  // PERCENT SIGN
      [6,5,0],[7,7,0],[8,9,0],[10,10,0],[11,10,0],[13,16,1],[16,17,1],[19,19,1],
      [22,23,2],[26,27,2],[31,32,2],[37,37,3],[43,46,3],[52,53,4]
    ],
    0x26: [  // AMPERSAND
      [5,5,0],[6,6,0],[7,8,0],[9,9,0],[10,9,0],[12,12,0],[14,14,0],[17,19,1],
      [20,22,1],[23,25,1],[28,27,0],[33,34,1],[39,43,2],[46,49,2]
    ],
    0x27: [  // APOSTROPHE
      [2,2,-3],[2,2,-4],[2,3,-5],[3,3,-6],[3,3,-6],[4,4,-8],[4,5,-9],[5,6,-11],
      [6,7,-13],[7,8,-15],[8,9,-18],[9,10,-22],[11,13,-26],[13,15,-30]
    ],
    0x28: [  // LEFT PARENTHESIS
      [3,7,2],[3,9,2],[4,11,3],[4,14,3],[5,14,3],[6,18,4],[7,20,5],[8,24,6],
      [10,29,7],[11,33,8],[14,40,10],[16,46,11],[19,57,14],[22,66,16]
    ],
    0x29: [  // RIGHT PARENTHESIS
      [3,7,2],[3,9,2],[4,11,3],[4,14,3],[5,14,3],[6,18,4],[7,20,5],[8,24,6],
      [9,29,7],[11,33,8],[13,40,10],[15,46,11],[18,57,14],[21,66,16]
    ],
    0x2A: [  // ASTERISK
      [4,3,-2],[4,4,-3],[5,5,-3],[6,6,-4],[7,6,-4],[8,8,-5],[9,9,-6],[11,11,-7],
      [13,13,-9],[15,15,-10],[18,18,-12],[21,21,-14],[25,26,-17],[29,30,-20]
    ],
    0x2B: [  // PLUS SIGN
      [5,5,1],[6,6,1],[8,8,1],[9,9,1],[10,9,1],[12,12,1],[14,14,2],[17,16,2],
      [20,19,2],[24,22,3],[28,27,3],[34,31,4],[40,38,5],[48,44,5]
    ],
    0x2C: [  // COMMA
      [2,2,1],[2,3,2],[2,4,2],[3,4,2],[3,4,2],[4,4,2],[4,5,3],[5,6,3],
      [6,7,4],[7,8,4],[8,9,5],[9,11,6],[11,13,7],[13,15,8]
    ],
    0x2D: [  // HYPHEN-MINUS
      [2,1,-1],[3,1,-1],[3,1,-2],[4,1,-2],[4,1,-2],[5,3,-2],[6,3,-3],[7,2,-4],
      [8,3,-5],[10,3,-6],[11,3,-7],[13,4,-9],[16,5,-11],[19,5,-12]
    ],
    0x2E: [  // FULL STOP
      [2,1,0],[2,1,0],[2,2,0],[3,2,0],[3,2,0],[4,2,0],[4,2,0],[5,3,0],
      [6,3,0],[7,4,0],[8,4,0],[9,5,0],[11,6,0],[13,7,0]
    ],
    0x2F: [  // SOLIDUS
      [4,7,2],[4,9,2],[5,11,3],[6,13,3],[7,13,3],[8,18,4],[9,20,5],[11,24,6],
      [13,29,7],[15,33,8],[18,39,10],[21,46,11],[25,57,14],[30,66,16]
    ],
    0x30: [  // DIGIT ZERO
      [4,5,0],[4,6,0],[5,8,0],[6,9,0],[7,9,0],[8,12,0],[9,14,0],[11,18,1],
      [13,21,1],[16,24,1],[18,28,0],[22,32,1],[26,40,1],[31,46,1]
    ],
    0x31: [  // DIGIT ONE
      [3,5,0],[4,6,0],[5,8,0],[6,9,0],[6,9,0],[8,12,0],[9,14,0],[10,17,0],
      [12,20,0],[15,22,0],[17,27,0],[20,31,0],[24,39,0],[29,45,0]
    ],
    0x32: [  // DIGIT TWO
      [4,5,0],[4,6,0],[5,8,0],[6,9,0],[7,9,0],[8,12,0],[9,14,0],[11,17,0],
      [13,20,0],[15,22,0],[18,27,0],[21,31,0],[25,39,0],[30,45,0]
    ],
    0x33: [  // DIGIT THREE
      [4,5,0],[4,6,0],[5,8,0],[6,9,0],[7,9,0],[8,12,0],[9,14,0],[11,18,1],
      [13,21,1],[16,24,1],[18,27,0],[22,32,1],[26,40,1],[31,46,1]
    ],
    0x34: [  // DIGIT FOUR
      [4,5,0],[4,6,0],[5,8,0],[6,9,0],[7,9,0],[8,12,0],[10,13,0],[11,16,0],
      [14,19,0],[16,22,0],[19,26,0],[22,30,0],[27,38,0],[31,43,0]
    ],
    0x35: [  // DIGIT FIVE
      [4,5,0],[4,6,0],[5,7,0],[6,9,0],[7,9,0],[8,13,0],[9,14,0],[11,17,1],
      [13,20,1],[15,23,1],[18,27,0],[21,31,0],[25,39,1],[30,45,1]
    ],
    0x36: [  // DIGIT SIX
      [4,5,0],[4,6,0],[5,8,0],[6,9,0],[7,9,0],[8,12,0],[9,14,0],[11,18,1],
      [13,20,1],[16,24,1],[18,27,0],[22,32,2],[26,40,1],[31,46,1]
    ],
    0x37: [  // DIGIT SEVEN
      [4,5,0],[4,6,0],[5,7,0],[6,9,0],[7,9,0],[8,12,0],[9,13,0],[11,16,0],
      [13,19,0],[16,22,0],[18,26,0],[22,31,1],[26,38,1],[31,44,1]
    ],
    0x38: [  // DIGIT EIGHT
      [4,5,0],[4,6,0],[5,8,0],[6,9,0],[7,9,0],[8,12,0],[9,14,0],[11,18,1],
      [13,21,1],[15,24,1],[18,28,0],[22,32,1],[26,40,1],[30,46,1]
    ],
    0x39: [  // DIGIT NINE
      [4,5,0],[4,6,0],[5,8,0],[6,9,0],[7,9,0],[8,12,0],[9,14,0],[11,18,1],
      [13,20,1],[16,24,1],[18,27,0],[22,32,1],[26,40,1],[31,46,1]
    ],
    0x3A: [  // COLON
      [2,3,0],[2,4,0],[2,5,0],[3,6,0],[3,6,0],[4,8,0],[4,9,0],[5,11,0],
      [6,13,0],[7,15,0],[8,17,0],[9,20,0],[11,25,0],[13,29,0]
    ],
    0x3B: [  // SEMICOLON
      [2,4,1],[2,6,2],[2,7,2],[3,8,2],[3,8,2],[4,10,2],[4,12,3],[5,14,3],
      [6,17,4],[7,19,4],[8,22,5],[9,26,6],[11,32,7],[13,37,8]
    ],
    0x3D: [  // EQUALS SIGN
      [5,2,0],[6,3,-1],[8,3,-1],[9,4,-1],[10,4,-1],[12,4,-2],[15,5,-2],[17,6,-3],
      [20,8,-3],[24,10,-3],[29,10,-5],[34,11,-6],[40,14,-7],[48,16,-9]
    ],
    0x3F: [  // QUESTION MARK
      [3,5,0],[4,6,0],[5,8,0],[5,9,0],[6,9,0],[7,12,0],[9,14,0],[10,17,0],
      [12,20,0],[14,23,0],[17,27,0],[20,33,0],[23,40,0],[28,46,0]
    ],
    0x40: [  // COMMERCIAL AT
      [5,5,0],[6,6,0],[6,8,0],[8,9,0],[9,9,0],[11,12,0],[12,14,0],[15,17,0],
      [17,20,0],[21,23,0],[24,27,0],[29,33,0],[34,41,1],[41,47,1]
    ],
    0x41: [  // LATIN CAPITAL LETTER A
      [5,5,0],[6,6,0],[7,8,0],[8,9,0],[9,9,0],[11,12,0],[13,14,0],[15,17,0],
      [18,20,0],[21,23,0],[25,27,0],[30,32,0],[36,39,0],[42,45,0]
    ],
    0x42: [  // LATIN CAPITAL LETTER B
      [5,5,0],[6,8,1],[6,9,1],[8,11,1],[9,11,1],[11,14,1],[12,16,1],[15,19,1],
      [17,22,1],[21,25,1],[24,29,1],[29,34,1],[34,41,1],[41,47,1]
    ],
    0x43: [  // LATIN CAPITAL LETTER C
      [5,5,0],[5,6,0],[6,8,0],[7,9,0],[9,9,0],[10,12,0],[12,14,0],[14,17,0],
      [17,20,0],[20,23,0],[23,27,0],[28,33,0],[33,41,1],[39,47,1]
    ],
    0x44: [  // LATIN CAPITAL LETTER D
      [5,5,0],[6,6,0],[7,8,0],[8,9,0],[10,9,0],[12,12,0],[13,14,0],[16,17,0],
      [19,20,0],[22,23,0],[27,27,0],[31,32,0],[37,39,0],[44,45,0]
    ],
    0x45: [  // LATIN CAPITAL LETTER E
      [4,5,0],[5,6,0],[6,8,0],[7,9,0],[8,9,0],[10,12,0],[11,14,0],[13,17,0],
      [16,20,0],[19,23,0],[22,27,0],[26,32,0],[31,39,0],[37,45,0]
    ],
    0x46: [  // LATIN CAPITAL LETTER F
      [4,5,0],[5,6,0],[6,8,0],[7,9,0],[8,9,0],[9,12,0],[11,14,0],[13,17,0],
      [15,20,0],[18,23,0],[21,27,0],[25,32,0],[30,39,0],[35,45,0]
    ],
    0x47: [  // LATIN CAPITAL LETTER G
      [5,5,0],[5,6,0],[6,8,0],[8,9,0],[9,9,0],[10,12,0],[12,14,0],[14,17,0],
      [17,20,0],[20,23,0],[24,27,0],[28,33,0],[34,41,1],[40,47,1]
    ],
    0x48: [  // LATIN CAPITAL LETTER H
      [5,5,0],[6,6,0],[7,8,0],[8,9,0],[9,9,0],[11,12,0],[13,14,0],[15,17,0],
      [18,20,0],[21,23,0],[25,27,0],[29,32,0],[35,39,0],[41,45,0]
    ],
    0x49: [  // LATIN CAPITAL LETTER I
      [2,5,0],[2,6,0],[2,8,0],[3,9,0],[3,9,0],[4,12,0],[4,14,0],[5,17,0],
      [6,20,0],[7,23,0],[8,27,0],[9,32,0],[11,39,0],[13,45,0]
    ],
    0x4A: [  // LATIN CAPITAL LETTER J
      [3,5,0],[4,6,0],[4,8,0],[5,9,0],[6,9,0],[7,12,0],[8,14,0],[9,18,1],
      [11,21,1],[13,24,1],[16,27,0],[18,33,1],[22,41,2],[26,47,2]
    ],
    0x4B: [  // LATIN CAPITAL LETTER K
      [5,5,0],[6,6,0],[7,8,0],[8,9,0],[9,9,0],[11,12,0],[13,14,0],[16,17,0],
      [18,20,0],[22,23,0],[26,27,0],[31,32,0],[36,39,0],[43,45,0]
    ],
    0x4C: [  // LATIN CAPITAL LETTER L
      [4,5,0],[5,6,0],[5,8,0],[6,9,0],[7,9,0],[9,12,0],[10,14,0],[12,17,0],
      [14,20,0],[17,23,0],[20,27,0],[24,32,0],[28,39,0],[33,45,0]
    ],
    0x4D: [  // LATIN CAPITAL LETTER M
      [6,5,0],[7,6,0],[8,8,0],[10,9,0],[11,9,0],[13,12,0],[16,14,0],[19,17,0],
      [22,20,0],[26,23,0],[31,27,0],[37,32,0],[44,39,0],[52,45,0]
    ],
    0x4E: [  // LATIN CAPITAL LETTER N
      [5,5,0],[6,6,0],[7,8,0],[8,9,0],[9,9,0],[11,12,0],[13,14,0],[15,17,0],
      [18,20,0],[21,23,0],[25,27,0],[29,32,0],[35,39,0],[41,45,0]
    ],
    0x4F: [  // LATIN CAPITAL LETTER O
      [5,5,0],[6,6,0],[7,8,0],[8,9,0],[10,9,0],[12,12,0],[14,14,0],[16,19,1],
      [19,22,1],[23,25,1],[27,27,0],[32,34,1],[38,42,1],[45,49,1]
    ],
    0x50: [  // LATIN CAPITAL LETTER P
      [5,5,0],[5,6,0],[6,8,0],[7,9,0],[9,9,0],[10,12,0],[12,14,0],[14,17,0],
      [17,20,0],[20,23,0],[23,27,0],[28,32,0],[33,39,0],[39,45,0]
    ],
    0x51: [  // LATIN CAPITAL LETTER Q
      [5,6,1],[6,7,1],[7,9,1],[8,11,2],[10,11,2],[12,14,2],[14,16,2],[16,21,3],
      [19,25,4],[23,28,4],[27,31,4],[32,40,7],[38,48,7],[45,56,8]
    ],
    0x52: [  // LATIN CAPITAL LETTER R
      [5,5,0],[6,6,0],[7,8,0],[8,9,0],[9,9,0],[11,12,0],[13,14,0],[15,17,0],
      [18,20,0],[21,23,0],[25,27,0],[29,32,0],[35,39,0],[41,45,0]
    ],
    0x53: [  // LATIN CAPITAL LETTER S
      [4,5,0],[5,6,0],[5,8,0],[6,9,0],[7,9,0],[9,12,0],[10,14,0],[12,19,1],
      [14,22,1],[17,25,1],[20,27,0],[24,34,1],[28,42,1],[33,49,1]
    ],
    0x54: [  // LATIN CAPITAL LETTER T
      [5,5,0],[6,6,0],[7,8,0],[8,9,0],[9,9,0],[11,12,0],[13,14,0],[15,17,0],
      [18,20,0],[22,23,0],[26,27,0],[30,32,0],[36,39,0],[43,45,0]
    ],
    0x55: [  // LATIN CAPITAL LETTER U
      [5,5,0],[5,6,0],[6,8,0],[8,9,0],[9,9,0],[10,12,0],[12,14,0],[14,18,1],
      [17,21,1],[20,24,1],[24,27,0],[28,33,1],[34,41,2],[40,47,2]
    ],
    0x56: [  // LATIN CAPITAL LETTER V
      [5,5,0],[6,6,0],[7,8,0],[8,9,0],[10,9,0],[11,12,0],[13,14,0],[16,17,0],
      [19,20,0],[22,23,0],[26,27,0],[31,32,0],[37,39,0],[43,45,0]
    ],
    0x57: [  // LATIN CAPITAL LETTER W
      [7,5,0],[8,6,0],[10,8,0],[11,9,0],[13,9,0],[16,12,0],[19,14,0],[22,17,0],
      [26,20,0],[31,23,0],[37,27,0],[44,32,0],[52,39,0],[62,45,0]
    ],
    0x58: [  // LATIN CAPITAL LETTER X
      [5,5,0],[6,6,0],[7,8,0],[8,9,0],[10,9,0],[11,12,0],[13,14,0],[16,17,0],
      [19,20,0],[22,23,0],[26,27,0],[31,32,0],[37,39,0],[43,45,0]
    ],
    0x59: [  // LATIN CAPITAL LETTER Y
      [5,5,0],[6,6,0],[7,8,0],[8,9,0],[10,9,0],[11,12,0],[13,14,0],[16,17,0],
      [19,20,0],[22,23,0],[26,27,0],[31,32,0],[37,39,0],[44,45,0]
    ],
    0x5A: [  // LATIN CAPITAL LETTER Z
      [4,5,0],[5,6,0],[6,8,0],[7,9,0],[8,9,0],[10,12,0],[11,14,0],[13,17,0],
      [16,20,0],[19,23,0],[22,27,0],[27,32,0],[31,39,0],[37,45,0]
    ],
    0x5B: [  // LEFT SQUARE BRACKET
      [2,7,2],[3,9,2],[3,11,3],[4,13,3],[4,13,3],[5,19,5],[6,20,5],[7,24,6],
      [8,29,7],[9,33,8],[11,39,10],[13,46,11],[15,57,14],[18,65,16]
    ],
    0x5D: [  // RIGHT SQUARE BRACKET
      [2,7,2],[2,9,2],[2,11,3],[3,13,3],[3,13,3],[4,19,5],[4,20,5],[5,24,6],
      [6,29,7],[7,33,8],[8,39,10],[10,46,11],[11,57,14],[13,65,16]
    ],
    0x5E: [  // CIRCUMFLEX ACCENT
      [3,1,-4],[4,2,-4],[5,2,-6],[5,2,-7],[6,2,-7],[7,3,-9],[9,4,-10],[10,4,-13],
      [12,5,-15],[14,6,-17],[17,7,-20],[20,8,-24],[24,10,-29],[28,11,-34]
    ],
    0x5F: [  // LOW LINE
      [4,2,1],[5,2,1],[5,2,1],[6,2,1],[7,2,1],[9,4,3],[10,4,3],[12,4,3],
      [14,4,3],[17,5,4],[20,5,4],[24,6,5],[28,7,6],[33,8,7]
    ],
    0x61: [  // LATIN SMALL LETTER A
      [3,3,0],[4,4,0],[4,5,0],[5,6,0],[6,6,0],[7,8,0],[8,9,0],[10,11,0],
      [12,13,0],[14,15,0],[16,18,0],[19,21,0],[23,27,1],[27,31,1]
    ],
    0x62: [  // LATIN SMALL LETTER B
      [4,5,0],[4,6,0],[5,8,0],[6,9,0],[7,9,0],[8,12,0],[10,14,0],[12,17,0],
      [14,20,0],[16,23,0],[19,27,0],[23,32,0],[27,40,1],[32,46,1]
    ],
    0x63: [  // LATIN SMALL LETTER C
      [3,3,0],[4,4,0],[5,5,0],[5,6,0],[6,6,0],[7,8,0],[9,9,0],[10,11,0],
      [12,13,0],[14,15,0],[17,18,0],[20,21,0],[23,27,1],[28,32,1]
    ],
    0x64: [  // LATIN SMALL LETTER D
      [4,5,0],[4,6,0],[5,8,0],[6,9,0],[7,9,0],[8,12,0],[9,14,0],[11,17,0],
      [13,20,0],[15,23,0],[18,27,0],[21,32,0],[25,40,1],[30,46,1]
    ],
    0x65: [  // LATIN SMALL LETTER E
      [3,3,0],[4,4,0],[5,5,0],[5,6,0],[6,6,0],[7,8,0],[9,9,0],[10,11,0],
      [12,13,0],[14,15,0],[17,18,0],[20,21,0],[23,28,1],[28,31,1]
    ],
    0x66: [  // LATIN SMALL LETTER F
      [3,5,0],[3,6,0],[4,8,0],[5,9,0],[5,9,0],[6,12,0],[7,14,0],[9,17,0],
      [10,20,0],[12,23,0],[14,27,0],[17,33,0],[20,40,0],[23,46,0]
    ],
    0x67: [  // LATIN SMALL LETTER G
      [4,4,1],[5,6,2],[5,7,2],[6,9,3],[7,9,3],[9,11,3],[10,13,4],[12,16,5],
      [14,18,5],[16,21,6],[19,25,8],[23,30,10],[27,38,12],[32,44,14]
    ],
    0x68: [  // LATIN SMALL LETTER H
      [4,5,0],[4,6,0],[5,8,0],[6,9,0],[7,9,0],[8,12,0],[9,14,0],[11,17,0],
      [13,20,0],[15,23,0],[18,27,0],[21,32,0],[25,39,0],[29,45,0]
    ],
    0x69: [  // LATIN SMALL LETTER I
      [2,5,0],[2,6,0],[2,8,0],[3,9,0],[3,9,0],[3,12,0],[4,14,0],[4,17,0],
      [5,20,0],[6,23,0],[7,27,0],[8,31,0],[10,39,0],[12,45,0]
    ],
    0x6A: [  // LATIN SMALL LETTER J
      [3,6,1],[3,8,2],[3,10,2],[4,12,3],[4,12,3],[5,15,3],[6,18,4],[7,22,5],
      [8,25,5],[9,29,6],[11,35,8],[13,41,10],[15,51,12],[17,58,14]
    ],
    0x6B: [  // LATIN SMALL LETTER K
      [4,5,0],[4,6,0],[5,8,0],[6,9,0],[7,9,0],[8,12,0],[10,14,0],[11,17,0],
      [14,20,0],[16,23,0],[19,27,0],[22,32,0],[27,39,0],[31,45,0]
    ],
    0x6C: [  // LATIN SMALL LETTER L
      [2,5,0],[2,6,0],[2,8,0],[2,9,0],[3,9,0],[3,12,0],[4,14,0],[4,17,0],
      [5,20,0],[6,23,0],[7,27,0],[8,32,0],[10,39,0],[11,45,0]
    ],
    0x6D: [  // LATIN SMALL LETTER M
      [5,3,0],[6,4,0],[7,5,0],[9,6,0],[10,6,0],[12,8,0],[14,9,0],[17,11,0],
      [20,13,0],[24,15,0],[28,17,0],[34,20,0],[40,27,1],[47,31,1]
    ],
    0x6E: [  // LATIN SMALL LETTER N
      [4,3,0],[4,4,0],[5,5,0],[6,6,0],[7,6,0],[8,8,0],[9,9,0],[11,11,0],
      [13,13,0],[15,15,0],[18,17,0],[21,20,0],[25,26,0],[29,30,0]
    ],
    0x6F: [  // LATIN SMALL LETTER O
      [4,3,0],[4,4,0],[5,5,0],[6,6,0],[7,6,0],[8,8,0],[10,9,0],[11,11,0],
      [14,13,0],[16,15,0],[19,18,0],[22,21,0],[27,28,1],[31,31,1]
    ],
    0x70: [  // LATIN SMALL LETTER P
      [4,4,1],[4,6,2],[5,7,2],[6,9,3],[7,9,3],[9,11,3],[10,13,4],[12,16,5],
      [14,18,5],[16,21,6],[19,26,8],[23,30,9],[27,37,11],[32,43,13]
    ],
    0x71: [  // LATIN SMALL LETTER Q
      [4,4,1],[4,6,2],[5,7,2],[6,9,3],[7,9,3],[8,11,3],[9,13,4],[11,16,5],
      [13,18,5],[15,21,6],[18,26,8],[21,30,9],[25,37,11],[30,43,13]
    ],
    0x72: [  // LATIN SMALL LETTER R
      [3,3,0],[3,4,0],[4,5,0],[4,6,0],[5,6,0],[6,8,0],[7,9,0],[8,11,0],
      [10,13,0],[11,15,0],[13,17,0],[16,21,0],[19,26,0],[22,30,0]
    ],
    0x73: [  // LATIN SMALL LETTER S
      [3,3,0],[3,4,0],[4,5,0],[5,6,0],[5,6,0],[6,8,0],[8,9,0],[9,11,0],
      [10,13,0],[12,15,0],[15,18,0],[17,21,0],[20,27,1],[24,31,1]
    ],
    0x74: [  // LATIN SMALL LETTER T
      [3,4,0],[3,5,0],[4,7,0],[4,8,0],[5,8,0],[6,10,0],[7,12,0],[8,14,0],
      [10,17,0],[11,19,0],[14,23,0],[16,27,0],[19,34,1],[22,39,1]
    ],
    0x75: [  // LATIN SMALL LETTER U
      [4,3,0],[4,4,0],[5,5,0],[6,6,0],[7,6,0],[8,8,0],[9,9,0],[11,11,0],
      [13,13,0],[15,15,0],[18,17,0],[21,20,0],[25,27,1],[30,31,1]
    ],
    0x76: [  // LATIN SMALL LETTER V
      [4,3,0],[4,4,0],[5,5,0],[6,6,0],[7,6,0],[8,8,0],[9,9,0],[11,11,0],
      [13,13,0],[15,15,0],[18,17,0],[21,20,0],[25,25,0],[30,29,0]
    ],
    0x77: [  // LATIN SMALL LETTER W
      [5,3,0],[6,4,0],[7,5,0],[8,6,0],[10,6,0],[12,8,0],[14,9,0],[16,11,0],
      [19,13,0],[22,15,0],[27,17,0],[32,20,0],[37,25,0],[44,29,0]
    ],
    0x78: [  // LATIN SMALL LETTER X
      [4,3,0],[4,4,0],[5,5,0],[6,6,0],[7,6,0],[8,8,0],[9,9,0],[11,11,0],
      [13,13,0],[16,15,0],[18,17,0],[22,20,0],[26,26,0],[31,30,0]
    ],
    0x79: [  // LATIN SMALL LETTER Y
      [4,4,1],[4,6,2],[5,7,2],[6,9,3],[7,9,3],[8,11,3],[9,13,4],[11,16,5],
      [13,18,5],[15,21,6],[18,25,8],[21,30,10],[25,37,12],[30,43,14]
    ],
    0x7A: [  // LATIN SMALL LETTER Z
      [3,3,0],[4,4,0],[4,5,0],[5,6,0],[6,6,0],[7,8,0],[8,9,0],[10,11,0],
      [12,13,0],[14,15,0],[16,17,0],[19,20,0],[23,26,0],[27,29,0]
    ],
    0x7E: [  // TILDE
      [3,1,-1],[4,1,-2],[5,2,-2],[5,2,-3],[6,2,-3],[7,4,-3],[9,3,-4],[10,3,-5],
      [12,4,-5],[14,5,-6],[17,6,-8],[20,6,-9],[23,8,-11],[28,9,-13]
    ]
  }
});

MathJax.Ajax.loadComplete(MathJax.OutputJax["HTML-CSS"].imgDir+"/SansSerif/Regular"+
                          MathJax.OutputJax["HTML-CSS"].imgPacked+"/BasicLatin.js");
