/*
 *  /MathJax/jax/output/HTML-CSS/fonts/Gyre-Pagella/Size1/Regular/Main.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.OutputJax["HTML-CSS"].FONTDATA.FONTS.GyrePagellaMathJax_Size1={directory:"Size1/Regular",family:"GyrePagellaMathJax_Size1",testString:"\u00A0\u0302\u0303\u0305\u0306\u030C\u0311\u032C\u032D\u032E\u032F\u0330\u0332\u0333\u033F",32:[0,0,250,0,0],40:[744,244,456,124,374],41:[744,244,456,82,332],47:[774,274,574,80,494],91:[750,250,428,124,346],92:[774,274,574,80,494],93:[750,250,428,82,304],123:[749,249,441,82,359],124:[730,230,210,80,130],125:[749,249,441,82,359],160:[0,0,250,0,0],770:[712,-544,613,0,613],771:[700,-544,608,0,608],773:[646,-598,500,0,500],774:[708,-553,631,0,631],780:[710,-542,613,0,613],785:[720,-564,631,0,631],812:[-60,228,613,0,613],813:[-70,238,613,0,613],814:[-60,216,631,0,631],815:[-78,234,631,0,631],816:[-78,234,608,0,608],818:[-60,108,500,0,500],819:[-60,216,500,0,500],831:[754,-598,500,0,500],8214:[730,230,380,80,300],8260:[774,274,574,80,494],8400:[784,-640,670,80,590],8401:[784,-640,670,80,590],8406:[784,-544,670,80,590],8407:[784,-544,670,80,590],8417:[784,-544,742,80,662],8425:[772,-647,740,0,740],8428:[-170,314,670,80,590],8429:[-170,314,670,80,590],8430:[-74,314,670,80,590],8431:[-74,314,670,80,590],8512:[971,471,1326,80,1246],8592:[400,-100,1370,80,1290],8593:[860,350,460,80,380],8594:[400,-100,1370,80,1290],8595:[850,360,460,80,380],8596:[400,-100,1455,80,1375],8597:[898,397,460,80,380],8598:[681,195,1037,80,957],8599:[681,195,1037,80,957],8600:[695,181,1037,80,957],8601:[695,181,1037,80,957],8602:[400,-100,1370,80,1290],8603:[400,-100,1370,80,1290],8606:[400,-100,1445,80,1365],8607:[898,387,460,80,380],8608:[400,-100,1445,80,1365],8609:[887,398,460,80,380],8610:[400,-100,1455,80,1375],8611:[400,-100,1455,80,1375],8612:[400,-100,1370,80,1290],8613:[860,350,460,80,380],8614:[400,-100,1370,80,1290],8615:[850,360,460,80,380],8617:[490,-100,1400,80,1320],8618:[490,-100,1400,80,1320],8619:[490,-40,1400,80,1320],8620:[490,-40,1400,80,1320],8621:[400,-100,1455,80,1375],8622:[400,-100,1455,80,1375],8624:[708,207,710,80,630],8625:[708,207,710,80,630],8626:[707,208,710,80,630],8627:[707,208,710,80,630],8630:[640,-240,1183,80,1103],8631:[640,-240,1183,80,1103],8636:[400,-220,1370,80,1290],8637:[280,-100,1370,80,1290],8638:[860,350,340,80,260],8639:[860,350,340,80,260],8640:[400,-220,1370,80,1290],8641:[280,-100,1370,80,1290],8642:[850,360,340,80,260],8643:[850,360,340,80,260],8644:[570,70,1380,80,1300],8645:[860,360,800,80,720],8646:[570,70,1380,80,1300],8647:[570,70,1370,80,1290],8648:[860,350,800,80,720],8649:[570,70,1370,80,1290],8650:[850,360,800,80,720],8651:[510,10,1379,80,1299],8652:[510,10,1379,80,1299],8653:[550,50,1370,80,1290],8654:[550,50,1455,80,1375],8655:[550,50,1370,80,1290],8656:[450,-50,1370,80,1290],8657:[860,350,560,80,480],8658:[450,-50,1370,80,1290],8659:[850,360,560,80,480],8660:[450,-50,1455,80,1375],8661:[898,397,560,80,480],8662:[681,259,1100,80,1020],8663:[681,259,1100,80,1020],8664:[759,181,1100,80,1020],8665:[759,181,1100,80,1020],8666:[525,25,1370,80,1290],8667:[525,25,1370,80,1290],8668:[400,-100,1370,80,1290],8669:[400,-100,1370,80,1290],8678:[450,-50,1497,80,1417],8679:[930,407,560,80,480],8680:[450,-50,1497,80,1417],8681:[907,430,560,80,480],8691:[930,430,560,80,480],8693:[860,360,800,80,720],8694:[740,240,1370,80,1290],8719:[971,471,1314,80,1234],8720:[971,471,1314,80,1234],8721:[971,471,1246,80,1166],8730:[840,310,703,120,733],8739:[730,230,210,80,130],8741:[730,230,380,80,300],8747:[1263,763,715,80,635],8748:[1263,763,1087,80,1007],8749:[1263,763,1459,80,1379],8750:[1263,763,772,80,692],8751:[1263,763,1144,80,1064],8752:[1263,763,1516,80,1436],8753:[1263,763,811,80,771],8754:[1263,763,820,80,780],8755:[1263,763,773,80,733],8866:[650,150,1360,80,1280],8867:[650,150,1360,80,1280],8868:[650,150,860,80,780],8869:[650,150,860,80,780],8896:[969,442,1000,80,920],8897:[942,469,1000,80,920],8898:[960,442,1000,80,920],8899:[942,460,1000,80,920],8968:[750,230,428,124,346],8969:[750,230,428,82,304],8970:[730,250,428,124,346],8971:[730,250,428,82,304],9001:[781,281,419,82,337],9002:[781,281,419,82,337],9140:[772,-647,740,0,740],9141:[-177,302,740,0,740],9180:[762,-603,1028,0,1028],9181:[-133,292,1028,0,1028],9182:[797,-590,1038,0,1038],9183:[-120,327,1038,0,1038],9184:[729,-529,1064,0,1064],9185:[-59,259,1064,0,1064],10145:[450,-50,1445,80,1365],10214:[750,250,444,124,362],10215:[750,250,444,82,320],10216:[781,281,419,82,337],10217:[781,281,419,82,337],10218:[781,281,654,82,572],10219:[781,281,654,82,572],10222:[744,244,350,124,268],10223:[744,244,350,82,226],10502:[450,-50,1445,80,1365],10503:[450,-50,1445,80,1365],10752:[844,344,1348,80,1268],10753:[844,344,1348,80,1268],10754:[844,344,1348,80,1268],10755:[942,460,1000,80,920],10756:[942,460,1000,80,920],10757:[960,424,1000,80,920],10758:[924,460,1000,80,920],10761:[737,237,1134,80,1054],10764:[1263,763,1831,80,1751],10769:[1263,763,811,80,771],11012:[450,-50,1519,80,1439],11013:[450,-50,1445,80,1365],11014:[898,387,560,80,480],11015:[887,398,560,80,480],11020:[450,-50,1455,80,1375],11021:[898,397,560,80,480],11057:[740,240,1370,80,1290]};MathJax.Callback.Queue(["initFont",MathJax.OutputJax["HTML-CSS"],"GyrePagellaMathJax_Size1"],["loadComplete",MathJax.Ajax,MathJax.OutputJax["HTML-CSS"].fontDir+"/Size1/Regular/Main.js"]);
