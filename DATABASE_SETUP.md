# Elite Quiz Database Setup Guide

## Quick Setup

### Option 1: Automatic Setup (Recommended)

**For Windows:**
```bash
# Run as Administrator
quick_setup.bat
```

**For macOS/Linux:**
```bash
# Make executable and run
chmod +x quick_setup.sh
sudo ./quick_setup.sh
```

### Option 2: Manual Setup

## Manual Database Setup

### Step 1: Start XAMPP Services

1. **Open XAMPP Control Panel**
2. **Start Apache and MySQL services**
3. **Verify services are running** (green indicators)

### Step 2: Create Database

1. **Open phpMyAdmin**
   - Go to: http://localhost/phpmyadmin
   - Login (usually no password required)

2. **Create New Database**
   - Click "New" in left sidebar
   - Database name: `elite_quiz_local`
   - Collation: `utf8mb4_unicode_ci`
   - Click "Create"

### Step 3: Import Database Schema

1. **Select Database**
   - Click on `elite_quiz_local` database

2. **Import SQL File**
   - Click "Import" tab
   - Choose file: `adminpanel2.3.4/install/assets/quiz.php`
   - Click "Go" to import

3. **Verify Import**
   - Check that tables are created successfully
   - Should see tables like: `tbl_users`, `tbl_category`, `tbl_question`, etc.

## Database Configuration

### Update Database Connection

Edit `adminpanel2.3.4/application/config/database.php`:

```php
$db['default'] = array(
    'dsn' => '',
    'hostname' => 'localhost',
    'username' => 'root',
    'password' => '',  // Usually empty for XAMPP
    'database' => 'elite_quiz_local',
    'dbdriver' => 'mysqli',
    'dbprefix' => '',
    'pconnect' => FALSE,
    'db_debug' => (ENVIRONMENT !== 'production'),
    'cache_on' => FALSE,
    'cachedir' => '',
    'char_set' => 'utf8mb4',
    'dbcollat' => 'utf8mb4_unicode_ci',
    'swap_pre' => '',
    'encrypt' => FALSE,
    'compress' => FALSE,
    'stricton' => FALSE,
    'failover' => array(),
    'save_queries' => TRUE
);
```

## Default Admin Account

After database import, you can login with:
- **Username:** `admin`
- **Password:** `admin123`

## Database Tables Overview

### Core Tables

1. **tbl_authenticate** - Admin users
2. **tbl_users** - App users
3. **tbl_category** - Quiz categories
4. **tbl_subcategory** - Sub-categories
5. **tbl_question** - Quiz questions
6. **tbl_settings** - System settings

### Feature Tables

1. **tbl_contest** - Contest management
2. **tbl_exam_module** - Exam system
3. **tbl_badges** - Achievement system
4. **tbl_leaderboard_daily** - Daily rankings
5. **tbl_leaderboard_monthly** - Monthly rankings
6. **tbl_users_statistics** - User performance
7. **tbl_bookmark** - Saved questions
8. **tbl_rooms** - Battle rooms

## Sample Data

The database comes with sample data including:

### Sample Categories
- General Knowledge
- Science
- History
- Sports
- Mathematics

### Sample Questions
- Multiple choice questions
- True/False questions
- Audio questions
- Math questions

### Sample Settings
- App configuration
- Coin rewards
- Badge settings
- System preferences

## Adding Custom Data

### Add Categories

```sql
INSERT INTO tbl_category (category_name, image, maxlevel, no_of_que, status) 
VALUES ('Technology', 'tech.png', 5, 10, 1);
```

### Add Questions

```sql
INSERT INTO tbl_question (category, question, optiona, optionb, optionc, optiond, answer, level) 
VALUES (1, 'What does HTML stand for?', 'Hyper Text Markup Language', 'High Tech Modern Language', 'Home Tool Markup Language', 'Hyperlink and Text Markup Language', 'a', 1);
```

### Add Admin User

```sql
INSERT INTO tbl_authenticate (auth_username, auth_pass, role, status) 
VALUES ('newadmin', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'admin', 1);
```

## Database Maintenance

### Backup Database

```bash
# Using mysqldump
mysqldump -u root elite_quiz_local > backup.sql

# Using phpMyAdmin
# Go to Export tab and download SQL file
```

### Restore Database

```bash
# Using mysql command
mysql -u root elite_quiz_local < backup.sql

# Using phpMyAdmin
# Go to Import tab and upload SQL file
```

### Reset Database

```sql
-- Clear all user data but keep structure
TRUNCATE TABLE tbl_users;
TRUNCATE TABLE tbl_leaderboard_daily;
TRUNCATE TABLE tbl_leaderboard_monthly;
TRUNCATE TABLE tbl_users_statistics;
TRUNCATE TABLE tbl_bookmark;
```

## Troubleshooting

### Common Issues

1. **Database Connection Failed**
   ```
   Error: Could not connect to database
   ```
   **Solution:**
   - Check MySQL service is running in XAMPP
   - Verify database name and credentials
   - Check if database exists

2. **Import Failed**
   ```
   Error: SQL syntax error
   ```
   **Solution:**
   - Check file encoding (should be UTF-8)
   - Ensure database is selected
   - Try importing in smaller chunks

3. **Permission Denied**
   ```
   Error: Access denied for user 'root'
   ```
   **Solution:**
   - Check MySQL user permissions
   - Reset MySQL root password if needed
   - Use correct username/password

4. **Table Already Exists**
   ```
   Error: Table 'tbl_users' already exists
   ```
   **Solution:**
   - Drop existing tables first
   - Or create new database with different name

### Reset MySQL Password

If you need to reset MySQL root password:

1. **Stop MySQL service in XAMPP**
2. **Open command prompt as Administrator**
3. **Navigate to XAMPP MySQL bin directory**
4. **Run MySQL without password:**
   ```bash
   mysqld --skip-grant-tables
   ```
5. **In another command prompt:**
   ```bash
   mysql -u root
   UPDATE mysql.user SET Password=PASSWORD('newpassword') WHERE User='root';
   FLUSH PRIVILEGES;
   ```

### Check Database Status

```sql
-- Check database size
SELECT 
    table_schema AS 'Database',
    ROUND(SUM(data_length + index_length) / 1024 / 1024, 2) AS 'Size (MB)'
FROM information_schema.tables 
WHERE table_schema = 'elite_quiz_local';

-- Check table row counts
SELECT 
    table_name AS 'Table',
    table_rows AS 'Rows'
FROM information_schema.tables 
WHERE table_schema = 'elite_quiz_local';

-- Check recent users
SELECT id, name, email, date_registered 
FROM tbl_users 
ORDER BY date_registered DESC 
LIMIT 10;
```

## Performance Optimization

### Add Indexes

```sql
-- Add indexes for better performance
ALTER TABLE tbl_question ADD INDEX idx_category (category);
ALTER TABLE tbl_question ADD INDEX idx_level (level);
ALTER TABLE tbl_users ADD INDEX idx_email (email);
ALTER TABLE tbl_leaderboard_daily ADD INDEX idx_date (date_created);
```

### Optimize Tables

```sql
-- Optimize all tables
OPTIMIZE TABLE tbl_users;
OPTIMIZE TABLE tbl_question;
OPTIMIZE TABLE tbl_category;
```

## Security Considerations

### For Production

1. **Change default admin password**
2. **Create database user with limited privileges**
3. **Enable MySQL password authentication**
4. **Use strong encryption keys**
5. **Regular database backups**
6. **Monitor database access logs**

### Create Limited User

```sql
-- Create user for application
CREATE USER 'quiz_app'@'localhost' IDENTIFIED BY 'strong_password';
GRANT SELECT, INSERT, UPDATE, DELETE ON elite_quiz_local.* TO 'quiz_app'@'localhost';
FLUSH PRIVILEGES;
```

Your database is now ready for local development! 🎉
