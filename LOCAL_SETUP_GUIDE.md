# Elite Quiz Local Development Setup Guide

## Prerequisites

### Required Software
- **XAMPP** (recommended) or WAMP/MAMP
- **Flutter SDK** (3.8.1 or higher)
- **Android Studio** or VS Code
- **Git**
- **Composer** (PHP dependency manager)
- **Node.js** (for Firebase CLI)

---

## Step 1: Setup Local Server Environment

### Install XAMPP

1. **Download XAMPP**
   - Go to https://www.apachefriends.org/
   - Download XAMPP for your operating system
   - Install with default settings

2. **Start Services**
   - Open XAMPP Control Panel
   - Start **Apache** and **MySQL** services
   - Verify Apache is running on http://localhost

3. **Configure PHP**
   - Open `xampp/php/php.ini`
   - Enable required extensions:
   ```ini
   extension=curl
   extension=fileinfo
   extension=gd
   extension=mbstring
   extension=openssl
   extension=pdo_mysql
   extension=zip
   ```
   - Restart Apache after changes

---

## Step 2: Setup Database

### Create Database

1. **Access phpMyAdmin**
   - Open http://localhost/phpmyadmin
   - Login (usually no password required for local)

2. **Create Database**
   ```sql
   CREATE DATABASE elite_quiz_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
   ```

3. **Import Database Schema**
   - Navigate to `adminpanel2.3.4/install/`
   - Look for SQL files or database dump
   - Import the schema into `elite_quiz_db`

### Sample Database Creation Script

If no SQL dump is available, create basic tables:

```sql
USE elite_quiz_db;

-- Admin authentication table
CREATE TABLE `tbl_authenticate` (
  `auth_id` int(11) NOT NULL AUTO_INCREMENT,
  `auth_username` varchar(50) NOT NULL DEFAULT 'admin',
  `auth_pass` varchar(255) NOT NULL,
  `role` varchar(32) NOT NULL DEFAULT 'admin',
  `permissions` text,
  `status` int(1) NOT NULL DEFAULT 1,
  `created` datetime DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`auth_id`)
);

-- Insert default admin user (password: admin123)
INSERT INTO `tbl_authenticate` (`auth_username`, `auth_pass`, `role`, `status`) 
VALUES ('admin', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'admin', 1);

-- Users table
CREATE TABLE `tbl_users` (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `firebase_id` longtext NOT NULL,
  `name` varchar(128) NOT NULL,
  `email` varchar(128) NOT NULL,
  `mobile` varchar(32) NOT NULL,
  `type` varchar(16) NOT NULL,
  `profile` varchar(128) NOT NULL,
  `fcm_id` varchar(1024) DEFAULT NULL,
  `coins` int(11) NOT NULL DEFAULT 100,
  `refer_code` varchar(128) DEFAULT NULL,
  `friends_code` varchar(128) DEFAULT NULL,
  `status` int(11) NOT NULL DEFAULT 1,
  `date_registered` datetime DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
);

-- Categories table
CREATE TABLE `tbl_category` (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `category_name` varchar(128) NOT NULL,
  `image` varchar(255) NOT NULL,
  `maxlevel` int(11) NOT NULL DEFAULT 5,
  `no_of_que` int(11) NOT NULL DEFAULT 10,
  `is_premium` int(11) NOT NULL DEFAULT 0,
  `coins` int(11) NOT NULL DEFAULT 0,
  `status` int(11) NOT NULL DEFAULT 1,
  PRIMARY KEY (`id`)
);

-- Insert sample categories
INSERT INTO `tbl_category` (`category_name`, `image`, `maxlevel`, `no_of_que`) VALUES
('General Knowledge', 'general.png', 5, 10),
('Science', 'science.png', 5, 10),
('History', 'history.png', 5, 10),
('Sports', 'sports.png', 5, 10);

-- Questions table
CREATE TABLE `tbl_question` (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `category` int(11) NOT NULL,
  `subcategory` int(11) NOT NULL DEFAULT 0,
  `language_id` int(11) NOT NULL DEFAULT 1,
  `image` varchar(255) DEFAULT NULL,
  `question` text NOT NULL,
  `question_type` int(11) NOT NULL DEFAULT 1,
  `optiona` text NOT NULL,
  `optionb` text NOT NULL,
  `optionc` text NOT NULL,
  `optiond` text NOT NULL,
  `optione` text NOT NULL,
  `answer` varchar(10) NOT NULL,
  `level` int(11) NOT NULL DEFAULT 1,
  `note` text,
  PRIMARY KEY (`id`)
);

-- Insert sample questions
INSERT INTO `tbl_question` (`category`, `question`, `optiona`, `optionb`, `optionc`, `optiond`, `answer`, `level`) VALUES
(1, 'What is the capital of India?', 'Mumbai', 'Delhi', 'Kolkata', 'Chennai', 'b', 1),
(1, 'Which planet is known as Red Planet?', 'Venus', 'Mars', 'Jupiter', 'Saturn', 'b', 1),
(2, 'What is the chemical symbol for water?', 'H2O', 'CO2', 'NaCl', 'O2', 'a', 1),
(2, 'How many bones are in adult human body?', '206', '208', '210', '212', 'a', 2);

-- Settings table
CREATE TABLE `tbl_settings` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `type` varchar(128) NOT NULL,
  `message` longtext NOT NULL,
  PRIMARY KEY (`id`)
);

-- Insert basic settings
INSERT INTO `tbl_settings` (`type`, `message`) VALUES
('system_timezone', 'Asia/Kolkata'),
('system_timezone_gmt', '+05:30'),
('app_version', '2.3.4'),
('refer_coin', '50'),
('earn_coin', '25'),
('reward_coin', '10');
```

---

## Step 3: Setup Admin Panel

### Configure Admin Panel

1. **Copy Admin Panel**
   ```bash
   # Copy admin panel to XAMPP htdocs
   cp -r adminpanel2.3.4 C:/xampp/htdocs/elite_quiz_admin
   ```

2. **Install Dependencies**
   ```bash
   cd C:/xampp/htdocs/elite_quiz_admin
   composer install
   ```

3. **Configure Database Connection**
   
   Edit `application/config/database.php`:
   ```php
   <?php
   $db['default'] = array(
       'dsn'	=> '',
       'hostname' => 'localhost',
       'username' => 'root',
       'password' => '',  // Usually empty for XAMPP
       'database' => 'elite_quiz_db',
       'dbdriver' => 'mysqli',
       'dbprefix' => '',
       'pconnect' => FALSE,
       'db_debug' => (ENVIRONMENT !== 'production'),
       'cache_on' => FALSE,
       'cachedir' => '',
       'char_set' => 'utf8mb4',
       'dbcollat' => 'utf8mb4_unicode_ci',
       'swap_pre' => '',
       'encrypt' => FALSE,
       'compress' => FALSE,
       'stricton' => FALSE,
       'failover' => array(),
       'save_queries' => TRUE
   );
   ```

4. **Configure Base URL**
   
   Edit `application/config/config.php`:
   ```php
   $config['base_url'] = 'http://localhost/elite_quiz_admin/';
   $config['index_page'] = 'index.php';
   $config['encryption_key'] = 'your-32-character-secret-key-here';
   ```

5. **Set File Permissions**
   ```bash
   # For Windows (run as administrator)
   icacls "C:\xampp\htdocs\elite_quiz_admin\images" /grant Everyone:F
   icacls "C:\xampp\htdocs\elite_quiz_admin\upload" /grant Everyone:F
   
   # For Linux/Mac
   chmod -R 755 application/
   chmod -R 777 images/
   chmod -R 777 upload/
   ```

6. **Test Admin Panel**
   - Open http://localhost/elite_quiz_admin
   - Login with: username: `admin`, password: `admin123`

---

## Step 4: Configure Firebase

### Create Firebase Project

1. **Go to Firebase Console**
   - Visit https://console.firebase.google.com
   - Create new project: "elite-quiz-local"

2. **Enable Services**
   - **Authentication**: Enable Email/Password, Google, Phone
   - **Firestore**: Create database in test mode
   - **Cloud Messaging**: Enable for push notifications

3. **Add Android App**
   - Package name: `com.wrteam.flutterquiz` (or your custom package)
   - Download `google-services.json`

4. **Add iOS App** (if needed)
   - Bundle ID: `com.wrteam.flutterquiz`
   - Download `GoogleService-Info.plist`

### Configure Firestore Rules

```javascript
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Battle rooms
    match /battleRoom/{roomId} {
      allow read, write: if request.auth != null;
    }
    
    // Allow read/write access on all documents to any user signed in to the application
    match /{document=**} {
      allow read, write: if request.auth != null;
    }
  }
}
```

---

## Step 5: Setup Flutter App

### Configure Flutter App for Local Development

1. **Update API Configuration**
   
   Edit `elite_quiz_app-2.3.4/lib/core/config/config.dart`:
   ```dart
   // Local development configuration
   const String panelUrl = 'http://********/elite_quiz_admin'; // For Android emulator
   // const String panelUrl = 'http://localhost/elite_quiz_admin'; // For iOS simulator
   // const String panelUrl = 'http://*************/elite_quiz_admin'; // For physical device
   
   const String kAppName = 'Elite Quiz Local';
   const bool enableDebugMode = true;
   ```

2. **Add Firebase Configuration Files**
   ```bash
   # Copy Firebase config files
   cp google-services.json elite_quiz_app-2.3.4/android/app/
   cp GoogleService-Info.plist elite_quiz_app-2.3.4/ios/Runner/
   ```

3. **Update Network Security (Android)**
   
   Edit `elite_quiz_app-2.3.4/android/app/src/main/AndroidManifest.xml`:
   ```xml
   <application
       android:name="io.flutter.app.FlutterApplication"
       android:label="Elite Quiz"
       android:usesCleartextTraffic="true"
       android:networkSecurityConfig="@xml/network_security_config">
   ```
   
   Create `elite_quiz_app-2.3.4/android/app/src/main/res/xml/network_security_config.xml`:
   ```xml
   <?xml version="1.0" encoding="utf-8"?>
   <network-security-config>
       <domain-config cleartextTrafficPermitted="true">
           <domain includeSubdomains="true">********</domain>
           <domain includeSubdomains="true">localhost</domain>
           <domain includeSubdomains="true">*************</domain>
       </domain-config>
   </network-security-config>
   ```

4. **Install Dependencies**
   ```bash
   cd elite_quiz_app-2.3.4
   flutter pub get
   ```

5. **Generate App Icons**
   ```bash
   dart run flutter_launcher_icons
   ```

---

## Step 6: Test the Setup

### Test Admin Panel

1. **Access Admin Panel**
   - URL: http://localhost/elite_quiz_admin
   - Login: admin / admin123

2. **Test Features**
   - Add categories
   - Add questions
   - Check API endpoints: http://localhost/elite_quiz_admin/Api/get_categories

### Test Flutter App

1. **Run Flutter App**
   ```bash
   cd elite_quiz_app-2.3.4
   flutter run
   ```

2. **Test Registration**
   - Create new account with email
   - Verify Firebase authentication works

3. **Test API Connectivity**
   - Check if categories load
   - Try playing a quiz
   - Verify coin system works

---

## Troubleshooting

### Common Issues

1. **CORS Issues**
   - Add CORS headers in admin panel
   - Edit `application/config/config.php`:
   ```php
   header('Access-Control-Allow-Origin: *');
   header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
   header('Access-Control-Allow-Headers: Content-Type');
   ```

2. **Database Connection Failed**
   - Check MySQL service is running
   - Verify database credentials
   - Check database name exists

3. **Flutter App Can't Connect**
   - Use correct IP address for physical device
   - Check network security config
   - Verify API endpoints are accessible

4. **Firebase Issues**
   - Check Firebase configuration files
   - Verify project settings
   - Check authentication methods enabled

### Network Configuration

**For Android Emulator:**
- Use `********` instead of `localhost`

**For iOS Simulator:**
- Use `localhost` or `127.0.0.1`

**For Physical Device:**
- Use your computer's IP address (e.g., `*************`)
- Ensure device and computer are on same network

### Find Your IP Address

**Windows:**
```cmd
ipconfig
```

**Mac/Linux:**
```bash
ifconfig
```

Look for your local network IP (usually starts with 192.168.x.x)

---

## Next Steps

1. **Customize the App**
   - Update app name and package
   - Change colors and themes
   - Add your own questions and categories

2. **Add Sample Data**
   - Create more categories
   - Add questions with images
   - Set up contests and exams

3. **Test All Features**
   - Quiz gameplay
   - Battle system
   - Coin system
   - Leaderboards

Your local development environment is now ready! You can start developing and testing the Elite Quiz application locally.
