/*
 *  /MathJax/fonts/HTML-CSS/TeX/png/Main/Regular/SpacingModLetters.js
 *  
 *  Copyright (c) 2010-2013 The MathJax Consortium
 *
 *  Part of the MathJax library.
 *  See http://www.mathjax.org for details.
 * 
 *  Licensed under the Apache License, Version 2.0;
 *  you may not use this file except in compliance with the License.
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 */

MathJax.OutputJax["HTML-CSS"].defineImageData({MathJax_Main:{710:[[3,2,-3],[4,3,-4],[4,3,-5],[5,3,-5],[6,3,-7],[7,4,-9],[8,4,-10],[9,5,-13],[11,6,-15],[13,7,-17],[16,7,-21],[18,9,-24],[22,10,-29],[26,12,-35]],711:[[3,1,-3],[4,1,-5],[4,2,-6],[5,2,-6],[6,2,-7],[7,3,-9],[8,3,-10],[9,3,-13],[11,4,-15],[13,5,-17],[16,6,-21],[18,6,-24],[22,8,-29],[26,9,-34]],713:[[3,1,-3],[4,1,-5],[5,1,-6],[6,1,-6],[6,1,-7],[8,1,-10],[9,1,-11],[10,2,-13],[12,3,-15],[15,2,-18],[17,2,-22],[20,3,-26],[24,3,-30],[29,4,-36]],714:[[3,2,-3],[4,2,-4],[4,3,-5],[5,3,-5],[6,3,-6],[7,4,-8],[8,5,-9],[10,6,-12],[11,7,-14],[13,7,-17],[16,9,-19],[19,10,-23],[22,12,-28],[26,13,-34]],715:[[3,2,-3],[3,2,-4],[3,3,-5],[4,3,-5],[5,3,-6],[5,4,-8],[6,4,-10],[7,6,-12],[9,7,-14],[10,7,-17],[12,8,-20],[14,10,-23],[17,11,-28],[20,13,-34]],728:[[3,2,-3],[4,2,-4],[4,2,-6],[5,2,-6],[6,3,-6],[7,3,-9],[8,4,-10],[10,5,-12],[12,5,-15],[14,6,-17],[16,7,-21],[19,9,-24],[23,10,-29],[27,12,-34]],729:[[3,1,-4],[3,2,-4],[4,2,-6],[4,2,-6],[5,2,-7],[6,3,-9],[7,3,-11],[8,3,-14],[9,4,-16],[11,5,-18],[13,5,-22],[15,6,-26],[18,7,-31],[21,9,-37]],730:[[3,2,-3],[3,2,-4],[4,2,-6],[5,2,-6],[5,3,-6],[6,3,-9],[7,4,-10],[9,5,-13],[10,6,-15],[12,7,-17],[14,8,-22],[17,8,-25],[20,10,-30],[24,12,-36]],732:[[3,1,-4],[4,1,-5],[5,1,-7],[5,1,-7],[6,1,-8],[7,3,-9],[9,3,-11],[10,3,-14],[12,3,-17],[14,4,-19],[17,4,-23],[20,5,-26],[23,6,-32],[28,7,-38]]}});MathJax.Ajax.loadComplete(MathJax.OutputJax["HTML-CSS"].imgDir+"/Main/Regular"+MathJax.OutputJax["HTML-CSS"].imgPacked+"/SpacingModLetters.js");

