# Elite Quiz - Quick Start Guide for Local Development

## 🚀 One-Click Setup (Recommended)

### For Windows Users
```bash
# Run as Administrator
quick_setup.bat
```

### For macOS/Linux Users
```bash
# Make executable and run
chmod +x quick_setup.sh
sudo ./quick_setup.sh
```

## 📋 Manual Setup (Step by Step)

### Step 1: Install Prerequisites
1. **Download and Install XAMPP**
   - Windows: https://www.apachefriends.org/download.html
   - Install with default settings

2. **Install Flutter SDK**
   - Download from: https://flutter.dev/docs/get-started/install
   - Add to PATH

3. **Install Git** (if not already installed)

### Step 2: Setup Local Server
1. **Start XAMPP**
   - Open XAMPP Control Panel
   - Start Apache and MySQL services

2. **Configure Admin Panel**
   ```bash
   # Run configuration script
   php setup_local_config.php
   
   # Copy admin panel to XAMPP
   cp -r adminpanel2.3.4 C:/xampp/htdocs/elite_quiz_admin
   ```

3. **Setup Database**
   - Open http://localhost/phpmyadmin
   - Create database: `elite_quiz_local`
   - Import: `adminpanel2.3.4/install/assets/quiz.php`

### Step 3: Configure Firebase
1. **Create Firebase Project**
   - Go to https://console.firebase.google.com
   - Create project: `elite-quiz-local`
   - Enable Authentication, Firestore, Cloud Messaging

2. **Add App Configuration**
   - Download `google-services.json` → `android/app/`
   - Download `GoogleService-Info.plist` → `ios/Runner/`

### Step 4: Setup Flutter App
1. **Configure for Local Development**
   ```bash
   # Run Flutter configuration
   dart setup_flutter_local.dart
   ```

2. **Install Dependencies**
   ```bash
   cd elite_quiz_app-2.3.4
   flutter pub get
   dart run flutter_launcher_icons
   ```

### Step 5: Test Everything
1. **Test Admin Panel**
   - Open: http://localhost/elite_quiz_admin
   - Login: admin / admin123

2. **Test Flutter App**
   ```bash
   flutter run
   ```

## 🔧 Configuration Details

### API Endpoints
- **Admin Panel:** `http://localhost/elite_quiz_admin`
- **API Base:** `http://localhost/elite_quiz_admin/Api`
- **Database:** `elite_quiz_local`

### Default Credentials
- **Admin Username:** `admin`
- **Admin Password:** `admin123`

### Network Configuration
- **Android Emulator:** `http://********/elite_quiz_admin`
- **iOS Simulator:** `http://localhost/elite_quiz_admin`
- **Physical Device:** `http://YOUR_IP/elite_quiz_admin`

## 📱 Device-Specific Setup

### For Android Emulator
```dart
// In lib/core/config/config.dart
const panelUrl = 'http://********/elite_quiz_admin';
```

### For iOS Simulator
```dart
// In lib/core/config/config.dart
const panelUrl = 'http://localhost/elite_quiz_admin';
```

### For Physical Device
1. **Find Your IP Address**
   ```bash
   # Windows
   ipconfig
   
   # macOS/Linux
   ifconfig
   ```

2. **Update Configuration**
   ```dart
   // Replace with your actual IP
   const panelUrl = 'http://*************/elite_quiz_admin';
   ```

## 🔍 Troubleshooting

### Common Issues & Solutions

#### 1. Admin Panel Not Loading
```bash
# Check if Apache is running
# Verify URL: http://localhost/elite_quiz_admin
# Check file permissions
```

#### 2. Database Connection Failed
```bash
# Check if MySQL is running in XAMPP
# Verify database name: elite_quiz_local
# Check database credentials in config
```

#### 3. Flutter App Can't Connect
```bash
# For Android Emulator: use ********
# For Physical Device: use your computer's IP
# Check network security config
```

#### 4. Firebase Auth Failed
```bash
# Check google-services.json location
# Verify Firebase project settings
# Enable authentication methods
```

## 📚 Documentation Files

| File | Purpose |
|------|---------|
| `LOCAL_SETUP_GUIDE.md` | Detailed setup instructions |
| `DATABASE_SETUP.md` | Database configuration guide |
| `FIREBASE_SETUP.md` | Firebase configuration guide |
| `FLUTTER_LOCAL_SETUP.md` | Flutter app configuration |
| `TESTING_GUIDE.md` | Complete testing procedures |
| `API_REFERENCE.md` | API documentation |

## 🎯 Quick Commands

### XAMPP Control
```bash
# Start XAMPP (Windows)
C:\xampp\xampp_start.exe

# Start XAMPP (Linux)
sudo /opt/lampp/lampp start

# Start XAMPP (macOS)
sudo /Applications/XAMPP/xamppfiles/xampp start
```

### Flutter Commands
```bash
# Install dependencies
flutter pub get

# Run app
flutter run

# Clean build
flutter clean

# Check setup
flutter doctor

# Generate icons
dart run flutter_launcher_icons
```

### Database Commands
```bash
# Access MySQL
mysql -u root

# Create database
CREATE DATABASE elite_quiz_local;

# Import schema
mysql -u root elite_quiz_local < quiz.php
```

## 🌐 URLs Reference

| Service | URL |
|---------|-----|
| Admin Panel | http://localhost/elite_quiz_admin |
| phpMyAdmin | http://localhost/phpmyadmin |
| API Base | http://localhost/elite_quiz_admin/Api |
| XAMPP Dashboard | http://localhost |

## 📊 Testing Checklist

### ✅ Admin Panel
- [ ] Login successful
- [ ] Categories visible
- [ ] Questions loadable
- [ ] API endpoints working

### ✅ Flutter App
- [ ] App compiles and runs
- [ ] Categories load from server
- [ ] User registration works
- [ ] Quiz gameplay functional

### ✅ Database
- [ ] All tables created
- [ ] Sample data loaded
- [ ] Admin user exists
- [ ] API can read/write

### ✅ Firebase
- [ ] Authentication enabled
- [ ] Firestore accessible
- [ ] Config files added
- [ ] No errors in console

## 🚀 Next Steps

1. **Customize Content**
   - Add your own categories and questions
   - Upload custom images
   - Configure app settings

2. **Branding**
   - Update app name and logo
   - Change color scheme
   - Modify UI elements

3. **Testing**
   - Test all features thoroughly
   - Add more sample data
   - Test on different devices

4. **Production Preparation**
   - Setup production server
   - Configure production Firebase
   - Update security settings

## 🆘 Getting Help

If you encounter issues:

1. **Check Documentation**
   - Read relevant `.md` files
   - Follow troubleshooting guides

2. **Verify Setup**
   - Run through testing checklist
   - Check all services are running

3. **Debug Logs**
   - Check XAMPP logs
   - Use `flutter logs`
   - Monitor browser console

## 🎉 Success!

If everything is working:
- Admin panel loads and login works
- Flutter app connects to local server
- Categories and questions load properly
- User registration/login functional

**Your Elite Quiz local development environment is ready!** 

You can now start developing, customizing, and testing the application locally. 🚀
