/*
 *  /MathJax/fonts/HTML-CSS/TeX/png/AMS/Regular/Dingbats.js
 *  
 *  Copyright (c) 2010-2013 The MathJax Consortium
 *
 *  Part of the MathJax library.
 *  See http://www.mathjax.org for details.
 * 
 *  Licensed under the Apache License, Version 2.0;
 *  you may not use this file except in compliance with the License.
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 */

MathJax.OutputJax["HTML-CSS"].defineImageData({MathJax_AMS:{10003:[[6,6,1],[7,7,1],[8,8,1],[9,10,1],[11,11,1],[13,13,1],[15,15,1],[18,18,1],[21,21,1],[25,26,2],[30,30,2],[35,35,2],[42,42,2],[50,50,3]],10016:[[6,6,1],[7,7,1],[8,9,1],[10,10,1],[11,11,1],[13,13,1],[16,15,1],[19,18,1],[22,21,1],[26,25,1],[31,30,1],[37,35,1],[44,42,2],[52,50,2]]}});MathJax.Ajax.loadComplete(MathJax.OutputJax["HTML-CSS"].imgDir+"/AMS/Regular"+MathJax.OutputJax["HTML-CSS"].imgPacked+"/Dingbats.js");

