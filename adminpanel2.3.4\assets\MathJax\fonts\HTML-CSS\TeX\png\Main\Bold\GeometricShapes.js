/*
 *  /MathJax/fonts/HTML-CSS/TeX/png/Main/Bold/GeometricShapes.js
 *  
 *  Copyright (c) 2010-2013 The MathJax Consortium
 *
 *  Part of the MathJax library.
 *  See http://www.mathjax.org for details.
 * 
 *  Licensed under the Apache License, Version 2.0;
 *  you may not use this file except in compliance with the License.
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 */

MathJax.OutputJax["HTML-CSS"].defineImageData({"MathJax_Main-bold":{9651:[[7,5,0],[8,6,0],[10,7,0],[12,9,0],[14,10,0],[16,12,0],[19,14,0],[23,17,0],[27,20,0],[32,24,0],[38,28,0],[45,34,0],[53,40,0],[63,47,0]],9657:[[4,5,1],[5,6,1],[6,7,1],[7,8,1],[8,9,1],[10,10,1],[11,12,1],[13,14,1],[15,17,2],[18,20,2],[22,24,2],[25,28,2],[30,33,3],[36,39,3]],9661:[[7,6,2],[8,7,2],[10,8,3],[12,9,3],[14,10,3],[16,13,4],[19,14,4],[23,18,6],[27,20,6],[32,25,8],[38,29,9],[45,34,10],[53,40,12],[63,47,14]],9667:[[4,5,1],[5,6,1],[6,7,1],[7,8,1],[8,9,1],[9,10,1],[11,12,1],[13,14,1],[15,17,2],[18,20,2],[22,24,2],[26,28,2],[30,33,3],[36,39,3]],9711:[[8,7,2],[9,8,2],[11,9,2],[13,12,3],[15,13,3],[18,16,4],[22,19,5],[26,22,5],[30,26,6],[36,31,7],[43,37,9],[51,44,10],[60,52,12],[72,61,14]]}});MathJax.Ajax.loadComplete(MathJax.OutputJax["HTML-CSS"].imgDir+"/Main/Bold"+MathJax.OutputJax["HTML-CSS"].imgPacked+"/GeometricShapes.js");

