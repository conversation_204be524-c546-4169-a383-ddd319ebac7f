/*
 *  /MathJax/fonts/HTML-CSS/TeX/png/AMS/Regular/SuppMathOperators.js
 *  
 *  Copyright (c) 2010-2013 The MathJax Consortium
 *
 *  Part of the MathJax library.
 *  See http://www.mathjax.org for details.
 * 
 *  Licensed under the Apache License, Version 2.0;
 *  you may not use this file except in compliance with the License.
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 */

MathJax.OutputJax["HTML-CSS"].defineImageData({MathJax_AMS:{10846:[[4,7,1],[5,8,1],[6,9,1],[7,12,2],[8,14,2],[10,16,2],[11,18,2],[13,22,3],[16,26,3],[19,31,4],[22,36,4],[26,43,5],[31,51,6],[37,61,7]],10877:[[5,7,2],[6,8,2],[8,9,2],[9,10,2],[10,11,2],[12,14,3],[14,16,3],[17,19,4],[20,22,4],[24,26,5],[28,31,6],[34,37,7],[40,44,8],[48,51,9]],10878:[[5,7,2],[6,8,2],[7,9,2],[9,10,2],[10,11,2],[12,14,3],[14,16,3],[17,19,4],[20,22,4],[23,26,5],[28,31,6],[33,37,7],[39,44,8],[46,51,9]],10885:[[5,8,2],[6,10,3],[7,11,3],[9,13,4],[10,15,4],[12,18,5],[14,21,6],[17,25,7],[20,31,9],[24,36,10],[29,42,12],[34,50,14],[40,60,17],[48,71,20]],10886:[[5,8,2],[6,10,3],[7,11,3],[9,13,4],[10,15,4],[12,18,5],[14,21,6],[17,25,7],[20,30,8],[24,36,10],[29,42,12],[34,50,14],[40,59,16],[48,70,19]],10887:[[5,7,2],[6,9,3],[7,10,3],[9,11,3],[10,13,4],[12,16,5],[14,18,5],[17,21,6],[20,25,7],[23,30,9],[28,35,10],[33,42,12],[39,50,14],[46,58,16]],10888:[[5,7,2],[6,9,3],[7,10,3],[9,11,3],[10,13,4],[12,16,5],[14,18,5],[17,21,6],[20,25,7],[23,30,9],[28,35,10],[33,42,12],[39,50,14],[46,58,16]],10889:[[5,9,3],[6,11,4],[8,12,4],[9,14,5],[10,17,6],[12,20,7],[14,23,8],[17,27,9],[20,33,11],[24,39,13],[29,46,16],[34,54,18],[40,65,22],[48,77,26]],10890:[[5,9,3],[6,11,4],[8,12,4],[9,14,5],[10,17,6],[12,20,7],[14,23,8],[17,27,9],[20,33,11],[24,39,13],[29,46,16],[34,54,18],[40,65,22],[48,77,26]],10891:[[5,11,4],[6,13,4],[7,15,5],[9,18,6],[10,21,7],[12,25,8],[14,30,10],[17,35,11],[20,41,13],[23,49,16],[28,59,19],[33,69,22],[39,82,26],[47,97,31]],10892:[[5,11,4],[6,13,4],[7,15,5],[9,18,6],[10,21,7],[12,25,8],[14,30,10],[17,35,11],[20,41,13],[23,49,16],[28,59,19],[33,69,22],[39,82,26],[46,97,31]],10901:[[5,6,1],[6,8,2],[8,9,2],[9,10,2],[10,11,2],[12,14,3],[14,16,3],[17,19,4],[20,22,4],[24,26,5],[28,31,6],[34,37,7],[40,43,8],[48,52,10]],10902:[[5,6,1],[6,8,2],[7,9,2],[9,10,2],[10,11,2],[12,14,3],[14,16,3],[17,19,4],[20,22,4],[23,26,5],[28,31,6],[33,37,7],[39,43,8],[46,52,10]],10933:[[5,8,2],[6,10,3],[7,11,3],[9,13,4],[10,15,4],[12,18,5],[14,21,6],[17,25,7],[20,29,8],[23,35,10],[28,42,12],[33,49,14],[39,58,16],[46,69,19]],10934:[[5,8,2],[6,10,3],[7,11,3],[9,13,4],[10,15,4],[12,18,5],[14,21,6],[17,25,7],[20,29,8],[23,35,10],[28,42,12],[33,49,14],[39,58,16],[46,69,19]],10935:[[5,8,2],[6,10,3],[8,11,3],[9,13,4],[10,15,4],[12,18,5],[14,21,6],[17,25,7],[20,31,9],[24,36,10],[29,42,12],[34,50,14],[40,60,17],[48,71,20]],10936:[[5,8,2],[6,10,3],[8,11,3],[9,13,4],[10,15,4],[12,18,5],[14,21,6],[17,25,7],[20,31,9],[24,36,10],[29,42,12],[34,50,14],[40,60,17],[48,71,20]],10937:[[5,9,3],[6,10,3],[8,12,4],[9,13,4],[10,16,5],[12,19,6],[15,22,7],[17,26,8],[20,32,10],[24,38,12],[29,44,14],[34,52,16],[40,62,19],[48,73,22]],10938:[[5,9,3],[6,10,3],[8,12,4],[9,13,4],[10,16,5],[12,19,6],[15,22,7],[17,26,8],[20,32,10],[24,38,12],[29,44,14],[34,52,16],[40,62,19],[48,73,22]],10949:[[5,8,2],[6,9,2],[7,10,2],[9,12,3],[10,14,3],[12,17,4],[14,20,5],[16,23,5],[20,28,7],[23,33,8],[27,39,9],[32,46,11],[39,55,13],[46,65,15]],10950:[[5,8,2],[6,9,2],[7,10,2],[9,12,3],[10,14,3],[12,17,4],[14,19,4],[17,23,5],[20,27,6],[24,32,7],[28,39,9],[33,46,10],[39,54,12],[47,64,14]],10955:[[5,9,3],[6,11,4],[7,12,4],[9,15,5],[10,17,6],[12,20,7],[14,24,8],[17,28,9],[20,33,11],[23,39,13],[28,47,16],[33,55,18],[39,66,22],[46,78,26]],10956:[[5,9,3],[6,11,4],[7,12,4],[9,14,5],[10,17,6],[12,20,7],[14,24,8],[17,28,9],[20,33,11],[23,39,13],[28,47,16],[33,55,18],[39,66,22],[46,78,26]]}});MathJax.Ajax.loadComplete(MathJax.OutputJax["HTML-CSS"].imgDir+"/AMS/Regular"+MathJax.OutputJax["HTML-CSS"].imgPacked+"/SuppMathOperators.js");

