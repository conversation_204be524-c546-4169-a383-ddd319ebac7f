/*
 *  /MathJax/jax/output/HTML-CSS/fonts/Gyre-<PERSON>lla/Script/Regular/Main.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.OutputJax["HTML-CSS"].FONTDATA.FONTS.GyrePagellaMathJax_Script={directory:"Script/Regular",family:"GyrePagellaMathJax_Script",testString:"\u00A0\u210A\u210B\u2110\u2112\u211B\u212C\u212F\u2130\u2131\u2133\u2134\uD835\uDC9C\uD835\uDC9E\uD835\uDC9F",32:[0,0,250,0,0],160:[0,0,250,0,0],8458:[398,398,508,-62,562],8459:[798,18,790,55,844],8464:[787,8,577,75,647],8466:[755,8,789,52,842],8475:[764,8,905,55,958],8492:[764,8,887,47,940],8495:[398,8,406,21,459],8496:[757,8,569,55,623],8497:[802,8,754,55,835],8499:[783,73,940,47,1011],8500:[398,8,471,23,524],119964:[783,8,783,33,836],119966:[757,8,568,55,621],119967:[764,8,867,39,920],119970:[757,406,637,51,690],119973:[787,406,679,55,749],119974:[788,8,881,62,935],119977:[783,8,832,42,896],119978:[757,8,788,53,841],119979:[764,8,833,55,887],119980:[757,244,788,56,841],119982:[764,8,608,62,662],119983:[897,8,555,43,971],119984:[798,8,657,51,710],119985:[816,8,606,52,659],119986:[816,8,948,48,1001],119987:[757,8,672,60,725],119988:[798,406,649,51,702],119989:[764,14,673,47,732],119990:[398,8,567,23,620],119991:[788,8,465,18,519],119992:[398,8,406,21,459],119993:[788,8,567,23,620],119995:[788,390,247,-83,300],119997:[788,8,524,-10,577],119998:[632,8,244,30,298],119999:[632,398,215,-309,268],120000:[788,8,456,-10,510],120001:[788,8,265,17,319],120002:[398,8,753,12,806],120003:[398,8,520,12,573],120005:[398,398,485,-118,538],120006:[398,397,486,23,540],120007:[421,0,442,39,495],120008:[421,8,413,-26,467],120009:[655,8,286,23,339],120010:[390,8,540,39,593],120011:[420,8,491,39,545],120012:[420,8,649,39,702],120013:[398,8,488,25,541],120014:[390,398,530,-39,584],120015:[404,8,437,-13,490],120016:[785,10,796,30,853],120017:[767,10,913,44,970],120018:[759,10,568,39,625],120019:[767,10,880,36,937],120020:[759,10,569,39,627],120021:[807,10,761,52,850],120022:[759,408,664,35,722],120023:[801,22,803,39,861],120024:[789,10,590,73,663],120025:[789,408,692,39,764],120026:[790,10,894,47,952],120027:[758,10,789,36,846],120028:[785,77,966,43,1040],120029:[785,10,852,39,917],120030:[759,10,801,51,858],120031:[767,10,846,47,904],120032:[759,250,801,53,858],120033:[767,10,943,39,975],120034:[767,10,615,60,672],120035:[900,10,555,40,972],120036:[801,10,696,48,753],120037:[819,10,632,49,689],120038:[819,10,987,49,1044],120039:[759,10,685,57,742],120040:[801,408,688,48,745],120041:[767,17,673,43,736],120042:[400,10,606,21,663],120043:[790,10,491,16,549],120044:[400,10,432,20,489],120045:[790,10,606,21,663],120046:[400,10,419,18,476],120047:[790,393,274,-86,332],120048:[400,400,534,-52,592],120049:[790,10,563,-13,620],120050:[649,10,270,34,328],120051:[647,400,228,-312,285],120052:[790,10,489,-3,546],120053:[790,10,291,16,348],120054:[400,10,805,10,862],120055:[400,10,559,10,616],120056:[400,10,497,21,554],120057:[400,400,511,-134,568],120058:[400,399,525,21,582],120059:[424,3,481,38,540],120060:[424,10,426,-29,484],120061:[658,10,299,21,356],120062:[393,10,579,35,636],120063:[423,10,511,35,568],120064:[423,10,688,35,745],120065:[400,10,514,35,571],120066:[393,400,558,-16,615],120067:[408,10,437,-16,494]};MathJax.Callback.Queue(["initFont",MathJax.OutputJax["HTML-CSS"],"GyrePagellaMathJax_Script"],["loadComplete",MathJax.Ajax,MathJax.OutputJax["HTML-CSS"].fontDir+"/Script/Regular/Main.js"]);
