# Elite Quiz Local Setup Testing Guide

## Pre-Testing Checklist

Before testing, ensure all components are properly set up:

### ✅ Server Environment
- [ ] XAMPP/WAMP installed and running
- [ ] Apache service running (green in XAMPP)
- [ ] MySQL service running (green in XAMPP)
- [ ] Admin panel accessible at `http://localhost/elite_quiz_admin`

### ✅ Database
- [ ] Database `elite_quiz_local` created
- [ ] Database schema imported successfully
- [ ] Admin user exists (admin/admin123)
- [ ] Sample data loaded

### ✅ Firebase
- [ ] Firebase project created
- [ ] Authentication enabled (Email, Google, Phone)
- [ ] Firestore database created
- [ ] `google-services.json` added to Android app
- [ ] `GoogleService-Info.plist` added to iOS app (if applicable)

### ✅ Flutter App
- [ ] Dependencies installed (`flutter pub get`)
- [ ] Config updated for local development
- [ ] Network security config added
- [ ] App icons generated

## Testing Sequence

### Phase 1: Admin Panel Testing

#### 1.1 Access Admin Panel
```bash
# Open in browser
http://localhost/elite_quiz_admin
```

**Expected Result:** Login page should load

#### 1.2 Admin Login
- **Username:** `admin`
- **Password:** `admin123`

**Expected Result:** Dashboard should load with statistics

#### 1.3 Test Database Connection
1. Go to **Categories** section
2. Check if sample categories are visible
3. Try adding a new category

**Expected Result:** Categories should load and new category should save

#### 1.4 Test API Endpoints
```bash
# Test categories API
curl -X POST http://localhost/elite_quiz_admin/Api/get_categories \
  -H "Content-Type: application/json" \
  -d '{"type": 1}'
```

**Expected Result:** JSON response with categories data

### Phase 2: Flutter App Testing

#### 2.1 Launch Flutter App
```bash
cd elite_quiz_app-2.3.4
flutter run
```

**Expected Result:** App should compile and launch successfully

#### 2.2 Test App Loading
1. App should show splash screen
2. Navigate to main screen
3. Check if categories are loading

**Expected Result:** Categories should load from local server

#### 2.3 Test User Registration
1. Go to Sign Up screen
2. Register with email and password
3. Complete profile setup

**Expected Result:** 
- User should be created in Firebase Auth
- User should appear in local database `tbl_users`

#### 2.4 Test User Login
1. Login with created credentials
2. Check if user data loads

**Expected Result:** User should login successfully

### Phase 3: Quiz Functionality Testing

#### 3.1 Test Quiz Categories
1. Navigate to Quiz Zone
2. Select a category
3. Check if questions load

**Expected Result:** Questions should load from local database

#### 3.2 Test Quiz Gameplay
1. Start a quiz
2. Answer questions
3. Complete quiz
4. Check results

**Expected Result:** 
- Questions should display correctly
- Score should be calculated
- Coins should be awarded
- Results should save to database

#### 3.3 Test Coin System
1. Check initial coin balance
2. Play quiz and earn coins
3. Verify coin balance updates

**Expected Result:** Coins should update in both app and database

### Phase 4: Advanced Features Testing

#### 4.1 Test Battle System (if Firebase configured)
1. Create a battle room
2. Check if room appears in Firestore
3. Test real-time updates

**Expected Result:** Battle room should be created in Firestore

#### 4.2 Test Bookmark System
1. Bookmark a question during quiz
2. Check bookmarks section
3. Verify bookmark in database

**Expected Result:** Bookmark should save to `tbl_bookmark`

#### 4.3 Test Leaderboard
1. Complete multiple quizzes
2. Check daily leaderboard
3. Verify rankings

**Expected Result:** User should appear in leaderboard

## Troubleshooting Common Issues

### Issue 1: Admin Panel Not Loading

**Symptoms:**
- Browser shows "This site can't be reached"
- 404 error

**Solutions:**
1. Check if Apache is running in XAMPP
2. Verify admin panel is copied to `htdocs/elite_quiz_admin`
3. Check URL: `http://localhost/elite_quiz_admin`

### Issue 2: Database Connection Failed

**Symptoms:**
- Admin panel shows database error
- API returns database connection error

**Solutions:**
1. Check if MySQL is running in XAMPP
2. Verify database name in `application/config/database.php`
3. Check if database `elite_quiz_local` exists
4. Import database schema if missing

### Issue 3: Flutter App Can't Connect to API

**Symptoms:**
- Categories not loading
- API connection timeout
- Network error in app

**Solutions:**
1. **For Android Emulator:** Use `********` instead of `localhost`
2. **For Physical Device:** Use computer's IP address
3. Check network security config
4. Verify admin panel API is accessible

### Issue 4: Firebase Authentication Failed

**Symptoms:**
- User registration fails
- Firebase auth error

**Solutions:**
1. Check if `google-services.json` is in correct location
2. Verify Firebase project configuration
3. Enable authentication methods in Firebase Console
4. Check internet connectivity

### Issue 5: Questions Not Loading

**Symptoms:**
- Empty quiz categories
- No questions in quiz

**Solutions:**
1. Check if questions exist in database
2. Verify category IDs match
3. Check API response for questions
4. Import sample questions if missing

## Performance Testing

### Load Testing
1. Create multiple user accounts
2. Play multiple quizzes simultaneously
3. Monitor server performance

### Memory Testing
1. Play extended quiz sessions
2. Monitor app memory usage
3. Check for memory leaks

### Network Testing
1. Test with slow network connection
2. Test offline functionality
3. Verify data caching

## API Testing Commands

### Test All Major Endpoints

```bash
# Base URL
BASE_URL="http://localhost/elite_quiz_admin/Api"

# Test system configuration
curl -X POST $BASE_URL/get_system_configurations \
  -H "Content-Type: application/json" \
  -d '{}'

# Test categories
curl -X POST $BASE_URL/get_categories \
  -H "Content-Type: application/json" \
  -d '{"type": 1}'

# Test questions
curl -X POST $BASE_URL/get_questions_by_type \
  -H "Content-Type: application/json" \
  -d '{"type": 1, "limit": 5}'

# Test user signup (replace with actual Firebase ID)
curl -X POST $BASE_URL/user_signup \
  -H "Content-Type: application/json" \
  -d '{
    "firebase_id": "test123",
    "type": "email",
    "name": "Test User",
    "email": "<EMAIL>"
  }'
```

## Database Verification Queries

### Check Data Integrity

```sql
-- Check if tables exist
SHOW TABLES;

-- Check categories
SELECT * FROM tbl_category LIMIT 5;

-- Check questions
SELECT * FROM tbl_question LIMIT 5;

-- Check users
SELECT * FROM tbl_users LIMIT 5;

-- Check admin user
SELECT * FROM tbl_authenticate;

-- Check settings
SELECT * FROM tbl_settings LIMIT 10;
```

## Success Criteria

### ✅ Complete Setup Success

Your setup is successful if:

1. **Admin Panel:**
   - [ ] Loads without errors
   - [ ] Login works with admin/admin123
   - [ ] Categories and questions are visible
   - [ ] API endpoints respond correctly

2. **Flutter App:**
   - [ ] Compiles and runs without errors
   - [ ] Connects to local API successfully
   - [ ] Categories load from local server
   - [ ] User registration/login works

3. **Database:**
   - [ ] All tables created successfully
   - [ ] Sample data loaded
   - [ ] API can read/write data

4. **Firebase:**
   - [ ] Authentication works
   - [ ] Firestore accessible (for battles)
   - [ ] No configuration errors

## Next Steps After Successful Testing

1. **Add Custom Content:**
   - Create your own categories
   - Add custom questions
   - Upload category images

2. **Customize Branding:**
   - Update app name and logo
   - Change color scheme
   - Modify UI elements

3. **Configure Features:**
   - Set coin rewards
   - Configure badge system
   - Setup contest schedules

4. **Prepare for Production:**
   - Setup production server
   - Configure production Firebase
   - Update security settings

## Monitoring and Logs

### Check Logs

**XAMPP Logs:**
- Apache: `xampp/apache/logs/error.log`
- MySQL: `xampp/mysql/data/*.err`

**Flutter Logs:**
```bash
flutter logs
```

**Android Logs:**
```bash
adb logcat | grep flutter
```

### Monitor Performance

1. **Database Performance:**
   - Check query execution times
   - Monitor connection count
   - Watch for slow queries

2. **API Performance:**
   - Monitor response times
   - Check error rates
   - Watch memory usage

3. **App Performance:**
   - Monitor frame rates
   - Check memory usage
   - Watch for crashes

Your local development environment is now fully tested and ready for development! 🎉
