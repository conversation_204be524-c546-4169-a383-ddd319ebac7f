/*
 *  /MathJax/fonts/HTML-CSS/TeX/png/Main/Regular/LatinExtendedA.js
 *  
 *  Copyright (c) 2010-2013 The MathJax Consortium
 *
 *  Part of the MathJax library.
 *  See http://www.mathjax.org for details.
 * 
 *  Licensed under the Apache License, Version 2.0;
 *  you may not use this file except in compliance with the License.
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 */

MathJax.OutputJax["HTML-CSS"].defineImageData({MathJax_Main:{305:[[2,3,0],[3,4,0],[3,5,0],[3,5,0],[4,6,0],[5,8,0],[5,9,0],[6,11,0],[8,13,0],[9,15,0],[10,18,0],[12,21,0],[15,25,0],[17,30,0]]}});MathJax.Ajax.loadComplete(MathJax.OutputJax["HTML-CSS"].imgDir+"/Main/Regular"+MathJax.OutputJax["HTML-CSS"].imgPacked+"/LatinExtendedA.js");

