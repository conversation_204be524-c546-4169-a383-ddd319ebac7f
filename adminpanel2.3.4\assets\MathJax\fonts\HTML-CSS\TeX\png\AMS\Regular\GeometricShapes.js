/*
 *  /MathJax/fonts/HTML-CSS/TeX/png/AMS/Regular/GeometricShapes.js
 *  
 *  Copyright (c) 2010-2013 The MathJax Consortium
 *
 *  Part of the MathJax library.
 *  See http://www.mathjax.org for details.
 * 
 *  Licensed under the Apache License, Version 2.0;
 *  you may not use this file except in compliance with the License.
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 */

MathJax.OutputJax["HTML-CSS"].defineImageData({MathJax_AMS:{9632:[[5,5,0],[6,6,0],[8,7,0],[9,9,0],[10,10,0],[12,12,0],[15,14,0],[17,16,0],[20,20,0],[24,23,0],[29,27,0],[34,32,0],[40,39,0],[48,46,0]],9633:[[5,5,0],[6,6,0],[7,7,0],[9,8,0],[10,10,0],[12,12,0],[14,14,0],[17,16,0],[21,20,0],[24,23,0],[29,27,0],[34,32,0],[40,39,0],[48,46,0]],9650:[[5,5,1],[6,6,1],[7,7,1],[8,8,1],[9,9,1],[11,11,1],[13,13,1],[15,15,1],[18,17,1],[21,20,1],[25,24,1],[30,28,1],[36,34,2],[42,40,2]],9651:[[5,5,0],[6,5,0],[7,6,0],[8,8,1],[9,10,1],[11,11,1],[13,13,1],[15,15,1],[18,17,1],[21,20,1],[25,24,1],[30,28,1],[36,34,2],[42,41,2]],9654:[[5,5,1],[6,6,1],[7,7,1],[9,8,1],[10,9,1],[12,10,1],[14,12,1],[17,14,1],[20,17,2],[23,20,2],[28,24,2],[33,28,2],[39,33,3],[46,39,3]],9660:[[5,5,1],[6,6,1],[7,7,1],[8,8,1],[9,9,1],[11,11,1],[13,13,1],[15,15,1],[18,17,1],[21,20,1],[25,24,1],[30,28,1],[36,33,1],[42,40,2]],9661:[[5,5,1],[6,6,1],[7,7,1],[8,8,1],[9,9,1],[11,11,1],[13,13,1],[15,15,1],[18,17,1],[21,20,1],[25,24,1],[30,28,1],[36,33,1],[42,40,2]],9664:[[5,5,1],[6,6,1],[7,7,1],[9,8,1],[10,9,1],[12,10,1],[14,12,1],[17,14,1],[20,17,2],[23,20,2],[28,24,2],[33,28,2],[39,33,3],[46,39,3]],9674:[[5,6,1],[6,8,2],[7,10,2],[8,11,2],[9,12,2],[11,15,3],[12,17,3],[15,21,4],[17,24,4],[21,29,5],[25,35,6],[29,41,7],[34,48,8],[41,57,9]]}});MathJax.Ajax.loadComplete(MathJax.OutputJax["HTML-CSS"].imgDir+"/AMS/Regular"+MathJax.OutputJax["HTML-CSS"].imgPacked+"/GeometricShapes.js");

