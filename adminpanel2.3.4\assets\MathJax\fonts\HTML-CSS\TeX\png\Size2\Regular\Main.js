/*
 *  /MathJax/fonts/HTML-CSS/TeX/png/Size2/Regular/Main.js
 *  
 *  Copyright (c) 2010-2013 The MathJax Consortium
 *
 *  Part of the MathJax library.
 *  See http://www.mathjax.org for details.
 * 
 *  Licensed under the Apache License, Version 2.0;
 *  you may not use this file except in compliance with the License.
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 */

MathJax.OutputJax["HTML-CSS"].defineImageData({MathJax_Size2:{32:[[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0]],40:[[4,13,4],[5,15,5],[6,18,6],[7,22,8],[8,25,9],[10,30,11],[11,36,13],[14,42,15],[16,50,18],[19,60,21],[22,71,25],[27,84,30],[32,100,36],[37,119,43]],41:[[3,13,4],[4,15,5],[5,18,6],[5,22,8],[6,25,9],[7,30,11],[9,36,13],[10,42,15],[12,50,18],[14,60,21],[17,71,25],[20,84,30],[23,100,36],[28,119,43]],47:[[6,13,4],[7,15,5],[8,18,6],[9,21,8],[11,25,9],[13,30,11],[15,35,13],[18,42,15],[21,50,18],[25,59,21],[30,71,25],[36,84,30],[42,100,36],[50,119,43]],91:[[4,13,5],[4,16,6],[5,18,7],[6,22,8],[7,25,9],[8,30,11],[9,35,13],[11,43,16],[13,51,19],[15,59,21],[18,70,25],[22,84,30],[26,100,36],[30,119,43]],92:[[6,13,4],[7,15,5],[8,18,6],[9,21,8],[11,25,9],[13,30,11],[15,35,13],[18,42,15],[21,50,18],[25,59,21],[30,71,25],[35,84,30],[42,100,36],[50,119,43]],93:[[2,13,5],[3,16,6],[3,18,7],[3,22,8],[4,25,9],[5,30,11],[5,35,13],[6,43,16],[7,51,19],[9,59,21],[10,70,25],[12,84,30],[14,100,36],[17,119,43]],123:[[4,13,4],[5,15,5],[6,18,6],[7,22,8],[8,25,9],[10,30,11],[11,36,13],[13,42,15],[16,50,18],[18,60,21],[22,71,25],[26,84,30],[31,100,36],[36,119,43]],125:[[4,13,4],[5,15,5],[6,18,6],[7,22,8],[8,25,9],[10,30,11],[11,36,13],[13,42,15],[16,50,18],[18,60,21],[22,71,25],[26,84,30],[31,100,36],[36,119,43]],160:[[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0]],710:[[8,2,-4],[10,2,-5],[11,3,-6],[13,3,-7],[15,3,-8],[18,4,-9],[21,4,-11],[25,5,-13],[29,6,-16],[35,8,-19],[41,9,-22],[48,10,-26],[57,12,-31],[68,14,-37]],732:[[7,2,-4],[9,2,-5],[10,2,-6],[12,2,-7],[14,2,-8],[17,2,-10],[20,4,-11],[24,5,-13],[28,4,-17],[33,5,-20],[40,6,-24],[47,7,-28],[56,8,-34],[66,9,-40]],770:[[8,2,-4],[10,2,-5],[11,3,-6],[13,3,-7],[15,3,-8],[18,4,-9],[21,4,-11],[25,5,-13],[29,6,-16],[35,8,-19],[41,9,-22],[48,10,-26],[57,12,-31],[68,14,-37]],771:[[7,2,-4],[9,2,-5],[10,2,-6],[12,2,-7],[14,2,-8],[17,2,-10],[20,4,-11],[24,5,-13],[28,4,-17],[33,5,-20],[40,6,-24],[47,7,-28],[56,8,-34],[66,9,-40]],8719:[[9,10,3],[11,12,4],[12,15,5],[15,17,6],[17,19,6],[21,24,8],[24,28,9],[29,34,11],[34,40,13],[41,47,15],[48,56,18],[57,66,21],[68,78,25],[81,93,30]],8720:[[9,10,3],[11,12,4],[12,15,5],[15,17,6],[17,19,6],[21,24,8],[24,28,9],[29,34,11],[34,40,13],[41,47,15],[48,56,18],[57,66,21],[68,78,25],[81,93,30]],8721:[[10,10,3],[12,12,4],[14,15,5],[17,17,6],[20,20,7],[24,24,8],[28,28,9],[33,34,11],[39,40,13],[46,47,15],[55,56,18],[65,66,21],[77,78,25],[92,93,30]],8730:[[8,13,5],[9,16,6],[11,19,7],[12,22,8],[15,25,9],[17,31,11],[20,36,13],[24,43,16],[29,50,18],[34,60,22],[40,71,26],[48,85,31],[57,100,36],[68,119,43]],8747:[[7,16,6],[8,19,7],[10,23,9],[12,26,10],[14,31,12],[16,38,15],[19,44,17],[22,53,21],[27,62,24],[32,74,29],[37,88,34],[44,105,41],[53,124,48],[63,147,57]],8748:[[10,16,6],[13,19,7],[15,23,9],[18,26,10],[21,31,12],[25,38,15],[29,44,17],[35,53,21],[41,62,24],[49,74,29],[58,88,34],[69,105,41],[82,124,48],[98,147,57]],8749:[[14,16,6],[17,19,7],[20,23,9],[24,26,10],[28,31,12],[33,38,15],[39,44,17],[47,53,21],[56,62,24],[66,74,29],[78,88,34],[93,105,41],[110,124,48],[131,147,57]],8750:[[7,16,6],[8,19,7],[10,23,9],[12,26,10],[14,31,12],[16,38,15],[19,44,17],[22,53,21],[27,62,24],[32,74,29],[37,88,34],[44,105,41],[53,124,48],[63,147,57]],8896:[[8,11,4],[9,12,4],[11,15,5],[13,18,6],[15,21,7],[18,24,8],[21,28,9],[25,34,11],[30,40,13],[35,47,15],[42,56,18],[50,66,21],[59,78,25],[70,93,30]],8897:[[8,11,4],[9,12,4],[11,15,5],[13,18,6],[15,21,7],[18,24,8],[21,28,9],[25,34,11],[30,40,13],[35,47,15],[42,56,18],[50,66,21],[59,78,25],[70,93,30]],8898:[[8,10,3],[9,12,4],[11,15,5],[13,18,6],[15,21,7],[18,24,8],[21,28,9],[25,34,11],[30,40,13],[35,47,15],[42,56,18],[50,66,21],[59,78,25],[70,93,30]],8899:[[8,10,3],[9,12,4],[11,15,5],[13,18,6],[15,21,7],[18,24,8],[21,28,9],[25,34,11],[30,40,13],[35,47,15],[42,56,18],[50,66,21],[59,78,25],[70,93,30]],8968:[[4,13,5],[5,16,6],[6,19,7],[6,22,8],[8,25,9],[9,30,11],[10,36,13],[12,42,15],[15,50,18],[17,60,22],[21,71,26],[24,85,31],[29,100,36],[34,119,43]],8969:[[2,13,5],[3,16,6],[3,19,7],[4,22,8],[4,25,9],[5,30,11],[6,36,13],[8,42,15],[9,50,18],[10,60,22],[12,71,26],[15,85,31],[17,100,36],[20,119,43]],8970:[[4,13,5],[5,16,6],[6,19,7],[6,22,8],[8,25,9],[9,30,11],[10,36,13],[12,43,16],[15,50,18],[17,60,22],[21,72,26],[24,85,31],[29,100,36],[34,119,43]],8971:[[2,13,5],[3,16,6],[3,19,7],[4,22,8],[4,25,9],[5,30,11],[6,36,13],[8,43,16],[9,50,18],[10,60,22],[12,72,26],[15,85,31],[17,100,36],[20,119,43]],10216:[[4,13,5],[5,16,6],[6,19,7],[7,22,8],[8,25,9],[9,31,11],[11,36,13],[13,43,16],[15,50,18],[18,60,22],[21,71,26],[25,85,31],[29,100,36],[35,119,43]],10217:[[4,13,5],[4,16,6],[5,19,7],[6,22,8],[7,25,9],[9,31,11],[10,36,13],[12,43,16],[14,50,18],[17,60,22],[20,71,26],[24,85,31],[28,100,36],[33,119,43]],10752:[[10,10,3],[13,12,4],[15,15,5],[18,18,6],[21,21,7],[25,24,8],[29,28,9],[34,34,11],[41,40,13],[48,47,15],[57,56,18],[68,66,21],[81,78,25],[96,93,30]],10753:[[10,10,3],[12,13,4],[15,15,5],[18,17,6],[21,21,7],[25,24,8],[29,28,9],[34,34,11],[41,40,13],[48,47,15],[57,56,18],[68,66,21],[81,78,25],[96,93,30]],10754:[[10,10,3],[13,12,4],[15,15,5],[18,18,6],[21,21,7],[25,24,8],[29,28,9],[34,34,11],[41,40,13],[48,47,15],[57,56,18],[68,66,21],[81,78,25],[96,93,30]],10756:[[8,10,3],[9,12,4],[11,15,5],[13,18,6],[15,21,7],[18,24,8],[21,28,9],[25,34,11],[30,40,13],[35,47,15],[42,56,18],[50,65,21],[59,78,25],[70,93,30]],10758:[[7,10,3],[9,12,4],[11,15,5],[13,18,6],[15,21,7],[18,24,8],[21,28,9],[25,34,11],[29,40,13],[35,47,15],[42,55,18],[49,66,21],[59,78,25],[70,93,30]]}});MathJax.Ajax.loadComplete(MathJax.OutputJax["HTML-CSS"].imgDir+"/Size2/Regular"+MathJax.OutputJax["HTML-CSS"].imgPacked+"/Main.js");

