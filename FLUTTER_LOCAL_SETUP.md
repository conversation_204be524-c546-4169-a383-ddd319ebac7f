# Flutter App Local Development Setup

## Overview

This guide will help you configure the Elite Quiz Flutter app to work with your local development server.

## Prerequisites

- Flutter SDK 3.8.1 or higher
- Android Studio or VS Code
- Local server running (XAMPP/WAMP)
- Admin panel configured on localhost

## Step 1: Configure API Endpoints

The app configuration has been updated to support local development. The current configuration in `lib/core/config/config.dart` is set for:

**Android Emulator:** `http://********/elite_quiz_admin`

### For Different Environments

Edit `elite_quiz_app-2.3.4/lib/core/config/config.dart` and uncomment the appropriate line:

```dart
// For Android Emulator (use ******** instead of localhost)
const panelUrl = 'http://********/elite_quiz_admin';

// For iOS Simulator (use localhost)
// const panelUrl = 'http://localhost/elite_quiz_admin';

// For Physical Device (replace with your computer's IP address)
// const panelUrl = 'http://*************/elite_quiz_admin';
```

### Find Your Computer's IP Address

**Windows:**
```cmd
ipconfig
```

**macOS/Linux:**
```bash
ifconfig
```

Look for your local network IP (usually starts with 192.168.x.x or 10.0.x.x)

## Step 2: Network Security Configuration

### Android Configuration

The network security configuration has been added to allow HTTP traffic for local development:

**File:** `android/app/src/main/res/xml/network_security_config.xml`

This allows HTTP connections to:
- `********` (Android Emulator)
- `localhost` and `127.0.0.1`
- Common local IP ranges (`192.168.1.x`, `192.168.0.x`)

### Android Manifest

The `AndroidManifest.xml` has been updated with:
```xml
android:usesCleartextTraffic="true"
android:networkSecurityConfig="@xml/network_security_config"
```

## Step 3: Firebase Configuration

### Add Firebase Config Files

1. **For Android:**
   - Download `google-services.json` from Firebase Console
   - Place in: `android/app/google-services.json`

2. **For iOS:**
   - Download `GoogleService-Info.plist` from Firebase Console
   - Place in: `ios/Runner/GoogleService-Info.plist`

### Firebase Project Setup

Create a Firebase project with these services enabled:
- Authentication (Email, Google, Phone, Apple)
- Firestore Database
- Cloud Messaging

See `FIREBASE_SETUP.md` for detailed instructions.

## Step 4: Install Dependencies

```bash
cd elite_quiz_app-2.3.4
flutter pub get
```

## Step 5: Generate App Icons

```bash
dart run flutter_launcher_icons
```

## Step 6: Test the Setup

### Run the App

```bash
flutter run
```

### Test API Connectivity

1. **Check Categories Loading:**
   - Open the app
   - Navigate to Quiz Zone
   - Categories should load from your local server

2. **Test User Registration:**
   - Try creating a new account
   - Check if user appears in local database

3. **Test Quiz Functionality:**
   - Select a category
   - Play a quiz
   - Verify coins are updated

## Troubleshooting

### Common Issues

#### 1. API Connection Failed

**Error:** `Failed to connect to server`

**Solutions:**
- Check if XAMPP Apache service is running
- Verify admin panel URL is accessible: `http://localhost/elite_quiz_admin`
- For physical device, use your computer's IP instead of localhost
- Check firewall settings

#### 2. Network Security Error

**Error:** `Cleartext HTTP traffic not permitted`

**Solutions:**
- Verify `network_security_config.xml` exists
- Check `AndroidManifest.xml` has `networkSecurityConfig` attribute
- Add your specific IP to network security config

#### 3. Firebase Authentication Failed

**Error:** `Firebase auth failed`

**Solutions:**
- Check if `google-services.json` is in correct location
- Verify Firebase project configuration
- Enable authentication methods in Firebase Console

#### 4. Database Connection Error

**Error:** `Database connection failed`

**Solutions:**
- Check if MySQL service is running in XAMPP
- Verify database name and credentials in admin panel
- Check if database tables exist

### Debug API Calls

Enable API logging by setting in `config.dart`:
```dart
const bool enableApiLogs = true;
```

This will print API requests and responses in the console.

### Network Testing

Test API endpoints manually:

```bash
# Test categories endpoint
curl -X POST http://********/elite_quiz_admin/Api/get_categories \
  -H "Content-Type: application/json" \
  -d '{"access_key": "test", "type": 1}'

# Test system config
curl -X POST http://********/elite_quiz_admin/Api/get_system_configurations \
  -H "Content-Type: application/json" \
  -d '{"access_key": "test"}'
```

## Development Tips

### 1. Hot Reload

Flutter supports hot reload for quick development:
- Press `r` in terminal to hot reload
- Press `R` to hot restart
- Press `q` to quit

### 2. Debug Mode

Run in debug mode for better error messages:
```bash
flutter run --debug
```

### 3. Device Logs

View device logs:
```bash
# Android
flutter logs

# Or use adb
adb logcat | grep flutter
```

### 4. Network Monitoring

Use Flutter Inspector or add logging to monitor API calls:

```dart
// Add to your API service
print('API Call: $endpoint');
print('Request: $requestData');
print('Response: $responseData');
```

## Environment Switching

### Development vs Production

Create environment-specific configurations:

```dart
// lib/core/config/environment.dart
enum Environment { development, production }

class EnvironmentConfig {
  static const Environment current = Environment.development;
  
  static String get apiUrl {
    switch (current) {
      case Environment.development:
        return 'http://********/elite_quiz_admin';
      case Environment.production:
        return 'https://elitequiz.wrteam.me';
    }
  }
}
```

### Build Configurations

Create different build configurations:

```bash
# Development build
flutter run --flavor development

# Production build
flutter run --flavor production
```

## Performance Optimization

### 1. Enable Caching

The app uses `cached_network_image` for image caching. Ensure images are properly cached for better performance.

### 2. Optimize API Calls

- Implement proper error handling
- Add retry mechanisms
- Use pagination for large data sets

### 3. Local Storage

The app uses Hive for local storage. Data is automatically cached for offline access.

## Security Considerations

### Development Only

The current configuration allows HTTP traffic for development. For production:

1. **Use HTTPS only**
2. **Remove cleartext traffic permission**
3. **Update network security config**
4. **Use production Firebase project**

### API Security

- Implement proper authentication
- Use secure API keys
- Validate all inputs
- Implement rate limiting

## Next Steps

1. **Test all app features**
2. **Add sample data to database**
3. **Configure push notifications**
4. **Test on different devices**
5. **Prepare for production deployment**

Your Flutter app is now configured for local development! 🚀

## Quick Commands Reference

```bash
# Install dependencies
flutter pub get

# Run app
flutter run

# Generate icons
dart run flutter_launcher_icons

# Clean build
flutter clean

# Check doctor
flutter doctor

# List devices
flutter devices

# Build APK
flutter build apk --debug
```
