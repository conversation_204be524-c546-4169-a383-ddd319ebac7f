/*
 *  /MathJax/fonts/HTML-CSS/TeX/png/AMS/Regular/GreekAndCoptic.js
 *  
 *  Copyright (c) 2010-2013 The MathJax Consortium
 *
 *  Part of the MathJax library.
 *  See http://www.mathjax.org for details.
 * 
 *  Licensed under the Apache License, Version 2.0;
 *  you may not use this file except in compliance with the License.
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 */

MathJax.OutputJax["HTML-CSS"].defineImageData({MathJax_AMS:{989:[[5,6,2],[6,7,1],[8,8,2],[9,9,1],[10,11,2],[12,13,2],[15,15,2],[17,17,2],[20,19,2],[24,23,3],[29,27,3],[34,33,4],[40,38,5],[48,46,6]],1008:[[6,3,0],[7,4,0],[8,5,0],[9,5,0],[11,7,0],[13,8,0],[15,9,0],[18,10,0],[21,12,0],[25,15,0],[29,17,0],[35,21,0],[41,25,0],[49,29,0]]}});MathJax.Ajax.loadComplete(MathJax.OutputJax["HTML-CSS"].imgDir+"/AMS/Regular"+MathJax.OutputJax["HTML-CSS"].imgPacked+"/GreekAndCoptic.js");

