@echo off
echo ========================================
echo Guessy - Complete Local Development Setup
echo ========================================
echo.

:: Check if running as administrator
net session >nul 2>&1
if %errorLevel% == 0 (
    echo Running as Administrator... Good!
) else (
    echo Please run this script as Administrator for proper file permissions
    pause
    exit /b 1
)

:: Set variables for Guessy
set XAMPP_PATH=C:\xampp
set PROJECT_PATH=%cd%
set ADMIN_PATH=%XAMPP_PATH%\htdocs\guessy_admin

echo Current directory: %PROJECT_PATH%
echo XAMPP path: %XAMPP_PATH%
echo Guessy admin panel will be installed at: %ADMIN_PATH%
echo.

:: Check if XAMPP is installed
if not exist "%XAMPP_PATH%" (
    echo ERROR: XAMPP not found at %XAMPP_PATH%
    echo Please install XAMPP first from https://www.apachefriends.org/
    pause
    exit /b 1
)

:: Check if admin panel exists
if not exist "adminpanel2.3.4" (
    echo ERROR: adminpanel2.3.4 directory not found!
    echo Please make sure you're running this script from the project root.
    pause
    exit /b 1
)

echo Step 1: Configuring Guessy admin panel...
:: Run PHP configuration script for Guessy
php guessy_admin_config.php
if %errorLevel% neq 0 (
    echo ERROR: Failed to configure Guessy admin panel
    pause
    exit /b 1
)

echo.
echo Step 2: Copying Guessy admin panel to XAMPP...
:: Copy admin panel to XAMPP htdocs with Guessy name
if exist "%ADMIN_PATH%" (
    echo Removing existing Guessy admin panel...
    rmdir /s /q "%ADMIN_PATH%"
)

echo Copying Guessy files...
xcopy "adminpanel2.3.4" "%ADMIN_PATH%" /E /I /H /Y
if %errorLevel% neq 0 (
    echo ERROR: Failed to copy Guessy admin panel files
    pause
    exit /b 1
)

echo.
echo Step 3: Setting up Guessy database...
:: Start XAMPP services
echo Starting XAMPP services...
"%XAMPP_PATH%\xampp_start.exe"

:: Wait a moment for services to start
timeout /t 5 /nobreak >nul

:: Create Guessy database
echo Creating Guessy database...
"%XAMPP_PATH%\mysql\bin\mysql.exe" -u root -e "CREATE DATABASE IF NOT EXISTS guessy_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;"
if %errorLevel% neq 0 (
    echo WARNING: Could not create Guessy database automatically
    echo Please create database 'guessy_db' manually in phpMyAdmin
)

:: Import database schema
echo Importing Guessy database schema...
"%XAMPP_PATH%\mysql\bin\mysql.exe" -u root guessy_db < "adminpanel2.3.4\install\assets\quiz.php"
if %errorLevel% neq 0 (
    echo WARNING: Could not import database automatically
    echo Please import adminpanel2.3.4\install\assets\quiz.php manually in phpMyAdmin
)

:: Run Guessy-specific database setup
echo Setting up Guessy-specific data...
"%XAMPP_PATH%\mysql\bin\mysql.exe" -u root guessy_db < "guessy_database_setup.sql"
if %errorLevel% neq 0 (
    echo WARNING: Could not run Guessy database setup
    echo Please run guessy_database_setup.sql manually in phpMyAdmin
)

echo.
echo Step 4: Configuring Guessy Flutter app...
:: Update Flutter package name
dart update_flutter_package.dart
if %errorLevel% neq 0 (
    echo ERROR: Failed to update Flutter package name
    echo Please run: dart update_flutter_package.dart manually
)

:: Install Flutter dependencies
echo Installing Flutter dependencies...
cd elite_quiz_app-2.3.4
call flutter clean
call flutter pub get
if %errorLevel% neq 0 (
    echo ERROR: Failed to install Flutter dependencies
    echo Please run 'flutter pub get' manually in elite_quiz_app-2.3.4 directory
)

:: Generate app icons
call dart run flutter_launcher_icons
cd ..

echo.
echo Step 5: Creating Firebase configuration templates...
echo Creating Firebase templates for Guessy...
echo Please update these templates with your actual Firebase project details:
echo - elite_quiz_app-2.3.4/android/app/google-services.json.template
echo - elite_quiz_app-2.3.4/ios/Runner/GoogleService-Info.plist.template

echo.
echo ========================================
echo Guessy Setup Complete!
echo ========================================
echo.
echo 🎯 Guessy Configuration:
echo Admin Panel: http://localhost/guessy_admin
echo Login: guessy_admin / guessy123 (or admin / admin123)
echo Database: guessy_db
echo Package: com.guessy.quiz
echo App Name: Guessy
echo.
echo 📋 Next Steps:
echo 1. Create Firebase project: guessy-quiz-app
echo 2. Add Android app with package: com.guessy.quiz
echo 3. Download google-services.json and replace template
echo 4. Update Firebase configuration in admin panel
echo 5. Test Guessy admin panel: http://localhost/guessy_admin
echo 6. Run Guessy Flutter app: flutter run
echo.
echo 🔧 Network Configuration:
echo - Android Emulator: http://********/guessy_admin
echo - iOS Simulator: http://localhost/guessy_admin
echo - Physical Device: http://YOUR_IP/guessy_admin
echo.
echo 🎨 Guessy Features:
echo - Quiz Zone with multiple categories
echo - Word Puzzles and Brain Teasers
echo - Math Challenges
echo - Real-time Battles
echo - Daily Quizzes and Contests
echo - Coin Rewards System
echo - Achievement Badges
echo.
echo Troubleshooting:
echo - If admin panel doesn't load, check XAMPP Apache service
echo - If database errors occur, import schema manually from phpMyAdmin
echo - For API connectivity issues, check network security config
echo - Update IP address in Flutter config for physical device testing
echo.
echo Your Guessy quiz application is ready for development! 🎉
pause
