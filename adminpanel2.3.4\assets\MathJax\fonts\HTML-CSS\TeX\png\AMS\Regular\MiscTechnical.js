/*
 *  /MathJax/fonts/HTML-CSS/TeX/png/AMS/Regular/MiscTechnical.js
 *  
 *  Copyright (c) 2010-2013 The MathJax Consortium
 *
 *  Part of the MathJax library.
 *  See http://www.mathjax.org for details.
 * 
 *  Licensed under the Apache License, Version 2.0;
 *  you may not use this file except in compliance with the License.
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 */

MathJax.OutputJax["HTML-CSS"].defineImageData({MathJax_AMS:{8994:[[5,3,0],[6,3,0],[8,3,-1],[9,4,-1],[10,5,-1],[12,6,-1],[15,6,-2],[17,7,-2],[20,8,-3],[24,9,-4],[29,11,-4],[34,13,-5],[40,15,-6],[48,17,-8]],8995:[[5,2,-1],[6,3,-1],[8,3,-1],[9,4,-1],[11,4,-2],[13,5,-2],[15,5,-3],[17,6,-3],[21,8,-3],[24,9,-4],[29,10,-5],[34,12,-6],[40,14,-7],[48,16,-9]]}});MathJax.Ajax.loadComplete(MathJax.OutputJax["HTML-CSS"].imgDir+"/AMS/Regular"+MathJax.OutputJax["HTML-CSS"].imgPacked+"/MiscTechnical.js");

