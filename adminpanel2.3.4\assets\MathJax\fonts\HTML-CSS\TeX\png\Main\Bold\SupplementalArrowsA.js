/*
 *  /MathJax/fonts/HTML-CSS/TeX/png/Main/Bold/SupplementalArrowsA.js
 *  
 *  Copyright (c) 2010-2013 The MathJax Consortium
 *
 *  Part of the MathJax library.
 *  See http://www.mathjax.org for details.
 * 
 *  Licensed under the Apache License, Version 2.0;
 *  you may not use this file except in compliance with the License.
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 */

MathJax.OutputJax["HTML-CSS"].defineImageData({"MathJax_Main-bold":{10229:[[13,5,1],[15,5,0],[18,7,1],[21,7,1],[25,9,1],[29,10,1],[34,11,1],[41,13,1],[49,16,1],[58,18,1],[69,22,1],[81,25,1],[97,31,2],[115,36,2]],10230:[[13,5,1],[15,5,0],[18,7,1],[21,7,1],[25,9,1],[30,9,0],[35,11,1],[42,14,1],[49,16,1],[59,18,1],[70,22,1],[83,26,1],[98,30,1],[117,36,1]],10231:[[15,5,1],[18,5,0],[21,7,1],[25,7,1],[29,9,1],[35,10,1],[41,11,1],[48,14,1],[57,16,1],[68,18,1],[81,22,1],[96,26,1],[114,30,1],[136,36,1]],10232:[[13,5,1],[16,6,1],[18,7,1],[22,8,1],[26,9,1],[31,10,1],[36,13,1],[42,14,1],[50,18,2],[60,21,2],[71,24,2],[85,29,3],[101,34,3],[120,40,3]],10233:[[13,5,1],[15,6,1],[18,7,1],[22,8,1],[25,9,1],[30,11,1],[36,12,1],[42,14,1],[50,18,2],[60,21,2],[71,24,2],[84,28,2],[100,34,3],[119,40,3]],10234:[[15,5,1],[18,6,1],[21,7,1],[25,8,1],[29,9,1],[35,10,1],[41,13,1],[48,15,2],[57,17,2],[68,21,2],[81,24,2],[96,29,3],[114,33,3],[136,41,4]],10236:[[13,5,1],[15,5,0],[18,7,1],[21,7,1],[25,9,1],[30,9,0],[35,11,1],[41,14,1],[49,16,1],[59,18,1],[70,22,1],[82,26,1],[98,30,1],[116,36,1]]}});MathJax.Ajax.loadComplete(MathJax.OutputJax["HTML-CSS"].imgDir+"/Main/Bold"+MathJax.OutputJax["HTML-CSS"].imgPacked+"/SupplementalArrowsA.js");

