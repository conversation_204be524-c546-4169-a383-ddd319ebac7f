/*
 *  /MathJax/fonts/HTML-CSS/TeX/png/Main/Regular/LatinExtendedB.js
 *  
 *  Copyright (c) 2010-2013 The MathJax Consortium
 *
 *  Part of the MathJax library.
 *  See http://www.mathjax.org for details.
 * 
 *  Licensed under the Apache License, Version 2.0;
 *  you may not use this file except in compliance with the License.
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 */

MathJax.OutputJax["HTML-CSS"].defineImageData({MathJax_Main:{567:[[3,4,1],[3,6,2],[4,7,2],[4,7,2],[5,9,3],[5,12,4],[7,13,4],[8,16,5],[9,19,6],[10,22,7],[12,26,8],[14,30,9],[17,36,11],[19,44,13]]}});MathJax.Ajax.loadComplete(MathJax.OutputJax["HTML-CSS"].imgDir+"/Main/Regular"+MathJax.OutputJax["HTML-CSS"].imgPacked+"/LatinExtendedB.js");

