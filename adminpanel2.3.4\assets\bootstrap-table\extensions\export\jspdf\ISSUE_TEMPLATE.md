Feature requests, bug reports etc. are very welcome as issues. But questions are directed to stackoverflow with the tag `jspdf`.

If you are facing issues with garbled Unicode characters, please refer to [#2677](https://github.com/MrRio/jsPDF/issues/2677).

Note that new issues should follow these guidelines. Otherwise, the issue will be closed without a comment and tagged with the "Needs Information" label.

1. A bug should be reported as an [mcve](https://stackoverflow.com/help/mcve).
2. Make sure code is properly indented and [formatted](https://help.github.com/articles/basic-writing-and-formatting-syntax/#quoting-code) (Use ``` around code blocks).
3. Provide a runnable example. Optimally, a link to an example that runs directly in the browser (JSFiddle, CodePen, etc.). Please don't share framework-specific code such as React components, unless strictly necessary to reproduce the issue. Try to isolate the code as much as possible and use only plain JS/HTML/CSS.
4. Try to make sure and show in your issue that the issue is actually related to jspdf and not your framework of choice or your setup.
5. Read and follow the [contribution guidelines](https://github.com/MrRio/jsPDF/blob/master/CONTRIBUTING.md#reporting-bugs).
6. To make sure you have read this, delete this template and start the issue description with "I have read and understood the contribution guidelines.".
