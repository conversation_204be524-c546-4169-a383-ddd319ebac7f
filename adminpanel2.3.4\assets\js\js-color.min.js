!function(e,t){"use strict";"object"!=typeof module||"object"!=typeof module.exports?t(e):module.exports=e.document?t(e):function(e){if(!e.document)throw new Error("jscolor needs a window with document");return t(e)}}("undefined"!=typeof window?window:this,function(w){"use strict";var e,t,y,t=((y={initialized:!1,instances:[],readyQueue:[],register:function(){void 0!==w&&w.document&&w.document.addEventListener("DOMContentLoaded",y.pub.init,!1)},installBySelector:function(e,t){if(!(t=t?y.node(t):w.document))throw new Error("Missing root node");for(var r,n,o=t.querySelectorAll(e),i=new RegExp("(^|\\s)("+y.pub.lookupClass+")(\\s*(\\{[^}]*\\})|\\s|$)","i"),a=0;a<o.length;a+=1)if(!(o[a].jscolor&&o[a].jscolor instanceof y.pub))if(void 0===o[a].type||"color"!=o[a].type.toLowerCase()||!y.isColorAttrSupported)if(null!==(r=y.getDataAttr(o[a],"jscolor"))||o[a].className&&(n=o[a].className.match(i))){var s=o[a],l="";null!==r?l=r:n&&(console.warn('Installation using class name is DEPRECATED. Use data-jscolor="" attribute instead.'+y.docsRef),n[4]&&(l=n[4]));var h=null;if(l.trim())try{h=y.parseOptionsStr(l)}catch(e){console.warn(e+"\n"+l)}try{new y.pub(s,h)}catch(e){console.warn(e)}}},parseOptionsStr:function(t){var r=null;try{r=JSON.parse(t)}catch(e){if(!y.pub.looseJSON)throw new Error("Could not parse jscolor options as JSON: "+e);try{r=new Function("var opts = ("+t+'); return typeof opts === "object" ? opts : {};')()}catch(e){throw new Error("Could not evaluate jscolor options: "+e)}}return r},getInstances:function(){for(var e=[],t=0;t<y.instances.length;t+=1)y.instances[t]&&y.instances[t].targetElement&&e.push(y.instances[t]);return e},createEl:function(e){e=w.document.createElement(e);return y.setData(e,"gui",!0),e},node:function(e){if(!e)return null;if("string"!=typeof e)return y.isNode(e)?e:(console.warn("Invalid node of type %s: %s",typeof e,e),null);var t=e,r=null;try{r=w.document.querySelector(t)}catch(e){return console.warn(e),null}return r||console.warn("No element matches the selector: %s",t),r},isNode:function(e){return"object"==typeof Node?e instanceof Node:e&&"object"==typeof e&&"number"==typeof e.nodeType&&"string"==typeof e.nodeName},nodeName:function(e){return!(!e||!e.nodeName)&&e.nodeName.toLowerCase()},removeChildren:function(e){for(;e.firstChild;)e.removeChild(e.firstChild)},isTextInput:function(e){return e&&"input"===y.nodeName(e)&&"text"===e.type.toLowerCase()},isButton:function(e){if(!e)return!1;var t=y.nodeName(e);return"button"===t||"input"===t&&-1<["button","submit","reset"].indexOf(e.type.toLowerCase())},isButtonEmpty:function(e){switch(y.nodeName(e)){case"input":return!e.value||""===e.value.trim();case"button":return""===e.textContent.trim()}return null},isPassiveEventSupported:function(){var e=!1;try{var t=Object.defineProperty({},"passive",{get:function(){e=!0}});w.addEventListener("testPassive",null,t),w.removeEventListener("testPassive",null,t)}catch(e){}return e}(),isColorAttrSupported:!(!(t=w.document.createElement("input")).setAttribute||(t.setAttribute("type","color"),"color"!=t.type.toLowerCase())),dataProp:"_data_jscolor",setData:function(){var e=arguments[0];if(3===arguments.length){var t=e.hasOwnProperty(y.dataProp)?e[y.dataProp]:e[y.dataProp]={},r=arguments[2];return t[n=arguments[1]]=r,!0}if(2!==arguments.length||"object"!=typeof arguments[1])throw new Error("Invalid arguments");var n,t=e.hasOwnProperty(y.dataProp)?e[y.dataProp]:e[y.dataProp]={},o=arguments[1];for(n in o)o.hasOwnProperty(n)&&(t[n]=o[n]);return!0},removeData:function(){var e=arguments[0];if(!e.hasOwnProperty(y.dataProp))return!0;for(var t=1;t<arguments.length;t+=1){var r=arguments[t];delete e[y.dataProp][r]}return!0},getData:function(e,t,r){if(!e.hasOwnProperty(y.dataProp)){if(void 0===r)return;e[y.dataProp]={}}e=e[y.dataProp];return e.hasOwnProperty(t)||void 0===r||(e[t]=r),e[t]},getDataAttr:function(e,t){t="data-"+t;return e.getAttribute(t)},setDataAttr:function(e,t,r){t="data-"+t;e.setAttribute(t,r)},_attachedGroupEvents:{},attachGroupEvent:function(e,t,r,n){y._attachedGroupEvents.hasOwnProperty(e)||(y._attachedGroupEvents[e]=[]),y._attachedGroupEvents[e].push([t,r,n]),t.addEventListener(r,n,!1)},detachGroupEvents:function(e){if(y._attachedGroupEvents.hasOwnProperty(e)){for(var t=0;t<y._attachedGroupEvents[e].length;t+=1){var r=y._attachedGroupEvents[e][t];r[0].removeEventListener(r[1],r[2],!1)}delete y._attachedGroupEvents[e]}},preventDefault:function(e){e.preventDefault&&e.preventDefault(),e.returnValue=!1},captureTarget:function(e){e.setCapture&&(y._capturedTarget=e,y._capturedTarget.setCapture())},releaseTarget:function(){y._capturedTarget&&(y._capturedTarget.releaseCapture(),y._capturedTarget=null)},triggerEvent:function(e,t,r,n){if(e){var o=null;return("function"==typeof Event?o=new Event(t,{bubbles:r,cancelable:n}):(o=w.document.createEvent("Event")).initEvent(t,r,n),o)?(y.setData(o,"internal",!0),e.dispatchEvent(o),!0):!1}},triggerInputEvent:function(e,t,r,n){e&&y.isTextInput(e)&&y.triggerEvent(e,t,r,n)},eventKey:function(e){var t={9:"Tab",13:"Enter",27:"Escape"};return"string"==typeof e.code?e.code:void 0!==e.keyCode&&t.hasOwnProperty(e.keyCode)?t[e.keyCode]:null},strList:function(e){return e?e.replace(/^\s+|\s+$/g,"").split(/\s+/):[]},hasClass:function(e,t){return!!t&&(void 0!==e.classList?e.classList.contains(t):-1!=(" "+e.className.replace(/\s+/g," ")+" ").indexOf(" "+t+" "))},addClass:function(e,t){var r=y.strList(t);if(void 0===e.classList)for(n=0;n<r.length;n+=1)y.hasClass(e,r[n])||(e.className+=(e.className?" ":"")+r[n]);else for(var n=0;n<r.length;n+=1)e.classList.add(r[n])},removeClass:function(e,t){var r=y.strList(t);if(void 0===e.classList)for(o=0;o<r.length;o+=1){var n=new RegExp("^\\s*"+r[o]+"\\s*|\\s*"+r[o]+"\\s*$|\\s+"+r[o]+"(\\s+)","g");e.className=e.className.replace(n,"$1")}else for(var o=0;o<r.length;o+=1)e.classList.remove(r[o])},getCompStyle:function(e){e=w.getComputedStyle?w.getComputedStyle(e):e.currentStyle;return e||{}},setStyle:function(e,t,r,n){var o,i,a=r?"important":"",s=null;for(o in t)t.hasOwnProperty(o)&&((i=null)===t[o]?(s=s||y.getData(e,"origStyle"))&&s.hasOwnProperty(o)&&(i=s[o]):(n&&((s=s||y.getData(e,"origStyle",{})).hasOwnProperty(o)||(s[o]=e.style[o])),i=t[o]),null!==i&&e.style.setProperty(o,i,a))},hexColor:function(e,t,r){return"#"+(("0"+Math.round(e).toString(16)).substr(-2)+("0"+Math.round(t).toString(16)).substr(-2)+("0"+Math.round(r).toString(16)).substr(-2)).toUpperCase()},hexaColor:function(e,t,r,n){return"#"+(("0"+Math.round(e).toString(16)).substr(-2)+("0"+Math.round(t).toString(16)).substr(-2)+("0"+Math.round(r).toString(16)).substr(-2)+("0"+Math.round(255*n).toString(16)).substr(-2)).toUpperCase()},rgbColor:function(e,t,r){return"rgb("+Math.round(e)+","+Math.round(t)+","+Math.round(r)+")"},rgbaColor:function(e,t,r,n){return"rgba("+Math.round(e)+","+Math.round(t)+","+Math.round(r)+","+Math.round(100*(null==n?1:n))/100+")"},linearGradient:(e=function(){for(var e="linear-gradient",t=["","-webkit-","-moz-","-o-","-ms-"],r=w.document.createElement("div"),n=0;n<t.length;n+=1){var o=t[n]+e,i=o+"(to right, rgba(0,0,0,0), rgba(0,0,0,0))";if(r.style.background=i,r.style.background)return o}return e}(),function(){return e+"("+Array.prototype.join.call(arguments,", ")+")"}),setBorderRadius:function(e,t){y.setStyle(e,{"border-radius":t||"0"})},setBoxShadow:function(e,t){y.setStyle(e,{"box-shadow":t||"none"})},getElementPos:function(e,t){var r=0,n=0,e=e.getBoundingClientRect(),r=e.left,n=e.top;return t||(r+=(t=y.getViewPos())[0],n+=t[1]),[r,n]},getElementSize:function(e){return[e.offsetWidth,e.offsetHeight]},getAbsPointerPos:function(e){var t=0,r=0;return void 0!==e.changedTouches&&e.changedTouches.length?(t=e.changedTouches[0].clientX,r=e.changedTouches[0].clientY):"number"==typeof e.clientX&&(t=e.clientX,r=e.clientY),{x:t,y:r}},getRelPointerPos:function(e){var t=(e.target||e.srcElement).getBoundingClientRect(),r=0,n=0;return void 0!==e.changedTouches&&e.changedTouches.length?(r=e.changedTouches[0].clientX,n=e.changedTouches[0].clientY):"number"==typeof e.clientX&&(r=e.clientX,n=e.clientY),{x:r-t.left,y:n-t.top}},getViewPos:function(){var e=w.document.documentElement;return[(w.pageXOffset||e.scrollLeft)-(e.clientLeft||0),(w.pageYOffset||e.scrollTop)-(e.clientTop||0)]},getViewSize:function(){var e=w.document.documentElement;return[w.innerWidth||e.clientWidth,w.innerHeight||e.clientHeight]},RGB_HSV:function(e,t,r){e/=255,t/=255,r/=255;var n=Math.min(Math.min(e,t),r),o=Math.max(Math.max(e,t),r),i=o-n;if(0==i)return[null,0,100*o];e=e===n?3+(r-t)/i:t===n?5+(e-r)/i:1+(t-e)/i;return[60*(6==e?0:e),i/o*100,100*o]},HSV_RGB:function(e,t,r){var n=r/100*255;if(null===e)return[n,n,n];e/=60,t/=100;var o=Math.floor(e),r=n*(1-t),e=n*(1-t*(o%2?e-o:1-(e-o)));switch(o){case 6:case 0:return[n,e,r];case 1:return[e,n,r];case 2:return[r,n,e];case 3:return[r,e,n];case 4:return[e,r,n];case 5:return[n,r,e]}},parseColorString:function(e){var t={rgba:null,format:null};if(a=e.match(/^\W*([0-9A-F]{3,8})\W*$/i)){if(8===a[1].length)t.format="hexa",t.rgba=[parseInt(a[1].substr(0,2),16),parseInt(a[1].substr(2,2),16),parseInt(a[1].substr(4,2),16),parseInt(a[1].substr(6,2),16)/255];else if(6===a[1].length)t.format="hex",t.rgba=[parseInt(a[1].substr(0,2),16),parseInt(a[1].substr(2,2),16),parseInt(a[1].substr(4,2),16),null];else{if(3!==a[1].length)return!1;t.format="hex",t.rgba=[parseInt(a[1].charAt(0)+a[1].charAt(0),16),parseInt(a[1].charAt(1)+a[1].charAt(1),16),parseInt(a[1].charAt(2)+a[1].charAt(2),16),null]}return t}if(a=e.match(/^\W*rgba?\(([^)]*)\)\W*$/i)){var r,n,o,i,e=a[1].split(","),a=/^\s*(\d+|\d*\.\d+|\d+\.\d*)\s*$/;if(3<=e.length&&(r=e[0].match(a))&&(n=e[1].match(a))&&(o=e[2].match(a)))return t.format="rgb",t.rgba=[parseFloat(r[1])||0,parseFloat(n[1])||0,parseFloat(o[1])||0,null],4<=e.length&&(i=e[3].match(a))&&(t.format="rgba",t.rgba[3]=parseFloat(i[1])||0),t}return!1},parsePaletteValue:function(e){var t=[];"string"==typeof e?e.replace(/#[0-9A-F]{3}([0-9A-F]{3})?|rgba?\(([^)]*)\)/gi,function(e){t.push(e)}):Array.isArray(e)&&(t=e);for(var r=[],n=0;n<t.length;n++){var o=y.parseColorString(t[n]);o&&r.push(o)}return r},containsTranparentColor:function(e){for(var t=0;t<e.length;t++){var r=e[t].rgba[3];if(null!==r&&r<1)return!0}return!1},isAlphaFormat:function(e){switch(e.toLowerCase()){case"hexa":case"rgba":return!0}return!1},scaleCanvasForHighDPR:function(e){var t=w.devicePixelRatio||1;e.width*=t,e.height*=t,e.getContext("2d").scale(t,t)},genColorPreviewCanvas:function(e,t,r,n){var o=Math.round(y.pub.previewSeparator.length),i=y.pub.chessboardSize,a=y.pub.chessboardColor1,s=y.pub.chessboardColor2,l=r||2*i,h=2*i,r=y.createEl("canvas"),d=r.getContext("2d");r.width=l,r.height=h,n&&y.scaleCanvasForHighDPR(r),d.fillStyle=a,d.fillRect(0,0,l,h),d.fillStyle=s;for(var p=0;p<l;p+=2*i)d.fillRect(p,0,i,i),d.fillRect(p+i,i,i,i);e&&(d.fillStyle=e,d.fillRect(0,0,l,h));var c=null;switch(t){case"left":c=0,d.clearRect(0,0,o/2,h);break;case"right":c=l-o,d.clearRect(l-o/2,0,o/2,h)}if(null!==c){d.lineWidth=1;for(var u=0;u<y.pub.previewSeparator.length;u+=1)d.beginPath(),d.strokeStyle=y.pub.previewSeparator[u],d.moveTo(.5+c+u,0),d.lineTo(.5+c+u,h),d.stroke()}return{canvas:r,width:l,height:h}},genColorPreviewGradient:function(e,t,r){var n=[],n=t&&r?["to "+{left:"right",right:"left"}[t],e+" 0%",e+" "+r+"px","rgba(0,0,0,0) "+(r+1)+"px","rgba(0,0,0,0) 100%"]:["to right",e+" 0%",e+" 100%"];return y.linearGradient.apply(this,n)},redrawPosition:function(){if(y.picker&&y.picker.owner){var e,t=y.picker.owner,r=t.fixed?(e=y.getElementPos(t.targetElement,!0),[0,0]):(e=y.getElementPos(t.targetElement),y.getViewPos()),n=y.getElementSize(t.targetElement),o=y.getViewSize(),i=y.getPickerDims(t),a=[i.borderW,i.borderH];switch(t.position.toLowerCase()){case"left":h=0,s=-(l=1);break;case"right":h=0,s=l=1;break;case"top":l=0,s=-(h=1);break;default:l=0,s=h=1}var i=(n[h]+a[h])/2,s=(i=t.smartPosition?[-r[l]+e[l]+a[l]>o[l]&&-r[l]+e[l]+n[l]/2>o[l]/2&&0<=e[l]+n[l]-a[l]?e[l]+n[l]-a[l]:e[l],-r[h]+e[h]+n[h]+a[h]-i+i*s>o[h]?-r[h]+e[h]+n[h]/2>o[h]/2&&0<=e[h]+n[h]-i-i*s?e[h]+n[h]-i-i*s:e[h]+n[h]-i+i*s:0<=e[h]+n[h]-i+i*s?e[h]+n[h]-i+i*s:e[h]+n[h]-i-i*s]:[e[l],e[h]+n[h]-i+i*s])[l],l=i[h],h=t.fixed?"fixed":"absolute",n=(i[0]+a[0]>e[0]||i[0]<e[0]+n[0])&&i[1]+a[1]<e[1]+n[1];y._drawPosition(t,s,l,h,n)}},_drawPosition:function(e,t,r,n,o){o=o?0:e.shadowBlur;y.picker.wrap.style.position=n,y.picker.wrap.style.left=t+"px",y.picker.wrap.style.top=r+"px",y.setBoxShadow(y.picker.boxS,e.shadow?new y.BoxShadow(0,o,e.shadowBlur,0,e.shadowColor):null)},getPickerDims:function(e){var t=2*e.controlBorderWidth+e.width,r=2*e.controlBorderWidth+e.height,n=2*e.controlBorderWidth+2*y.getControlPadding(e)+e.sliderSize;y.getSliderChannel(e)&&(t+=n),e.hasAlphaChannel()&&(t+=n);var o=y.getPaletteDims(e,t);o.height&&(r+=o.height+e.padding),e.closeButton&&(r+=2*e.controlBorderWidth+e.padding+e.buttonHeight);var i=t+2*e.padding,n=r+2*e.padding;return{contentW:t,contentH:r,paddedW:i,paddedH:n,borderW:i+2*e.borderWidth,borderH:n+2*e.borderWidth,palette:o}},getPaletteDims:function(e,t){var r=0,n=0,o=0,i=0,a=0,s=e._palette?e._palette.length:0;return s&&(n=0<(r=e.paletteCols)?Math.ceil(s/r):0,o=Math.max(1,Math.floor((t-(r-1)*e.paletteSpacing)/r)),i=e.paletteHeight?Math.min(e.paletteHeight,o):o),n&&(a=n*i+(n-1)*e.paletteSpacing),{cols:r,rows:n,cellW:o,cellH:i,width:t,height:a}},getControlPadding:function(e){return Math.max(e.padding/2,2*e.pointerBorderWidth+e.pointerThickness-e.controlBorderWidth)},getPadYChannel:function(e){return"v"===e.mode.charAt(1).toLowerCase()?"v":"s"},getSliderChannel:function(e){if(2<e.mode.length)switch(e.mode.charAt(2).toLowerCase()){case"s":return"s";case"v":return"v"}return null},triggerCallback:function(e,t){if(e[t]){var r=null;if("string"==typeof e[t])try{r=new Function(e[t])}catch(e){console.error(e)}else r=e[t];r&&r.call(e)}},triggerGlobal:function(e){for(var t=y.getInstances(),r=0;r<t.length;r+=1)t[r].trigger(e)},_pointerMoveEvent:{mouse:"mousemove",touch:"touchmove"},_pointerEndEvent:{mouse:"mouseup",touch:"touchend"},_pointerOrigin:null,_capturedTarget:null,onDocumentKeyUp:function(e){-1!==["Tab","Escape"].indexOf(y.eventKey(e))&&y.picker&&y.picker.owner&&y.picker.owner.tryHide()},onWindowResize:function(e){y.redrawPosition()},onWindowScroll:function(e){y.redrawPosition()},onParentScroll:function(e){y.picker&&y.picker.owner&&y.picker.owner.tryHide()},onDocumentMouseDown:function(e){var t=e.target||e.srcElement;t.jscolor&&t.jscolor instanceof y.pub?t.jscolor.showOnClick&&!t.disabled&&t.jscolor.show():y.getData(t,"gui")?y.getData(t,"control")&&y.onControlPointerStart(e,t,y.getData(t,"control"),"mouse"):y.picker&&y.picker.owner&&y.picker.owner.tryHide()},onPickerTouchStart:function(e){var t=e.target||e.srcElement;y.getData(t,"control")&&y.onControlPointerStart(e,t,y.getData(t,"control"),"touch")},onControlPointerStart:function(r,n,o,i){var e=y.getData(n,"instance");y.preventDefault(r),y.captureTarget(n);var t=function(e,t){y.attachGroupEvent("drag",e,y._pointerMoveEvent[i],y.onDocumentPointerMove(r,n,o,i,t)),y.attachGroupEvent("drag",e,y._pointerEndEvent[i],y.onDocumentPointerEnd(r,n,o,i))};t(w.document,[0,0]),w.parent&&w.frameElement&&(a=[-(a=w.frameElement.getBoundingClientRect()).left,-a.top],t(w.parent.window.document,a));var t=y.getAbsPointerPos(r),a=y.getRelPointerPos(r);switch(y._pointerOrigin={x:t.x-a.x,y:t.y-a.y},o){case"pad":"v"===y.getSliderChannel(e)&&0===e.channels.v&&e.fromHSVA(null,null,100,null),y.setPad(e,r,0,0);break;case"sld":y.setSld(e,r,0);break;case"asld":y.setASld(e,r,0)}e.trigger("input")},onDocumentPointerMove:function(e,r,n,t,o){return function(e){var t=y.getData(r,"instance");switch(n){case"pad":y.setPad(t,e,o[0],o[1]);break;case"sld":y.setSld(t,e,o[1]);break;case"asld":y.setASld(t,e,o[1])}t.trigger("input")}},onDocumentPointerEnd:function(e,r,t,n){return function(e){var t=y.getData(r,"instance");y.detachGroupEvents("drag"),y.releaseTarget(),t.trigger("input"),t.trigger("change")}},onPaletteSampleClick:function(e){var t=e.currentTarget,e=y.getData(t,"instance"),t=y.getData(t,"color");"any"===e.format.toLowerCase()&&(e._setFormat(t.format),y.isAlphaFormat(e.getFormat())||(t.rgba[3]=1)),null===t.rgba[3]&&(!0===e.paletteSetsAlpha||"auto"===e.paletteSetsAlpha&&e._paletteHasTransparency)&&(t.rgba[3]=1),e.fromRGBA.apply(e,t.rgba),e.trigger("input"),e.trigger("change"),e.hideOnPaletteClick&&e.hide()},setPad:function(e,t,r,n){t=y.getAbsPointerPos(t),r=r+t.x-y._pointerOrigin.x-e.padding-e.controlBorderWidth,t=n+t.y-y._pointerOrigin.y-e.padding-e.controlBorderWidth,r*=360/(e.width-1),t=100-t*(100/(e.height-1));switch(y.getPadYChannel(e)){case"s":e.fromHSVA(r,t,null,null);break;case"v":e.fromHSVA(r,null,t,null)}},setSld:function(e,t,r){t=100-(r+y.getAbsPointerPos(t).y-y._pointerOrigin.y-e.padding-e.controlBorderWidth)*(100/(e.height-1));switch(y.getSliderChannel(e)){case"s":e.fromHSVA(null,t,null,null);break;case"v":e.fromHSVA(null,null,t,null)}},setASld:function(e,t,r){r=1-(r+y.getAbsPointerPos(t).y-y._pointerOrigin.y-e.padding-e.controlBorderWidth)*(1/(e.height-1));r<1&&(t=e.getFormat(),"any"!==e.format.toLowerCase()||y.isAlphaFormat(t)||e._setFormat("hex"===t?"hexa":"rgba")),e.fromHSVA(null,null,null,r)},createPadCanvas:function(){var e={elm:null,draw:null},n=y.createEl("canvas"),o=n.getContext("2d");return e.elm=n,e.draw=function(e,t,r){n.width=e,n.height=t,o.clearRect(0,0,n.width,n.height);t=o.createLinearGradient(0,0,n.width,0);t.addColorStop(0,"#F00"),t.addColorStop(1/6,"#FF0"),t.addColorStop(2/6,"#0F0"),t.addColorStop(.5,"#0FF"),t.addColorStop(4/6,"#00F"),t.addColorStop(5/6,"#F0F"),t.addColorStop(1,"#F00"),o.fillStyle=t,o.fillRect(0,0,n.width,n.height);t=o.createLinearGradient(0,0,0,n.height);switch(r.toLowerCase()){case"s":t.addColorStop(0,"rgba(255,255,255,0)"),t.addColorStop(1,"rgba(255,255,255,1)");break;case"v":t.addColorStop(0,"rgba(0,0,0,0)"),t.addColorStop(1,"rgba(0,0,0,1)")}o.fillStyle=t,o.fillRect(0,0,n.width,n.height)},e},createSliderGradient:function(){var e={elm:null,draw:null},o=y.createEl("canvas"),i=o.getContext("2d");return e.elm=o,e.draw=function(e,t,r,n){o.width=e,o.height=t,i.clearRect(0,0,o.width,o.height);t=i.createLinearGradient(0,0,0,o.height);t.addColorStop(0,r),t.addColorStop(1,n),i.fillStyle=t,i.fillRect(0,0,o.width,o.height)},e},createASliderGradient:function(){var e={elm:null,draw:null},a=y.createEl("canvas"),s=a.getContext("2d");return e.elm=a,e.draw=function(e,t,r){a.width=e,a.height=t,s.clearRect(0,0,a.width,a.height);var n=a.width/2,t=y.pub.chessboardColor1,o=y.pub.chessboardColor2;if(s.fillStyle=t,s.fillRect(0,0,a.width,a.height),0<n)for(var i=0;i<a.height;i+=2*n)s.fillStyle=o,s.fillRect(0,i,n,n),s.fillRect(n,i+n,n,n);t=s.createLinearGradient(0,0,0,a.height);t.addColorStop(0,r),t.addColorStop(1,"rgba(0,0,0,0)"),s.fillStyle=t,s.fillRect(0,0,a.width,a.height)},e},BoxShadow:(r.prototype.toString=function(){var e=[Math.round(this.hShadow)+"px",Math.round(this.vShadow)+"px",Math.round(this.blur)+"px",Math.round(this.spread)+"px",this.color];return this.inset&&e.push("inset"),e.join(" ")},r),flags:{leaveValue:1,leaveAlpha:2,leavePreview:4},enumOpts:{format:["auto","any","hex","hexa","rgb","rgba"],previewPosition:["left","right"],mode:["hsv","hvs","hs","hv"],position:["left","right","top","bottom"],alphaChannel:["auto",!0,!1],paletteSetsAlpha:["auto",!0,!1]},deprecatedOpts:{styleElement:"previewElement",onFineChange:"onInput",overwriteImportant:"forceStyle",closable:"closeButton",insetWidth:"controlBorderWidth",insetColor:"controlBorderColor",refine:null},docsRef:" See https://jscolor.com/docs/",pub:function(e,t){var f=this;function n(e,t){if("string"!=typeof e)throw new Error("Invalid value for option name: "+e);if(y.enumOpts.hasOwnProperty(e)&&("string"==typeof t&&(t=t.toLowerCase()),-1===y.enumOpts[e].indexOf(t)))throw new Error("Option '"+e+"' has invalid value: "+t);if(y.deprecatedOpts.hasOwnProperty(e)){var r=e,n=y.deprecatedOpts[e];if(!n)throw new Error("Option '"+e+"' is DEPRECATED");console.warn("Option '%s' is DEPRECATED, using '%s' instead."+y.docsRef,r,n),e=n}n="set__"+e;if("function"==typeof f[n])return f[n](t),1;if(e in f)return f[e]=t,1;throw new Error("Unrecognized configuration option: "+e)}function r(){f._processParentElementsInDOM(),y.picker||(y.picker={owner:null,wrap:y.createEl("div"),box:y.createEl("div"),boxS:y.createEl("div"),boxB:y.createEl("div"),pad:y.createEl("div"),padB:y.createEl("div"),padM:y.createEl("div"),padCanvas:y.createPadCanvas(),cross:y.createEl("div"),crossBY:y.createEl("div"),crossBX:y.createEl("div"),crossLY:y.createEl("div"),crossLX:y.createEl("div"),sld:y.createEl("div"),sldB:y.createEl("div"),sldM:y.createEl("div"),sldGrad:y.createSliderGradient(),sldPtrS:y.createEl("div"),sldPtrIB:y.createEl("div"),sldPtrMB:y.createEl("div"),sldPtrOB:y.createEl("div"),asld:y.createEl("div"),asldB:y.createEl("div"),asldM:y.createEl("div"),asldGrad:y.createASliderGradient(),asldPtrS:y.createEl("div"),asldPtrIB:y.createEl("div"),asldPtrMB:y.createEl("div"),asldPtrOB:y.createEl("div"),pal:y.createEl("div"),btn:y.createEl("div"),btnT:y.createEl("span")},y.picker.pad.appendChild(y.picker.padCanvas.elm),y.picker.padB.appendChild(y.picker.pad),y.picker.cross.appendChild(y.picker.crossBY),y.picker.cross.appendChild(y.picker.crossBX),y.picker.cross.appendChild(y.picker.crossLY),y.picker.cross.appendChild(y.picker.crossLX),y.picker.padB.appendChild(y.picker.cross),y.picker.box.appendChild(y.picker.padB),y.picker.box.appendChild(y.picker.padM),y.picker.sld.appendChild(y.picker.sldGrad.elm),y.picker.sldB.appendChild(y.picker.sld),y.picker.sldB.appendChild(y.picker.sldPtrOB),y.picker.sldPtrOB.appendChild(y.picker.sldPtrMB),y.picker.sldPtrMB.appendChild(y.picker.sldPtrIB),y.picker.sldPtrIB.appendChild(y.picker.sldPtrS),y.picker.box.appendChild(y.picker.sldB),y.picker.box.appendChild(y.picker.sldM),y.picker.asld.appendChild(y.picker.asldGrad.elm),y.picker.asldB.appendChild(y.picker.asld),y.picker.asldB.appendChild(y.picker.asldPtrOB),y.picker.asldPtrOB.appendChild(y.picker.asldPtrMB),y.picker.asldPtrMB.appendChild(y.picker.asldPtrIB),y.picker.asldPtrIB.appendChild(y.picker.asldPtrS),y.picker.box.appendChild(y.picker.asldB),y.picker.box.appendChild(y.picker.asldM),y.picker.box.appendChild(y.picker.pal),y.picker.btn.appendChild(y.picker.btnT),y.picker.box.appendChild(y.picker.btn),y.picker.boxB.appendChild(y.picker.box),y.picker.wrap.appendChild(y.picker.boxS),y.picker.wrap.appendChild(y.picker.boxB),y.picker.wrap.addEventListener("touchstart",y.onPickerTouchStart,!!y.isPassiveEventSupported&&{passive:!1}));var e=y.picker,t=!!y.getSliderChannel(f),r=f.hasAlphaChannel(),n=y.getPickerDims(f),o=2*f.pointerBorderWidth+f.pointerThickness+2*f.crossSize,i=y.getControlPadding(f),a=Math.min(f.borderRadius,Math.round(f.padding*Math.PI));e.wrap.className="jscolor-picker-wrap",e.wrap.style.clear="both",e.wrap.style.width=n.borderW+"px",e.wrap.style.height=n.borderH+"px",e.wrap.style.zIndex=f.zIndex,e.box.className="jscolor-picker",e.box.style.width=n.paddedW+"px",e.box.style.height=n.paddedH+"px",e.box.style.position="relative",e.boxS.className="jscolor-picker-shadow",e.boxS.style.position="absolute",e.boxS.style.left="0",e.boxS.style.top="0",e.boxS.style.width="100%",e.boxS.style.height="100%",y.setBorderRadius(e.boxS,a+"px"),e.boxB.className="jscolor-picker-border",e.boxB.style.position="relative",e.boxB.style.border=f.borderWidth+"px solid",e.boxB.style.borderColor=f.borderColor,e.boxB.style.background=f.backgroundColor,y.setBorderRadius(e.boxB,a+"px"),e.padM.style.background="rgba(255,0,0,.2)",e.sldM.style.background="rgba(0,255,0,.2)",e.asldM.style.background="rgba(0,0,255,.2)",e.padM.style.opacity=e.sldM.style.opacity=e.asldM.style.opacity="0",e.pad.style.position="relative",e.pad.style.width=f.width+"px",e.pad.style.height=f.height+"px",e.padCanvas.draw(f.width,f.height,y.getPadYChannel(f)),e.padB.style.position="absolute",e.padB.style.left=f.padding+"px",e.padB.style.top=f.padding+"px",e.padB.style.border=f.controlBorderWidth+"px solid",e.padB.style.borderColor=f.controlBorderColor,e.padM.style.position="absolute",e.padM.style.left="0px",e.padM.style.top="0px",e.padM.style.width=f.padding+2*f.controlBorderWidth+f.width+i+"px",e.padM.style.height=2*f.controlBorderWidth+2*f.padding+f.height+"px",e.padM.style.cursor="crosshair",y.setData(e.padM,{instance:f,control:"pad"}),e.cross.style.position="absolute",e.cross.style.left=e.cross.style.top="0",e.cross.style.width=e.cross.style.height=o+"px",e.crossBY.style.position=e.crossBX.style.position="absolute",e.crossBY.style.background=e.crossBX.style.background=f.pointerBorderColor,e.crossBY.style.width=e.crossBX.style.height=2*f.pointerBorderWidth+f.pointerThickness+"px",e.crossBY.style.height=e.crossBX.style.width=o+"px",e.crossBY.style.left=e.crossBX.style.top=Math.floor(o/2)-Math.floor(f.pointerThickness/2)-f.pointerBorderWidth+"px",e.crossBY.style.top=e.crossBX.style.left="0",e.crossLY.style.position=e.crossLX.style.position="absolute",e.crossLY.style.background=e.crossLX.style.background=f.pointerColor,e.crossLY.style.height=e.crossLX.style.width=o-2*f.pointerBorderWidth+"px",e.crossLY.style.width=e.crossLX.style.height=f.pointerThickness+"px",e.crossLY.style.left=e.crossLX.style.top=Math.floor(o/2)-Math.floor(f.pointerThickness/2)+"px",e.crossLY.style.top=e.crossLX.style.left=f.pointerBorderWidth+"px",e.sld.style.overflow="hidden",e.sld.style.width=f.sliderSize+"px",e.sld.style.height=f.height+"px",e.sldGrad.draw(f.sliderSize,f.height,"#000","#000"),e.sldB.style.display=t?"block":"none",e.sldB.style.position="absolute",e.sldB.style.left=f.padding+f.width+2*f.controlBorderWidth+2*i+"px",e.sldB.style.top=f.padding+"px",e.sldB.style.border=f.controlBorderWidth+"px solid",e.sldB.style.borderColor=f.controlBorderColor,e.sldM.style.display=t?"block":"none",e.sldM.style.position="absolute",e.sldM.style.left=f.padding+f.width+2*f.controlBorderWidth+i+"px",e.sldM.style.top="0px",e.sldM.style.width=f.sliderSize+2*i+2*f.controlBorderWidth+(r?0:Math.max(0,f.padding-i))+"px",e.sldM.style.height=2*f.controlBorderWidth+2*f.padding+f.height+"px",e.sldM.style.cursor="default",y.setData(e.sldM,{instance:f,control:"sld"}),e.sldPtrIB.style.border=e.sldPtrOB.style.border=f.pointerBorderWidth+"px solid "+f.pointerBorderColor,e.sldPtrOB.style.position="absolute",e.sldPtrOB.style.left=-(2*f.pointerBorderWidth+f.pointerThickness)+"px",e.sldPtrOB.style.top="0",e.sldPtrMB.style.border=f.pointerThickness+"px solid "+f.pointerColor,e.sldPtrS.style.width=f.sliderSize+"px",e.sldPtrS.style.height=y.pub.sliderInnerSpace+"px",e.asld.style.overflow="hidden",e.asld.style.width=f.sliderSize+"px",e.asld.style.height=f.height+"px",e.asldGrad.draw(f.sliderSize,f.height,"#000"),e.asldB.style.display=r?"block":"none",e.asldB.style.position="absolute",e.asldB.style.left=f.padding+f.width+2*f.controlBorderWidth+i+(t?f.sliderSize+3*i+2*f.controlBorderWidth:0)+"px",e.asldB.style.top=f.padding+"px",e.asldB.style.border=f.controlBorderWidth+"px solid",e.asldB.style.borderColor=f.controlBorderColor,e.asldM.style.display=r?"block":"none",e.asldM.style.position="absolute",e.asldM.style.left=f.padding+f.width+2*f.controlBorderWidth+i+(t?f.sliderSize+2*i+2*f.controlBorderWidth:0)+"px",e.asldM.style.top="0px",e.asldM.style.width=f.sliderSize+2*i+2*f.controlBorderWidth+Math.max(0,f.padding-i)+"px",e.asldM.style.height=2*f.controlBorderWidth+2*f.padding+f.height+"px",e.asldM.style.cursor="default",y.setData(e.asldM,{instance:f,control:"asld"}),e.asldPtrIB.style.border=e.asldPtrOB.style.border=f.pointerBorderWidth+"px solid "+f.pointerBorderColor,e.asldPtrOB.style.position="absolute",e.asldPtrOB.style.left=-(2*f.pointerBorderWidth+f.pointerThickness)+"px",e.asldPtrOB.style.top="0",e.asldPtrMB.style.border=f.pointerThickness+"px solid "+f.pointerColor,e.asldPtrS.style.width=f.sliderSize+"px",e.asldPtrS.style.height=y.pub.sliderInnerSpace+"px",e.pal.className="jscolor-palette",e.pal.style.display=n.palette.rows?"block":"none",e.pal.style.position="absolute",e.pal.style.left=f.padding+"px",e.pal.style.top=2*f.controlBorderWidth+2*f.padding+f.height+"px",e.pal.innerHTML="";for(var s=y.genColorPreviewCanvas("rgba(0,0,0,0)"),l=0,h=0;h<n.palette.rows;h++)for(var d=0;d<n.palette.cols&&l<f._palette.length;d++,l++){var p=f._palette[l],c=y.rgbaColor.apply(null,p.rgba),u=y.createEl("div");u.style.width=n.palette.cellW-2*f.controlBorderWidth+"px",u.style.height=n.palette.cellH-2*f.controlBorderWidth+"px",u.style.backgroundColor=c;c=y.createEl("div");c.className="jscolor-palette-sample",c.style.display="block",c.style.position="absolute",c.style.left=(n.palette.cols<=1?0:Math.round(d*((n.contentW-n.palette.cellW)/(n.palette.cols-1))*10)/10)+"px",c.style.top=h*(n.palette.cellH+f.paletteSpacing)+"px",c.style.border=f.controlBorderWidth+"px solid",c.style.borderColor=f.controlBorderColor,c.style.cursor="pointer",null!==p.rgba[3]&&p.rgba[3]<1&&(c.style.backgroundImage="url('"+s.canvas.toDataURL()+"')",c.style.backgroundRepeat="repeat",c.style.backgroundPosition="center center"),y.setData(c,{instance:f,control:"palette-sample",color:p}),c.addEventListener("click",y.onPaletteSampleClick,!1),c.appendChild(u),e.pal.appendChild(c)}var g;e.btn.className="jscolor-btn-close",e.btn.style.display=f.closeButton?"block":"none",e.btn.style.position="absolute",e.btn.style.left=f.padding+"px",e.btn.style.bottom=f.padding+"px",e.btn.style.padding="0 15px",e.btn.style.maxWidth=n.contentW-2*f.controlBorderWidth-30+"px",e.btn.style.overflow="hidden",e.btn.style.height=f.buttonHeight+"px",e.btn.style.whiteSpace="nowrap",e.btn.style.border=f.controlBorderWidth+"px solid",g=(g=f.controlBorderColor.split(/\s+/)).length<2?g[0]:g[1]+" "+g[0]+" "+g[0]+" "+g[1],e.btn.style.borderColor=g,e.btn.style.color=f.buttonColor,e.btn.style.font="12px sans-serif",e.btn.style.textAlign="center",e.btn.style.cursor="pointer",e.btn.onmousedown=function(){f.hide()},e.btnT.style.lineHeight=f.buttonHeight+"px",e.btnT.innerHTML="",e.btnT.appendChild(w.document.createTextNode(f.closeText)),v(),m(),b(),y.picker.owner&&y.picker.owner!==f&&y.removeClass(y.picker.owner.targetElement,y.pub.activeClassName),(y.picker.owner=f).container===w.document.body?y.redrawPosition():y._drawPosition(f,0,0,"relative",!1),e.wrap.parentNode!==f.container&&f.container.appendChild(e.wrap),y.addClass(f.targetElement,y.pub.activeClassName)}function v(){var e=y.getPadYChannel(f),t=Math.round(f.channels.h/360*(f.width-1)),r=Math.round((1-f.channels[e]/100)*(f.height-1)),e=2*f.pointerBorderWidth+f.pointerThickness+2*f.crossSize,e=-Math.floor(e/2);switch(y.picker.cross.style.left=t+e+"px",y.picker.cross.style.top=r+e+"px",y.getSliderChannel(f)){case"s":var n=y.HSV_RGB(f.channels.h,100,f.channels.v),o=y.HSV_RGB(f.channels.h,0,f.channels.v),i="rgb("+Math.round(n[0])+","+Math.round(n[1])+","+Math.round(n[2])+")",n="rgb("+Math.round(o[0])+","+Math.round(o[1])+","+Math.round(o[2])+")";y.picker.sldGrad.draw(f.sliderSize,f.height,i,n);break;case"v":o=y.HSV_RGB(f.channels.h,f.channels.s,100),i="rgb("+Math.round(o[0])+","+Math.round(o[1])+","+Math.round(o[2])+")",n="#000";y.picker.sldGrad.draw(f.sliderSize,f.height,i,n)}y.picker.asldGrad.draw(f.sliderSize,f.height,f.toHEXString())}function m(){var e=y.getSliderChannel(f);e&&(e=Math.round((1-f.channels[e]/100)*(f.height-1)),y.picker.sldPtrOB.style.top=e-(2*f.pointerBorderWidth+f.pointerThickness)-Math.floor(y.pub.sliderInnerSpace/2)+"px"),y.picker.asldGrad.draw(f.sliderSize,f.height,f.toHEXString())}function b(){var e=Math.round((1-f.channels.a)*(f.height-1));y.picker.asldPtrOB.style.top=e-(2*f.pointerBorderWidth+f.pointerThickness)-Math.floor(y.pub.sliderInnerSpace/2)+"px"}function o(){return y.picker&&y.picker.owner===f}if(t=t||{},this.channels={r:255,g:255,b:255,h:0,s:0,v:100,a:1},this.format="auto",this.value=void 0,this.alpha=void 0,this.onChange=void 0,this.onInput=void 0,this.valueElement=void 0,this.alphaElement=void 0,this.previewElement=void 0,this.previewPosition="left",this.previewSize=32,this.previewPadding=8,this.required=!0,this.hash=!0,this.uppercase=!0,this.forceStyle=!0,this.width=181,this.height=101,this.mode="HSV",this.alphaChannel="auto",this.position="bottom",this.smartPosition=!0,this.showOnClick=!0,this.hideOnLeave=!0,this.palette=[],this.paletteCols=10,this.paletteSetsAlpha="auto",this.paletteHeight=16,this.paletteSpacing=4,this.hideOnPaletteClick=!1,this.sliderSize=16,this.crossSize=8,this.closeButton=!1,this.closeText="Close",this.buttonColor="rgba(0,0,0,1)",this.buttonHeight=18,this.padding=12,this.backgroundColor="rgba(255,255,255,1)",this.borderWidth=1,this.borderColor="rgba(187,187,187,1)",this.borderRadius=8,this.controlBorderWidth=1,this.controlBorderColor="rgba(187,187,187,1)",this.shadow=!0,this.shadowBlur=15,this.shadowColor="rgba(0,0,0,0.2)",this.pointerColor="rgba(76,76,76,1)",this.pointerBorderWidth=1,this.pointerBorderColor="rgba(255,255,255,1)",this.pointerThickness=2,this.zIndex=5e3,this.container=void 0,this.minS=0,this.maxS=100,this.minV=0,this.maxV=100,this.minA=0,this.maxA=1,this.option=function(){if(!arguments.length)throw new Error("No option specified");if(1===arguments.length&&"string"==typeof arguments[0]){try{return function(e){if("string"!=typeof e)throw new Error("Invalid value for option name: "+e);if(y.deprecatedOpts.hasOwnProperty(e)){var t=e,r=y.deprecatedOpts[e];if(!r)throw new Error("Option '"+e+"' is DEPRECATED");console.warn("Option '%s' is DEPRECATED, using '%s' instead."+y.docsRef,t,r),e=r}r="get__"+e;{if("function"==typeof f[r])return f[r](value);if(e in f)return f[e]}throw new Error("Unrecognized configuration option: "+e)}(arguments[0])}catch(e){console.warn(e)}return!1}if(2<=arguments.length&&"string"==typeof arguments[0]){try{if(!n(arguments[0],arguments[1]))return!1}catch(e){return console.warn(e),!1}return this.redraw(),this.exposeColor(),!0}if(1!==arguments.length||"object"!=typeof arguments[0])throw new Error("Invalid arguments");var e,t=arguments[0],r=!0;for(e in t)if(t.hasOwnProperty(e))try{n(e,t[e])||(r=!1)}catch(e){console.warn(e),r=!1}return this.redraw(),this.exposeColor(),r},this.channel=function(e,t){if("string"!=typeof e)throw new Error("Invalid value for channel name: "+e);if(void 0===t)return this.channels.hasOwnProperty(e.toLowerCase())?this.channels[e.toLowerCase()]:(console.warn("Getting unknown channel: "+e),!1);var r=!1;switch(e.toLowerCase()){case"r":r=this.fromRGBA(t,null,null,null);break;case"g":r=this.fromRGBA(null,t,null,null);break;case"b":r=this.fromRGBA(null,null,t,null);break;case"h":r=this.fromHSVA(t,null,null,null);break;case"s":r=this.fromHSVA(null,t,null,null);break;case"v":r=this.fromHSVA(null,null,t,null);break;case"a":r=this.fromHSVA(null,null,null,t);break;default:return console.warn("Setting unknown channel: "+e),!1}return!!r&&(this.redraw(),!0)},this.trigger=function(e){for(var t=y.strList(e),r=0;r<t.length;r+=1){var n=t[r].toLowerCase(),o=null;switch(n){case"input":o="onInput";break;case"change":o="onChange"}o&&y.triggerCallback(this,o),y.triggerInputEvent(this.valueElement,n,!0,!0)}},this.fromHSVA=function(e,t,r,n,o){if(void 0===e&&(e=null),void 0===t&&(t=null),void 0===r&&(r=null),void 0===n&&(n=null),null!==e){if(isNaN(e))return!1;this.channels.h=Math.max(0,Math.min(360,e))}if(null!==t){if(isNaN(t))return!1;this.channels.s=Math.max(0,Math.min(100,this.maxS,t),this.minS)}if(null!==r){if(isNaN(r))return!1;this.channels.v=Math.max(0,Math.min(100,this.maxV,r),this.minV)}if(null!==n){if(isNaN(n))return!1;this.channels.a=this.hasAlphaChannel()?Math.max(0,Math.min(1,this.maxA,n),this.minA):1}n=y.HSV_RGB(this.channels.h,this.channels.s,this.channels.v);return this.channels.r=n[0],this.channels.g=n[1],this.channels.b=n[2],this.exposeColor(o),!0},this.fromRGBA=function(e,t,r,n,o){if(void 0===e&&(e=null),void 0===t&&(t=null),void 0===r&&(r=null),void 0===n&&(n=null),null!==e){if(isNaN(e))return!1;e=Math.max(0,Math.min(255,e))}if(null!==t){if(isNaN(t))return!1;t=Math.max(0,Math.min(255,t))}if(null!==r){if(isNaN(r))return!1;r=Math.max(0,Math.min(255,r))}if(null!==n){if(isNaN(n))return!1;this.channels.a=this.hasAlphaChannel()?Math.max(0,Math.min(1,this.maxA,n),this.minA):1}r=y.RGB_HSV(null===e?this.channels.r:e,null===t?this.channels.g:t,null===r?this.channels.b:r);null!==r[0]&&(this.channels.h=Math.max(0,Math.min(360,r[0]))),0!==r[2]&&(this.channels.s=Math.max(0,this.minS,Math.min(100,this.maxS,r[1]))),this.channels.v=Math.max(0,this.minV,Math.min(100,this.maxV,r[2]));r=y.HSV_RGB(this.channels.h,this.channels.s,this.channels.v);return this.channels.r=r[0],this.channels.g=r[1],this.channels.b=r[2],this.exposeColor(o),!0},this.fromHSV=function(e,t,r,n){return console.warn("fromHSV() method is DEPRECATED. Using fromHSVA() instead."+y.docsRef),this.fromHSVA(e,t,r,null,n)},this.fromRGB=function(e,t,r,n){return console.warn("fromRGB() method is DEPRECATED. Using fromRGBA() instead."+y.docsRef),this.fromRGBA(e,t,r,null,n)},this.fromString=function(e,t){if(!this.required&&""===e.trim())return this.setPreviewElementBg(null),this.setValueElementValue(""),!0;e=y.parseColorString(e);return!!e&&("any"===this.format.toLowerCase()&&(this._setFormat(e.format),y.isAlphaFormat(this.getFormat())||(e.rgba[3]=1)),this.fromRGBA(e.rgba[0],e.rgba[1],e.rgba[2],e.rgba[3],t),!0)},this.toString=function(e){switch(void 0===e&&(e=this.getFormat()),e.toLowerCase()){case"hex":return this.toHEXString();case"hexa":return this.toHEXAString();case"rgb":return this.toRGBString();case"rgba":return this.toRGBAString()}return!1},this.toHEXString=function(){return y.hexColor(this.channels.r,this.channels.g,this.channels.b)},this.toHEXAString=function(){return y.hexaColor(this.channels.r,this.channels.g,this.channels.b,this.channels.a)},this.toRGBString=function(){return y.rgbColor(this.channels.r,this.channels.g,this.channels.b)},this.toRGBAString=function(){return y.rgbaColor(this.channels.r,this.channels.g,this.channels.b,this.channels.a)},this.toGrayscale=function(){return.213*this.channels.r+.715*this.channels.g+.072*this.channels.b},this.toCanvas=function(){return y.genColorPreviewCanvas(this.toRGBAString()).canvas},this.toDataURL=function(){return this.toCanvas().toDataURL()},this.toBackground=function(){return y.pub.background(this.toRGBAString())},this.isLight=function(){return 127.5<this.toGrayscale()},this.hide=function(){o()&&(y.removeClass(f.targetElement,y.pub.activeClassName),y.picker.wrap.parentNode.removeChild(y.picker.wrap),delete y.picker.owner)},this.show=function(){r()},this.redraw=function(){o()&&r()},this.getFormat=function(){return this._currentFormat},this._setFormat=function(e){this._currentFormat=e.toLowerCase()},this.hasAlphaChannel=function(){return"auto"===this.alphaChannel?"any"===this.format.toLowerCase()||y.isAlphaFormat(this.getFormat())||void 0!==this.alpha||void 0!==this.alphaElement:this.alphaChannel},this.processValueInput=function(e){this.fromString(e)||this.exposeColor()},this.processAlphaInput=function(e){this.fromHSVA(null,null,null,parseFloat(e))||this.exposeColor()},this.exposeColor=function(e){var t=this.toString(),r=this.getFormat();y.setDataAttr(this.targetElement,"current-color",t),e&y.flags.leaveValue||!this.valueElement||("hex"!==r&&"hexa"!==r||(this.uppercase||(t=t.toLowerCase()),this.hash||(t=t.replace(/^#/,""))),this.setValueElementValue(t)),e&y.flags.leaveAlpha||!this.alphaElement||(t=Math.round(100*this.channels.a)/100,this.setAlphaElementValue(t)),e&y.flags.leavePreview||!this.previewElement||((y.isTextInput(this.previewElement)||y.isButton(this.previewElement)&&!y.isButtonEmpty(this.previewElement))&&this.previewPosition,this.setPreviewElementBg(this.toRGBAString())),o()&&(v(),m(),b())},this.setPreviewElementBg=function(e){if(this.previewElement){var t=null,r=null;(y.isTextInput(this.previewElement)||y.isButton(this.previewElement)&&!y.isButtonEmpty(this.previewElement))&&(t=this.previewPosition,r=this.previewSize);var n=[];e?(n.push({image:y.genColorPreviewGradient(e,t,r?r-y.pub.previewSeparator.length:null),position:"left top",size:"auto",repeat:t?"repeat-y":"repeat",origin:"padding-box"}),a=y.genColorPreviewCanvas("rgba(0,0,0,0)",t?{left:"right",right:"left"}[t]:null,r,!0),n.push({image:"url('"+a.canvas.toDataURL()+"')",position:(t||"left")+" top",size:a.width+"px "+a.height+"px",repeat:t?"repeat-y":"repeat",origin:"padding-box"})):n.push({image:"none",position:"left top",size:"auto",repeat:"no-repeat",origin:"padding-box"});for(var o={image:[],position:[],size:[],repeat:[],origin:[]},i=0;i<n.length;i+=1)o.image.push(n[i].image),o.position.push(n[i].position),o.size.push(n[i].size),o.repeat.push(n[i].repeat),o.origin.push(n[i].origin);r={"background-image":o.image.join(", "),"background-position":o.position.join(", "),"background-size":o.size.join(", "),"background-repeat":o.repeat.join(", "),"background-origin":o.origin.join(", ")};y.setStyle(this.previewElement,r,this.forceStyle);var a={left:null,right:null};t&&(a[t]=this.previewSize+this.previewPadding+"px");r={"padding-left":a.left,"padding-right":a.right};y.setStyle(this.previewElement,r,this.forceStyle,!0)}},this.setValueElementValue=function(e){this.valueElement&&("input"===y.nodeName(this.valueElement)?this.valueElement.value=e:this.valueElement.innerHTML=e)},this.setAlphaElementValue=function(e){this.alphaElement&&("input"===y.nodeName(this.alphaElement)?this.alphaElement.value=e:this.alphaElement.innerHTML=e)},this._processParentElementsInDOM=function(){if(!this._parentElementsProcessed){this._parentElementsProcessed=!0;var e=this.targetElement;do{var t=y.getCompStyle(e)}while(t.position&&"fixed"===t.position.toLowerCase()&&(this.fixed=!0),e!==this.targetElement&&(y.getData(e,"hasScrollListener")||(e.addEventListener("scroll",y.onParentScroll,!1),y.setData(e,"hasScrollListener",!0))),(e=e.parentNode)&&"body"!==y.nodeName(e))}},this.tryHide=function(){this.hideOnLeave&&this.hide()},this.set__palette=function(e){this.palette=e,this._palette=y.parsePaletteValue(e),this._paletteHasTransparency=y.containsTranparentColor(this._palette)},y.pub.options)for(var i in y.pub.options)if(y.pub.options.hasOwnProperty(i))try{n(i,y.pub.options[i])}catch(e){console.warn(e)}var a=[];t.preset&&("string"==typeof t.preset?a=t.preset.split(/\s+/):Array.isArray(t.preset)?a=t.preset.slice():console.warn("Unrecognized preset value")),-1===a.indexOf("default")&&a.push("default");for(var s=a.length-1;0<=s;--s){var l=a[s];if(l)if(y.pub.presets.hasOwnProperty(l)){for(var i in y.pub.presets[l])if(y.pub.presets[l].hasOwnProperty(i))try{n(i,y.pub.presets[l][i])}catch(e){console.warn(e)}}else console.warn("Unknown preset: %s",l)}var h=["preset"];for(i in t)if(t.hasOwnProperty(i)&&-1===h.indexOf(i))try{n(i,t[i])}catch(e){console.warn(e)}if(void 0===this.container?this.container=w.document.body:this.container=y.node(this.container),!this.container)throw new Error("Cannot instantiate color picker without a container element");if(this.targetElement=y.node(e),!this.targetElement){if("string"==typeof e&&/^[a-zA-Z][\w:.-]*$/.test(e))throw new Error("If '"+e+"' is supposed to be an ID, please use '#"+e+"' or any valid CSS selector.");throw new Error("Cannot instantiate color picker without a target element")}if(this.targetElement.jscolor&&this.targetElement.jscolor instanceof y.pub)throw new Error("Color picker already installed on this element");this.targetElement.jscolor=this,y.addClass(this.targetElement,y.pub.className),y.instances.push(this),y.isButton(this.targetElement)&&("button"!==this.targetElement.type.toLowerCase()&&(this.targetElement.type="button"),y.isButtonEmpty(this.targetElement)&&(y.removeChildren(this.targetElement),this.targetElement.appendChild(w.document.createTextNode(" ")),p=y.getCompStyle(this.targetElement),(parseFloat(p["min-width"])||0)<this.previewSize&&y.setStyle(this.targetElement,{"min-width":this.previewSize+"px"},this.forceStyle))),void 0===this.valueElement?y.isTextInput(this.targetElement)&&(this.valueElement=this.targetElement):null===this.valueElement||(this.valueElement=y.node(this.valueElement)),this.alphaElement&&(this.alphaElement=y.node(this.alphaElement)),void 0===this.previewElement?this.previewElement=this.targetElement:null===this.previewElement||(this.previewElement=y.node(this.previewElement)),this.valueElement&&y.isTextInput(this.valueElement)&&(d={onInput:this.valueElement.oninput},this.valueElement.oninput=null,this.valueElement.addEventListener("keydown",function(e){"Enter"===y.eventKey(e)&&(f.valueElement&&f.processValueInput(f.valueElement.value),f.tryHide())},!1),this.valueElement.addEventListener("change",function(e){y.getData(e,"internal")||(e=f.valueElement.value,f.processValueInput(f.valueElement.value),y.triggerCallback(f,"onChange"),f.valueElement.value!==e&&y.triggerInputEvent(f.valueElement,"change",!0,!0))},!1),this.valueElement.addEventListener("input",function(e){y.getData(e,"internal")||(f.valueElement&&f.fromString(f.valueElement.value,y.flags.leaveValue),y.triggerCallback(f,"onInput"))},!1),d.onInput&&this.valueElement.addEventListener("input",d.onInput,!1),this.valueElement.setAttribute("autocomplete","off"),this.valueElement.setAttribute("autocorrect","off"),this.valueElement.setAttribute("autocapitalize","off"),this.valueElement.setAttribute("spellcheck",!1)),this.alphaElement&&y.isTextInput(this.alphaElement)&&(this.alphaElement.addEventListener("keydown",function(e){"Enter"===y.eventKey(e)&&(f.alphaElement&&f.processAlphaInput(f.alphaElement.value),f.tryHide())},!1),this.alphaElement.addEventListener("change",function(e){y.getData(e,"internal")||(e=f.alphaElement.value,f.processAlphaInput(f.alphaElement.value),y.triggerCallback(f,"onChange"),y.triggerInputEvent(f.valueElement,"change",!0,!0),f.alphaElement.value!==e&&y.triggerInputEvent(f.alphaElement,"change",!0,!0))},!1),this.alphaElement.addEventListener("input",function(e){y.getData(e,"internal")||(f.alphaElement&&f.fromHSVA(null,null,null,parseFloat(f.alphaElement.value),y.flags.leaveAlpha),y.triggerCallback(f,"onInput"),y.triggerInputEvent(f.valueElement,"input",!0,!0))},!1),this.alphaElement.setAttribute("autocomplete","off"),this.alphaElement.setAttribute("autocorrect","off"),this.alphaElement.setAttribute("autocapitalize","off"),this.alphaElement.setAttribute("spellcheck",!1));e="FFFFFF";void 0!==this.value?e=this.value:this.valueElement&&void 0!==this.valueElement.value&&(e=this.valueElement.value);var d,p=void 0;void 0!==this.alpha?p=""+this.alpha:this.alphaElement&&void 0!==this.alphaElement.value&&(p=this.alphaElement.value),this._currentFormat=null,-1<["auto","any"].indexOf(this.format.toLowerCase())?(d=y.parseColorString(e),this._currentFormat=d?d.format:"hex"):this._currentFormat=this.format.toLowerCase(),this.processValueInput(e),void 0!==p&&this.processAlphaInput(p)}}).pub.className="jscolor",y.pub.activeClassName="jscolor-active",y.pub.looseJSON=!0,y.pub.presets={},y.pub.presets.default={},y.pub.presets.light={backgroundColor:"rgba(255,255,255,1)",controlBorderColor:"rgba(187,187,187,1)",buttonColor:"rgba(0,0,0,1)"},y.pub.presets.dark={backgroundColor:"rgba(51,51,51,1)",controlBorderColor:"rgba(153,153,153,1)",buttonColor:"rgba(240,240,240,1)"},y.pub.presets.small={width:101,height:101,padding:10,sliderSize:14,paletteCols:8},y.pub.presets.medium={width:181,height:101,padding:12,sliderSize:16,paletteCols:10},y.pub.presets.large={width:271,height:151,padding:12,sliderSize:24,paletteCols:15},y.pub.presets.thin={borderWidth:1,controlBorderWidth:1,pointerBorderWidth:1},y.pub.presets.thick={borderWidth:2,controlBorderWidth:2,pointerBorderWidth:2},y.pub.sliderInnerSpace=3,y.pub.chessboardSize=8,y.pub.chessboardColor1="#666666",y.pub.chessboardColor2="#999999",y.pub.previewSeparator=["rgba(255,255,255,.65)","rgba(128,128,128,.65)"],y.pub.init=function(){if(!y.initialized)for(w.document.addEventListener("mousedown",y.onDocumentMouseDown,!1),w.document.addEventListener("keyup",y.onDocumentKeyUp,!1),w.addEventListener("resize",y.onWindowResize,!1),w.addEventListener("scroll",y.onWindowScroll,!1),y.pub.install(),y.initialized=!0;y.readyQueue.length;)y.readyQueue.shift()()},y.pub.install=function(e){var t=!0;try{y.installBySelector("[data-jscolor]",e)}catch(e){t=!1,console.warn(e)}if(y.pub.lookupClass)try{y.installBySelector("input."+y.pub.lookupClass+", button."+y.pub.lookupClass,e)}catch(e){}return t},y.pub.ready=function(e){return"function"!=typeof e?(console.warn("Passed value is not a function"),!1):(y.initialized?e():y.readyQueue.push(e),!0)},y.pub.trigger=function(e){function t(){y.triggerGlobal(e)}y.initialized?t():y.pub.ready(t)},y.pub.hide=function(){y.picker&&y.picker.owner&&y.picker.owner.hide()},y.pub.chessboard=function(e){return e=e||"rgba(0,0,0,0)",y.genColorPreviewCanvas(e).canvas.toDataURL()},y.pub.background=function(e){var t=[];t.push(y.genColorPreviewGradient(e));e=y.genColorPreviewCanvas();return t.push(["url('"+e.canvas.toDataURL()+"')","left top","repeat"].join(" ")),t.join(", ")},y.pub.options={},y.pub.lookupClass="jscolor",y.pub.installByClassName=function(){return console.error('jscolor.installByClassName() is DEPRECATED. Use data-jscolor="" attribute instead of a class name.'+y.docsRef),!1},y.register(),y.pub);function r(e,t,r,n,o,i){this.hShadow=e,this.vShadow=t,this.blur=r,this.spread=n,this.color=o,this.inset=!!i}return void 0===w.jscolor&&(w.jscolor=w.JSColor=t),t});