/*
 *  /MathJax/fonts/HTML-CSS/TeX/png/Fraktur/Regular/Other.js
 *  
 *  Copyright (c) 2010-2013 The MathJax Consortium
 *
 *  Part of the MathJax library.
 *  See http://www.mathjax.org for details.
 * 
 *  Licensed under the Apache License, Version 2.0;
 *  you may not use this file except in compliance with the License.
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 */

MathJax.OutputJax["HTML-CSS"].defineImageData({MathJax_Fraktur:{160:[[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0]],8216:[[2,2,-2],[2,3,-3],[2,3,-4],[2,4,-5],[3,5,-5],[3,5,-7],[4,6,-7],[4,7,-9],[5,8,-11],[6,10,-14],[7,12,-16],[8,14,-18],[9,17,-21],[11,20,-26]],8217:[[2,2,-2],[2,3,-3],[2,3,-4],[2,4,-5],[3,5,-5],[3,5,-7],[4,6,-7],[4,7,-9],[5,8,-11],[6,10,-14],[7,12,-16],[8,14,-18],[10,17,-21],[11,20,-26]]}});MathJax.Ajax.loadComplete(MathJax.OutputJax["HTML-CSS"].imgDir+"/Fraktur/Regular"+MathJax.OutputJax["HTML-CSS"].imgPacked+"/Other.js");

