# Elite Quiz API Reference

## Base URL
```
http://your-domain.com/Api/
```

## Authentication
Most endpoints require authentication via JWT token. Include the token in your request headers or POST data.

## Request Format
All requests should be made using POST method with form-data or JSON payload.

## Response Format
```json
{
  "error": false,
  "message": "Success message",
  "data": {
    // Response data
  }
}
```

## Status Codes

| Code | Message |
|------|---------|
| 101 | Invalid access key |
| 102 | Data not found |
| 103 | Please fill all data and submit |
| 104 | User registered successfully |
| 105 | Successfully logged in |
| 106 | Profile updated successfully |
| 107 | File upload fail |
| 108 | Battle destroyed successfully |
| 109 | Report submitted successfully |
| 110 | Data insert successfully |
| 111 | Data update successfully |
| 112 | Daily quiz already played |
| 113 | No matches played |
| 114 | No upcoming contest |
| 115 | No contest available |
| 116 | No contest played yet |
| 117 | Contest you have played |
| 118 | Play & win exciting prizes |
| 119 | Room already created |
| 120 | Room create successfully |
| 121 | Room destroyed successfully |
| 122 | Something wrong |
| 123 | Notification sent successfully |
| 124 | Invalid hash |
| 125 | Unauthorized access not allowed |
| 126 | Account deactivated |
| 127 | Payment request already exists |
| 128 | Already played |
| 129 | Unauthorized access |
| 130 | User exists |
| 131 | User not exists |
| 132 | Data exists |
| 133 | Daily ads limit achieved |
| 134 | User can continue |
| 135 | Payment request not pending |
| 136 | Already coin redeem |

---

## User Management APIs

### 1. User Signup/Login
**Endpoint:** `POST /user_signup`

**Parameters:**
```json
{
  "firebase_id": "xxxxxxxxxxxxxxxxxxxxx",
  "type": "email|gmail|fb|mobile|apple",
  "name": "User Name",
  "email": "<EMAIL>",
  "profile": "Image URL",
  "mobile": "**********",
  "fcm_id": "FCM Token",
  "web_fcm_id": "Web FCM Token",
  "friends_code": "Friend's referral code",
  "status": 1,
  "app_language": "english",
  "web_language": "english"
}
```

**Response:**
```json
{
  "error": false,
  "message": "User registered successfully",
  "data": {
    "id": "1",
    "firebase_id": "xxxxx",
    "name": "User Name",
    "email": "<EMAIL>",
    "coins": 100,
    "api_token": "jwt_token_here"
  }
}
```

### 2. Get User Details
**Endpoint:** `POST /get_user_by_id`

**Parameters:**
```json
{
  "access_key": "your_access_key"
}
```

### 3. Update Profile
**Endpoint:** `POST /update_profile`

**Parameters:**
```json
{
  "access_key": "your_access_key",
  "email": "<EMAIL>",
  "name": "New Name",
  "mobile": "**********",
  "remove_ads": 0,
  "app_language": "english",
  "web_language": "english"
}
```

### 4. Upload Profile Image
**Endpoint:** `POST /upload_profile_image`

**Parameters:**
- `access_key`: Your access key
- `image`: Image file (multipart/form-data)

---

## Quiz System APIs

### 1. Get Categories
**Endpoint:** `POST /get_categories`

**Parameters:**
```json
{
  "access_key": "your_access_key",
  "type": 1,
  "language_id": 1,
  "id": 31,
  "sub_type": 1
}
```

**Type Values:**
- 1: Quiz Zone
- 2: Fun & Learn
- 3: Guess the Word
- 4: Audio Questions
- 5: Math Questions
- 6: Multi Match

**Sub Type Values (when type=1):**
- 1: Self Challenge
- 2: Battle
- 3: Group Battle

### 2. Get Questions
**Endpoint:** `POST /get_questions`

**Parameters:**
```json
{
  "access_key": "your_access_key",
  "type": "category|subcategory",
  "id": 1
}
```

### 3. Get Questions by Level
**Endpoint:** `POST /get_questions_by_level`

**Parameters:**
```json
{
  "access_key": "your_access_key",
  "level": 2,
  "category": 5,
  "subcategory": 9,
  "language_id": 2
}
```

### 4. Get Questions by Type
**Endpoint:** `POST /get_questions_by_type`

**Parameters:**
```json
{
  "access_key": "your_access_key",
  "type": 1,
  "limit": 10,
  "language_id": 2
}
```

**Question Types:**
- 1: Normal questions
- 2: True/False questions

### 5. Submit Quiz Results
**Endpoint:** `POST /set_quiz_coin_score`

**Parameters:**
```json
{
  "access_key": "your_access_key",
  "quiz_type": "1",
  "play_questions": [
    {"id": "4", "answer": "a"},
    {"id": "20", "answer": "d"}
  ],
  "category": 1,
  "subcategory": 2,
  "lifeline": "50:50,audience_poll"
}
```

**Quiz Types:**
- 1: Quiz Zone
- 1.1: Daily Quiz
- 1.2: True/False
- 1.3: Random Battle
- 1.4: One vs One Battle
- 1.5: Group Battle
- 2: Fun & Learn
- 3: Guess the Word
- 4: Audio Quiz
- 5: Math Quiz
- 6: Multi Match

---

## Battle System APIs

### 1. Create Battle Room
**Endpoint:** `POST /create_room`

**Parameters:**
```json
{
  "access_key": "your_access_key",
  "room_id": "123",
  "room_type": "public|private",
  "language_id": 2,
  "category": 1,
  "entry_coin": 10
}
```

### 2. Get Battle Questions
**Endpoint:** `POST /get_question_by_room_id`

**Parameters:**
```json
{
  "access_key": "your_access_key",
  "room_id": "123"
}
```

### 3. Get Random Battle Questions
**Endpoint:** `POST /get_random_questions`

**Parameters:**
```json
{
  "access_key": "your_access_key",
  "match_id": "your_match_id",
  "language_id": 2,
  "category": 1,
  "destroy_match": 0,
  "random": true,
  "entry_coin": 10
}
```

---

## Contest System APIs

### 1. Get Contests
**Endpoint:** `POST /get_contest`

**Parameters:**
```json
{
  "access_key": "your_access_key",
  "language_id": 1,
  "timezone": "Asia/Kolkata",
  "gmt_format": "+5:30"
}
```

### 2. Get Contest Questions
**Endpoint:** `POST /get_questions_by_contest`

**Parameters:**
```json
{
  "access_key": "your_access_key",
  "contest_id": 5
}
```

### 3. Get Contest Leaderboard
**Endpoint:** `POST /get_contest_leaderboard`

**Parameters:**
```json
{
  "access_key": "your_access_key",
  "contest_id": 1
}
```

---

## Exam System APIs

### 1. Get Exam Modules
**Endpoint:** `POST /get_exam_module`

**Parameters:**
```json
{
  "access_key": "your_access_key",
  "language_id": 1,
  "type": 1,
  "offset": 0,
  "limit": 10,
  "timezone": "Asia/Kolkata",
  "gmt_format": "+5:30"
}
```

**Type Values:**
- 1: Today's exams
- 2: Completed exams

### 2. Get Exam Questions
**Endpoint:** `POST /get_exam_module_questions`

**Parameters:**
```json
{
  "access_key": "your_access_key",
  "exam_module_id": 1
}
```

### 3. Submit Exam Results
**Endpoint:** `POST /set_exam_module_result`

**Parameters:**
```json
{
  "access_key": "your_access_key",
  "exam_module_id": 1,
  "total_duration": 10,
  "obtained_marks": 85,
  "statistics": [
    {"mark": "1", "correct_answer": "2", "incorrect": "3"}
  ],
  "rules_violated": 0,
  "captured_question_ids": []
}
```

---

## Leaderboard APIs

### 1. Daily Leaderboard
**Endpoint:** `POST /get_daily_leaderboard`

**Parameters:**
```json
{
  "access_key": "your_access_key",
  "offset": 0,
  "limit": 10
}
```

### 2. Monthly Leaderboard
**Endpoint:** `POST /get_monthly_leaderboard`

**Parameters:**
```json
{
  "access_key": "your_access_key",
  "offset": 0,
  "limit": 10
}
```

### 3. Global Leaderboard
**Endpoint:** `POST /get_globle_leaderboard`

**Parameters:**
```json
{
  "access_key": "your_access_key",
  "offset": 0,
  "limit": 10
}
```

---

## System Configuration APIs

### 1. Get System Configurations
**Endpoint:** `POST /get_system_configurations`

**Parameters:**
```json
{
  "access_key": "your_access_key"
}
```

**Response includes:**
- App version information
- Coin settings
- Advertisement configuration
- Payment settings
- Feature toggles

### 2. Get Settings
**Endpoint:** `POST /get_settings`

**Parameters:**
```json
{
  "access_key": "your_access_key",
  "type": "about_us|privacy_policy|terms_conditions|contact_us|instructions|app_name"
}
```

### 3. Get Notifications
**Endpoint:** `POST /get_notifications`

**Parameters:**
```json
{
  "access_key": "your_access_key",
  "sort": "id|users|type",
  "order": "DESC|ASC",
  "offset": 0,
  "limit": 20
}
```

---

## Bookmark APIs

### 1. Set Bookmark
**Endpoint:** `POST /set_bookmark`

**Parameters:**
```json
{
  "access_key": "your_access_key",
  "question_id": 11,
  "status": 1,
  "type": 1
}
```

**Status Values:**
- 1: Bookmark
- 0: Remove bookmark

**Type Values:**
- 1: Quiz Zone
- 3: Guess the Word
- 4: Audio Questions
- 5: Math Questions

### 2. Get Bookmarks
**Endpoint:** `POST /get_bookmark`

**Parameters:**
```json
{
  "access_key": "your_access_key",
  "type": 1
}
```

---

## Coin System APIs

### 1. Set User Coins
**Endpoint:** `POST /set_user_coin_score`

**Parameters:**
```json
{
  "access_key": "your_access_key",
  "coins": 10,
  "score": 2,
  "type": "dashing_debut",
  "title": "Achievement unlocked"
}
```

**Badge Types:**
- dashing_debut
- combat_winner
- clash_winner
- most_wanted_winner
- ultimate_player
- quiz_warrior
- super_sonic
- flashback
- brainiac
- big_thing
- elite
- thirsty
- power_elite
- sharing_caring
- streak

### 2. Get User Coins
**Endpoint:** `POST /get_user_coin_score`

**Parameters:**
```json
{
  "access_key": "your_access_key"
}
```

---

## Error Handling

All API endpoints return consistent error responses:

```json
{
  "error": true,
  "message": "Error description",
  "data": []
}
```

Common error scenarios:
- Invalid access key (401)
- Missing required parameters (400)
- Data not found (404)
- Server errors (500)

## Rate Limiting

API endpoints may have rate limiting in place. Check response headers for rate limit information.

## Testing

Use tools like Postman or curl to test API endpoints:

```bash
curl -X POST http://your-domain.com/Api/get_categories \
  -H "Content-Type: application/json" \
  -d '{"access_key": "your_key", "type": 1}'
```
