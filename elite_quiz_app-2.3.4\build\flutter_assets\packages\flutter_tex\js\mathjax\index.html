<!DOCTYPE html>
<html lang="">
<meta content="width=device-width, initial-scale=1.0" name="viewport">

<head>
    <script src="../flutter_tex.js" type="text/javascript"></script>
    <link crossorigin="anonymous" href="../flutter_tex.css" rel="stylesheet">
    <style>
        .MathJax_SVG:focus {
            outline: none;
        }
    </style>
    <script>
        var mathjaxConfigurations = {
            showMathMenu: false,
            delayStartupUntil: "configured",
            tex2jax: {
                inlineMath: [['$', '$'], ["\\(", "\\)"]],
                processEscapes: true,
                ignoreClass: "tex2jax_ignore"
            },
            jax: ["input/MathML", "input/TeX", "output/SVG"],

            extensions: ["tex2jax.js", "MathEvents.js", "toMathML.js", "mml2jax.js"],

            TeX: {
                extensions: ["mhchem.js", "noErrors.js", "noUndefined.js", "autoload-all.js"],
                noErrors: { disabled: true },
                Macros: {
                    RR: "{\\bf R}",
                    bold: ["{\\bf #1}", 1]
                }
            },
            MathMl: {
                extensions: ["Mathcontent-mathml.js", "mml3.js"],
                noErrors: { disabled: true }
            },
            MathMenu: {
                showRenderer: false
            },
            messageStyle: "none",
            SVG: {
                blacker: 0,
                font: "Gyre-Pagella",
                linebreaks: {
                    automatic: true,
                    width: "container"
                },
                scale: 90,
                useFontCache: true,
                useGlobalCache: true,
                matchFontHeight: true
            }
        };
    </script>
    <script id="MathJaxConfig" type="text/x-mathjax-config">MathJax.Hub.Config(mathjaxConfigurations);</script>
    <script src="MathJax.js" type="text/javascript"></script>
    <title>mathjax</title>
</head>

<body>
    <div id="TeXView"></div>
    <script>
        teXView = document.getElementById('TeXView');

        function initView(jsonData) {
            initTeXView(jsonData, "mathjax");
        }

        function renderTeXView(onCompleteCallback) {
            MathJax.Hub.Queue(["Typeset", MathJax.Hub, "TeXView"], function () {
                onCompleteCallback();
            });
        }

    </script>
</body>

</html>