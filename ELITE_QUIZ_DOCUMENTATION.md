# Elite Quiz - Complete Project Documentation

## Table of Contents
1. [Project Overview](#project-overview)
2. [Architecture Overview](#architecture-overview)
3. [Flutter Mobile App](#flutter-mobile-app)
4. [Admin Panel (PHP CodeIgniter)](#admin-panel-php-codeigniter)
5. [API Documentation](#api-documentation)
6. [Database Schema](#database-schema)
7. [Setup and Installation](#setup-and-installation)
8. [User Guide](#user-guide)
9. [Developer Guide](#developer-guide)

---

## Project Overview

**Elite Quiz** is a comprehensive quiz application system consisting of:
- **Flutter Mobile App** (Android/iOS) - Version 2.3.4
- **PHP CodeIgniter Admin Panel** - Version 2.3.4
- **REST API Backend**
- **Firebase Integration** for real-time features
- **MySQL Database**

### Key Features

#### Mobile App Features
- **Multiple Quiz Types**: Quiz Zone, Fun & Learn, Guess the Word, Audio Questions, Math Mania, Multi-Match
- **Battle System**: 1v1 battles, group battles, random battles
- **Daily Quiz**: Time-based daily challenges
- **Contest System**: Timed contests with leaderboards
- **Exam <PERSON>**: Structured exam system with time limits
- **Coin System**: Virtual currency for rewards and purchases
- **Badge System**: Achievement system with various badges
- **Bookmark System**: Save questions for later review
- **Leaderboards**: Daily, monthly, and global rankings
- **Multi-language Support**: RTL and LTR language support
- **In-App Purchases**: Premium features and coin purchases
- **Ad Integration**: Google Ads and Unity Ads
- **Social Features**: Share, refer friends, social login

#### Admin Panel Features
- **User Management**: Complete user administration
- **Question Management**: CRUD operations for all question types
- **Category Management**: Hierarchical category system
- **Contest Management**: Create and manage timed contests
- **Exam Module Management**: Create structured exams
- **Badge Management**: Configure achievement system
- **System Configuration**: App settings and configurations
- **Analytics Dashboard**: User statistics and app analytics
- **Notification System**: Push notifications management
- **Payment Management**: Handle payment requests
- **Multi-language Management**: Manage app translations

### Technology Stack

#### Frontend (Mobile App)
- **Framework**: Flutter 3.8.1+
- **State Management**: BLoC (Business Logic Component)
- **Local Storage**: Hive (NoSQL database)
- **Authentication**: Firebase Auth
- **Real-time Database**: Cloud Firestore
- **Push Notifications**: Firebase Cloud Messaging
- **Analytics**: Firebase Analytics
- **Ads**: Google Mobile Ads, Unity Ads
- **Payments**: In-App Purchase (Android/iOS)

#### Backend (Admin Panel)
- **Framework**: PHP CodeIgniter 3.x
- **Database**: MySQL
- **Authentication**: Session-based
- **File Upload**: Image handling for questions/categories
- **API**: RESTful API endpoints

#### Infrastructure
- **Database**: MySQL with comprehensive schema
- **File Storage**: Local file system for images
- **Push Notifications**: Firebase Cloud Messaging
- **Real-time Features**: Firebase Firestore
- **Analytics**: Firebase Analytics

---

## Architecture Overview

### System Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Flutter App   │    │  Admin Panel    │    │    Firebase     │
│   (Mobile)      │    │  (Web/PHP)      │    │   Services      │
└─────────┬───────┘    └─────────┬───────┘    └─────────┬───────┘
          │                      │                      │
          │                      │                      │
          └──────────────────────┼──────────────────────┘
                                 │
                    ┌─────────────┴───────────┐
                    │     MySQL Database      │
                    │   (Central Data Store)  │
                    └─────────────────────────┘
```

### Data Flow

1. **User Registration/Login**: Firebase Auth → API → MySQL
2. **Quiz Data**: Admin Panel → MySQL → API → Flutter App
3. **Real-time Battles**: Flutter App ↔ Firebase Firestore
4. **Push Notifications**: Admin Panel → Firebase FCM → Flutter App
5. **Analytics**: Flutter App → Firebase Analytics

### Key Components

#### Flutter App Components
- **BLoC State Management**: Centralized state management
- **Repository Pattern**: Data abstraction layer
- **Local Storage**: Hive for offline data
- **Navigation**: Named routes with parameters
- **Theming**: Light/Dark theme support
- **Localization**: Multi-language support

#### Admin Panel Components
- **MVC Architecture**: Model-View-Controller pattern
- **Authentication System**: Role-based access control
- **File Management**: Image upload and management
- **API Layer**: RESTful endpoints for mobile app
- **Dashboard**: Analytics and statistics

---

## Flutter Mobile App

### Project Structure

```
lib/
├── app/                    # App initialization and configuration
├── commons/               # Shared components and models
│   ├── bottom_nav/       # Bottom navigation
│   ├── models/           # Common data models
│   ├── screens/          # Shared screens
│   └── widgets/          # Reusable widgets
├── core/                 # Core functionality
│   ├── config/           # App configuration
│   ├── constants/        # Constants and endpoints
│   ├── localization/     # Multi-language support
│   ├── navigation/       # Navigation utilities
│   ├── routes/           # Route definitions
│   └── theme/            # App theming
├── features/             # Feature-based modules
│   ├── ads/              # Advertisement integration
│   ├── auth/             # Authentication
│   ├── badges/           # Badge system
│   ├── battle_room/      # Battle functionality
│   ├── bookmark/         # Bookmark system
│   ├── coin_history/     # Coin transactions
│   ├── exam/             # Exam module
│   ├── in_app_purchase/  # In-app purchases
│   ├── leaderboard/      # Leaderboards
│   ├── music_player/     # Audio player
│   ├── notification/     # Push notifications
│   ├── profile_management/ # User profile
│   ├── quiz/             # Quiz functionality
│   ├── settings/         # App settings
│   ├── statistic/        # User statistics
│   ├── system_config/    # System configuration
│   └── wallet/           # Coin wallet
├── ui/                   # UI components
│   ├── screens/          # App screens
│   └── widgets/          # UI widgets
└── utils/                # Utility functions
```

### Key Features Implementation

#### Quiz Types

The app supports multiple quiz types defined in `QuizTypes` enum:

1. **Quiz Zone** (typeValue: '1')
   - Daily Quiz
   - True/False Quiz
   - Self Challenge
   - Bookmark Quiz
   - Random Battle

2. **Fun & Learn** (typeValue: '2')
   - Educational content with explanations

3. **Guess the Word** (typeValue: '3')
   - Word puzzle games

4. **Audio Questions** (typeValue: '4')
   - Audio-based quiz questions

5. **Math Mania** (typeValue: '5')
   - Mathematical questions with LaTeX support

6. **Multi-Match** (typeValue: '6')
   - Multiple answer selection questions

7. **Contest**
   - Timed competitive quizzes

8. **Exam**
   - Structured examination system

#### State Management (BLoC)

The app uses BLoC pattern for state management with the following key cubits:

- **AuthCubit**: User authentication state
- **UserDetailsCubit**: User profile management
- **QuizCategoryCubit**: Quiz categories
- **BattleRoomCubit**: Real-time battles
- **BookmarkCubit**: Bookmarked questions
- **SystemConfigCubit**: App configuration
- **SettingsCubit**: User preferences

#### Authentication System

Supports multiple authentication providers:
- Email/Password
- Google Sign-In
- Apple Sign-In
- Phone Number (OTP)

#### Battle System

Real-time multiplayer battles using Firebase Firestore:
- 1v1 battles
- Group battles (up to 4 players)
- Random battles
- Room-based battles with codes

#### Coin System

Virtual currency system with:
- Earning coins through quizzes
- Spending coins for premium features
- Daily rewards
- Referral bonuses
- In-app purchases

### Dependencies

Key Flutter packages used:
- `flutter_bloc`: State management
- `firebase_core`, `firebase_auth`, `cloud_firestore`: Firebase integration
- `hive_flutter`: Local storage
- `google_mobile_ads`: Advertisement
- `in_app_purchase`: In-app purchases
- `flutter_local_notifications`: Local notifications
- `cached_network_image`: Image caching
- `google_fonts`: Custom fonts
- `flutter_svg`: SVG support
- `lottie`: Animations

---

## Admin Panel (PHP CodeIgniter)

### Project Structure

```
adminpanel2.3.4/
├── application/
│   ├── config/           # Configuration files
│   ├── controllers/      # Controller classes
│   │   ├── Api.php       # API endpoints
│   │   ├── Dashboard.php # Admin dashboard
│   │   ├── Login.php     # Authentication
│   │   ├── Question.php  # Question management
│   │   ├── Category.php  # Category management
│   │   ├── Contest.php   # Contest management
│   │   ├── Exam_Module.php # Exam management
│   │   └── ...
│   ├── models/           # Model classes
│   │   ├── Question_model.php
│   │   ├── Category_model.php
│   │   ├── User_model.php
│   │   ├── Contest_model.php
│   │   └── ...
│   ├── views/            # View templates
│   ├── libraries/        # Custom libraries
│   └── helpers/          # Helper functions
├── assets/               # Static assets (CSS, JS, images)
├── images/               # Uploaded images
├── system/               # CodeIgniter core files
└── vendor/               # Composer dependencies
```

### Key Controllers

#### Api.php
Main API controller handling all mobile app requests:
- User management endpoints
- Quiz data endpoints
- Battle system endpoints
- Statistics and leaderboards
- Payment processing

#### Dashboard.php
Admin dashboard with analytics:
- User statistics
- Quiz performance metrics
- Revenue analytics
- System health monitoring

#### Question.php
Question management system:
- CRUD operations for questions
- Bulk import/export
- Question categorization
- Media file handling

### Database Models

Auto-loaded models in the system:
- `Login_model`: Authentication
- `Setting_model`: System settings
- `Language_model`: Multi-language support
- `Category_model`: Category management
- `Question_model`: Question operations
- `User_model`: User management
- `Contest_model`: Contest system
- `Exam_Module_model`: Exam functionality
- `Badges_model`: Achievement system

### Authentication & Security

- Session-based authentication
- Role-based access control
- Permission system for different admin levels
- CSRF protection
- Input validation and sanitization

---

## API Documentation

### Base URL
```
http://your-domain.com/Api/
```

### Authentication
Most endpoints require authentication via JWT token passed in headers or POST data.

### Key Endpoints

#### User Management
- `POST /user_signup` - User registration/login
- `POST /get_user_by_id` - Get user details
- `POST /update_profile` - Update user profile
- `POST /upload_profile_image` - Upload profile image
- `POST /delete_user_account` - Delete user account

#### Quiz System
- `POST /get_categories` - Get quiz categories
- `POST /get_questions` - Get questions by category
- `POST /get_questions_by_level` - Get questions by level
- `POST /get_questions_by_type` - Get questions by type
- `POST /set_quiz_coin_score` - Submit quiz results

#### Battle System
- `POST /create_room` - Create battle room
- `POST /get_question_by_room_id` - Get battle questions
- `POST /get_random_questions` - Get random battle questions

#### Contest System
- `POST /get_contest` - Get active contests
- `POST /get_questions_by_contest` - Get contest questions
- `POST /get_contest_leaderboard` - Get contest rankings

#### Exam System
- `POST /get_exam_module` - Get exam modules
- `POST /get_exam_module_questions` - Get exam questions
- `POST /set_exam_module_result` - Submit exam results

#### Leaderboards
- `POST /get_daily_leaderboard` - Daily rankings
- `POST /get_monthly_leaderboard` - Monthly rankings
- `POST /get_globle_leaderboard` - Global rankings

#### System
- `POST /get_system_configurations` - Get app settings
- `POST /get_settings` - Get content settings
- `POST /get_notifications` - Get notifications

### Response Format

All API responses follow this format:
```json
{
  "error": false,
  "message": "Success message",
  "data": {
    // Response data
  }
}
```

Error responses:
```json
{
  "error": true,
  "message": "Error message",
  "data": []
}
```

### Status Codes

The API uses custom status codes:
- 101: Invalid access key
- 102: Data not found
- 103: Please fill all data
- 104: User registered successfully
- 105: Successfully logged in
- 106: Profile updated successfully
- And many more...

---

## Database Schema

### Core Tables

#### Users Table (`tbl_users`)
```sql
CREATE TABLE `tbl_users` (
  `id` int(10) UNSIGNED NOT NULL,
  `firebase_id` longtext NOT NULL,
  `name` varchar(128) NOT NULL,
  `email` varchar(128) NOT NULL,
  `mobile` varchar(32) NOT NULL,
  `type` varchar(16) NOT NULL,
  `profile` varchar(128) NOT NULL,
  `fcm_id` varchar(1024) DEFAULT NULL,
  `coins` int(11) NOT NULL DEFAULT 0,
  `refer_code` varchar(128) DEFAULT NULL,
  `friends_code` varchar(128) DEFAULT NULL,
  `status` int(11) NOT NULL DEFAULT 1,
  `date_registered` datetime NOT NULL
);
```

#### Questions Table (`tbl_question`)
```sql
CREATE TABLE `tbl_question` (
  `id` int(10) UNSIGNED NOT NULL,
  `category` int(11) NOT NULL,
  `subcategory` int(11) NOT NULL,
  `language_id` int(11) NOT NULL DEFAULT 1,
  `image` varchar(255) DEFAULT NULL,
  `question` text NOT NULL,
  `question_type` int(11) NOT NULL DEFAULT 1,
  `optiona` text NOT NULL,
  `optionb` text NOT NULL,
  `optionc` text NOT NULL,
  `optiond` text NOT NULL,
  `optione` text NOT NULL,
  `answer` varchar(10) NOT NULL,
  `level` int(11) NOT NULL DEFAULT 1,
  `note` text NOT NULL
);
```

#### Categories Table (`tbl_category`)
```sql
CREATE TABLE `tbl_category` (
  `id` int(10) UNSIGNED NOT NULL,
  `category_name` varchar(128) NOT NULL,
  `image` varchar(255) NOT NULL,
  `maxlevel` int(11) NOT NULL DEFAULT 0,
  `no_of_que` int(11) NOT NULL DEFAULT 5,
  `is_premium` int(11) NOT NULL DEFAULT 0,
  `coins` int(11) NOT NULL DEFAULT 0,
  `status` int(11) NOT NULL DEFAULT 1
);
```

### Relationship Overview

```
Users (1) ←→ (M) User_Statistics
Users (1) ←→ (M) User_Badges  
Users (1) ←→ (M) Bookmarks
Users (1) ←→ (M) Battle_Rooms
Categories (1) ←→ (M) Questions
Categories (1) ←→ (M) Subcategories
Questions (1) ←→ (M) Bookmarks
Contest (1) ←→ (M) Contest_Questions
Exam_Module (1) ←→ (M) Exam_Questions
```

### Key Tables

1. **User Management**
   - `tbl_users`: User accounts
   - `tbl_users_badges`: User achievements
   - `tbl_users_statistics`: User performance stats
   - `tbl_tracker`: Coin transaction history

2. **Quiz System**
   - `tbl_category`: Quiz categories
   - `tbl_subcategory`: Sub-categories
   - `tbl_question`: Quiz questions
   - `tbl_level`: Level progression data

3. **Battle System**
   - `tbl_rooms`: Battle rooms
   - `tbl_battle_statistics`: Battle results

4. **Contest System**
   - `tbl_contest`: Contest definitions
   - `tbl_contest_questions`: Contest-specific questions
   - `tbl_contest_leaderboard`: Contest rankings

5. **Exam System**
   - `tbl_exam_module`: Exam definitions
   - `tbl_exam_module_questions`: Exam questions
   - `tbl_exam_module_result`: Exam results

6. **System Tables**
   - `tbl_settings`: App configuration
   - `tbl_languages`: Supported languages
   - `tbl_notifications`: Push notifications
   - `tbl_authenticate`: Admin authentication

---

## Setup and Installation

### Prerequisites

#### For Flutter App
- Flutter SDK 3.8.1 or higher
- Dart SDK
- Android Studio / VS Code
- Android SDK (for Android development)
- Xcode (for iOS development, macOS only)
- Firebase project setup

#### For Admin Panel
- PHP 7.4 or higher
- MySQL 5.7 or higher
- Apache/Nginx web server
- Composer (for PHP dependencies)

### Flutter App Setup

1. **Clone the Repository**
   ```bash
git clone <repository-url>
   cd elite_quiz_app-2.3.4
```

2. **Install Dependencies**
   ```bash
flutter pub get
```

3. **Firebase Configuration**
   - Create a Firebase project
   - Add Android/iOS apps to Firebase project
   - Download `google-services.json` (Android) and `GoogleService-Info.plist` (iOS)
   - Place files in respective platform directories
   - Configure Firebase Authentication, Firestore, and FCM

4. **Configure API Endpoints**
   - Update `lib/core/config/config.dart` with your server URL
   - Set `panelUrl` to your admin panel domain

5. **Generate App Icons**
   ```bash
dart run flutter_launcher_icons
```

6. **Run the App**
   ```bash
flutter run
```

### Admin Panel Setup

1. **Server Requirements**
   - PHP 7.4+
   - MySQL 5.7+
   - Apache/Nginx with mod_rewrite enabled
   - Composer

2. **Installation Steps**
   ```bash
# Extract admin panel files
   cd adminpanel2.3.4

   # Install PHP dependencies
   composer install

   # Set proper permissions
   chmod -R 755 application/
   chmod -R 777 images/
   chmod -R 777 upload/
```

3. **Database Setup**
   - Create MySQL database
   - Import database schema from `install/` directory
   - Update database configuration in `application/config/database.php`

4. **Configuration**
   - Update `application/config/config.php` with your domain
   - Configure email settings in `application/config/email.php`
   - Set up Firebase service account for push notifications

5. **Access Admin Panel**
   - Navigate to `http://your-domain.com/adminpanel2.3.4/`
   - Use default admin credentials (change after first login)

### Firebase Setup

1. **Create Firebase Project**
   - Go to Firebase Console
   - Create new project
   - Enable Authentication, Firestore, and Cloud Messaging

2. **Authentication Setup**
   - Enable Email/Password, Google, Apple, Phone authentication
   - Configure OAuth providers

3. **Firestore Setup**
   - Create Firestore database
   - Set up security rules for battle rooms
   - Configure indexes for queries

4. **Cloud Messaging Setup**
   - Generate server key for admin panel
   - Configure FCM in admin panel settings

---

## User Guide

### Mobile App User Guide

#### Getting Started

1. **Download and Install**
   - Download from Google Play Store or Apple App Store
   - Install and open the app

2. **Registration/Login**
   - Choose registration method (Email, Google, Apple, Phone)
   - Complete profile setup
   - Verify email/phone if required

3. **Home Screen Navigation**
   - **Quiz Zone**: Main quiz categories
   - **Daily Quiz**: Time-limited daily challenges
   - **Contests**: Competitive timed quizzes
   - **Battle**: Multiplayer quiz battles
   - **Profile**: User statistics and settings

#### Quiz Features

1. **Quiz Zone**
   - Browse categories by subject
   - Select difficulty level
   - Answer questions within time limit
   - Earn coins and XP for correct answers
   - Use lifelines (50:50, Audience Poll, etc.)

2. **Daily Quiz**
   - Available once per day
   - Limited time window
   - Special rewards for completion
   - Streak bonuses for consecutive days

3. **Battle Mode**
   - **1v1 Battle**: Challenge friends or random players
   - **Group Battle**: Up to 4 players in one room
   - **Random Battle**: Quick match with random opponent
   - Real-time competition with live scoring

4. **Contest Mode**
   - Participate in timed contests
   - Compete with all users globally
   - View live leaderboards
   - Win special prizes and recognition

5. **Exam Mode**
   - Structured examination format
   - Time-limited with strict rules
   - Comprehensive result analysis
   - Certificate generation

#### Coin System

1. **Earning Coins**
   - Complete quizzes successfully
   - Daily login bonuses
   - Watch advertisement videos
   - Refer friends to the app
   - Win battles and contests

2. **Spending Coins**
   - Unlock premium categories
   - Purchase lifelines
   - Enter paid battles
   - Buy hints in word games

#### Badge System

Earn badges for various achievements:
- **Dashing Debut**: Complete first quiz
- **Combat Winner**: Win battles
- **Quiz Warrior**: Answer questions correctly
- **Streak Master**: Maintain daily quiz streaks
- **Elite Player**: Reach top rankings

#### Social Features

1. **Leaderboards**
   - Daily, monthly, and all-time rankings
   - Category-wise leaderboards
   - Friend comparisons

2. **Sharing**
   - Share quiz results on social media
   - Invite friends via referral codes
   - Challenge friends to battles

### Admin Panel User Guide

#### Dashboard Overview

1. **Main Dashboard**
   - User statistics and growth metrics
   - Quiz performance analytics
   - Revenue and coin usage reports
   - System health monitoring

2. **Quick Actions**
   - Add new questions
   - Create contests
   - Send notifications
   - Manage user accounts

#### User Management

1. **User List**
   - View all registered users
   - Search and filter users
   - View user details and statistics
   - Manage user status (active/inactive)

2. **User Details**
   - Profile information
   - Quiz performance history
   - Coin transaction history
   - Badge achievements

#### Content Management

1. **Categories**
   - Create quiz categories
   - Set category images and descriptions
   - Configure premium status
   - Set coin requirements

2. **Questions**
   - Add questions with multiple choice options
   - Upload question images
   - Set difficulty levels
   - Categorize questions
   - Bulk import from CSV

3. **Contests**
   - Create timed contests
   - Set start/end dates
   - Configure prize structures
   - Monitor participation

4. **Exams**
   - Create structured exam modules
   - Set time limits and rules
   - Configure passing criteria
   - Generate certificates

#### System Configuration

1. **App Settings**
   - Configure coin rewards
   - Set quiz parameters
   - Manage advertisement settings
   - Configure in-app purchases

2. **Notifications**
   - Send push notifications
   - Schedule announcements
   - Target specific user groups
   - Track delivery statistics

3. **Languages**
   - Add new languages
   - Manage translations
   - Set RTL/LTR preferences
   - Configure language-specific content

---

## Developer Guide

### Flutter App Development

#### Project Architecture

The app follows **Clean Architecture** principles with **BLoC** state management:

```
Presentation Layer (UI)
    ↓
Business Logic Layer (BLoC/Cubit)
    ↓
Data Layer (Repository)
    ↓
Data Sources (Remote API, Local Storage)
```

#### Key Architectural Patterns

1. **BLoC Pattern**
   - Separates business logic from UI
   - Reactive programming with streams
   - Testable and maintainable code

2. **Repository Pattern**
   - Abstracts data sources
   - Provides clean API for business logic
   - Enables easy testing and mocking

3. **Dependency Injection**
   - Uses BlocProvider for dependency injection
   - Singleton pattern for repositories
   - Easy to test and maintain

#### Adding New Features

1. **Create Feature Structure**
   ```
features/new_feature/
   ├── models/
   ├── repository/
   ├── cubits/
   └── screens/
```

2. **Implement Data Models**
   ```dart
class NewFeatureModel {
     final String id;
     final String name;

     NewFeatureModel({required this.id, required this.name});

     factory NewFeatureModel.fromJson(Map<String, dynamic> json) {
       return NewFeatureModel(
         id: json['id'],
         name: json['name'],
       );
     }
   }
```

3. **Create Repository**
   ```dart
class NewFeatureRepository {
     final NewFeatureRemoteDataSource _remoteDataSource;

     NewFeatureRepository(this._remoteDataSource);

     Future<List<NewFeatureModel>> getFeatures() async {
       return await _remoteDataSource.getFeatures();
     }
   }
```

4. **Implement BLoC/Cubit**
   ```dart
class NewFeatureCubit extends Cubit<NewFeatureState> {
     final NewFeatureRepository _repository;

     NewFeatureCubit(this._repository) : super(NewFeatureInitial());

     Future<void> loadFeatures() async {
       emit(NewFeatureLoading());
       try {
         final features = await _repository.getFeatures();
         emit(NewFeatureLoaded(features));
       } catch (e) {
         emit(NewFeatureError(e.toString()));
       }
     }
   }
```

#### State Management Best Practices

1. **Cubit vs BLoC**
   - Use Cubit for simple state management
   - Use BLoC for complex event-driven scenarios

2. **State Classes**
   ```dart
abstract class NewFeatureState {}

   class NewFeatureInitial extends NewFeatureState {}
   class NewFeatureLoading extends NewFeatureState {}
   class NewFeatureLoaded extends NewFeatureState {
     final List<NewFeatureModel> features;
     NewFeatureLoaded(this.features);
   }
   class NewFeatureError extends NewFeatureState {
     final String message;
     NewFeatureError(this.message);
   }
```

3. **UI Integration**
   ```dart
BlocBuilder<NewFeatureCubit, NewFeatureState>(
     builder: (context, state) {
       if (state is NewFeatureLoading) {
         return CircularProgressIndicator();
       } else if (state is NewFeatureLoaded) {
         return ListView.builder(
           itemCount: state.features.length,
           itemBuilder: (context, index) {
             return ListTile(title: Text(state.features[index].name));
           },
         );
       } else if (state is NewFeatureError) {
         return Text('Error: ${state.message}');
       }
       return Container();
     },
   );
```

#### Testing

1. **Unit Tests**
   ```dart
void main() {
     group('NewFeatureCubit', () {
       late NewFeatureCubit cubit;
       late MockNewFeatureRepository mockRepository;

       setUp(() {
         mockRepository = MockNewFeatureRepository();
         cubit = NewFeatureCubit(mockRepository);
       });

       test('should emit loading then loaded when loadFeatures succeeds', () async {
         // Arrange
         when(mockRepository.getFeatures()).thenAnswer((_) async => []);

         // Act
         cubit.loadFeatures();

         // Assert
         expectLater(
           cubit.stream,
           emitsInOrder([NewFeatureLoading(), NewFeatureLoaded([])]),
         );
       });
     });
   }
```

2. **Widget Tests**
   ```dart
void main() {
     testWidgets('should display features when loaded', (tester) async {
       // Arrange
       final mockCubit = MockNewFeatureCubit();
       when(mockCubit.state).thenReturn(NewFeatureLoaded([]));

       // Act
       await tester.pumpWidget(
         BlocProvider<NewFeatureCubit>.value(
           value: mockCubit,
           child: MaterialApp(home: NewFeatureScreen()),
         ),
       );

       // Assert
       expect(find.byType(ListView), findsOneWidget);
     });
   }
```

### Admin Panel Development

#### CodeIgniter Structure

1. **MVC Pattern**
   - **Models**: Database operations
   - **Views**: HTML templates
   - **Controllers**: Business logic

2. **Adding New Controller**
   ```php
<?php
   class New_Feature extends CI_Controller {

       public function __construct() {
           parent::__construct();
           if (!$this->session->userdata('isLoggedIn')) {
               redirect('/');
           }
           $this->load->model('New_Feature_model');
       }

       public function index() {
           $data['features'] = $this->New_Feature_model->get_all();
           $this->load->view('new_feature/index', $data);
       }

       public function add() {
           if ($this->input->post('submit')) {
               $this->New_Feature_model->add_feature();
               redirect('new-feature');
           }
           $this->load->view('new_feature/add');
       }
   }
```

3. **Creating Model**
   ```php
<?php
   class New_Feature_model extends CI_Model {

       public function get_all() {
           return $this->db->get('tbl_new_feature')->result_array();
       }

       public function add_feature() {
           $data = array(
               'name' => $this->input->post('name'),
               'description' => $this->input->post('description'),
               'created_at' => date('Y-m-d H:i:s')
           );
           return $this->db->insert('tbl_new_feature', $data);
       }
   }
```

#### API Development

1. **Adding New API Endpoint**
   ```php
// In Api.php controller
   public function get_new_feature_post() {
       $user_id = $this->verify_token();
       if (!$user_id) {
           $this->response(['error' => true, 'message' => 'Unauthorized']);
           return;
       }

       $features = $this->New_Feature_model->get_user_features($user_id);
       $this->response([
           'error' => false,
           'message' => 'Features retrieved successfully',
           'data' => $features
       ]);
   }
```

2. **API Response Format**
   ```php
private function response($data) {
       $this->output
           ->set_content_type('application/json')
           ->set_output(json_encode($data));
   }
```

#### Database Operations

1. **Migration Scripts**
   ```sql
CREATE TABLE `tbl_new_feature` (
     `id` int(11) NOT NULL AUTO_INCREMENT,
     `name` varchar(255) NOT NULL,
     `description` text,
     `status` tinyint(1) DEFAULT 1,
     `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
     PRIMARY KEY (`id`)
   );
```

2. **Model Best Practices**
   ```php
public function get_paginated($offset = 0, $limit = 10) {
       $this->db->limit($limit, $offset);
       return $this->db->get('tbl_new_feature')->result_array();
   }

   public function search($keyword) {
       $this->db->like('name', $keyword);
       return $this->db->get('tbl_new_feature')->result_array();
   }
```

### Customization Guide

#### Theming

1. **Flutter App Theming**
   ```dart
// In core/theme/app_theme.dart
   final ThemeData lightTheme = ThemeData(
     primarySwatch: Colors.blue,
     brightness: Brightness.light,
     // Customize colors, fonts, etc.
   );
```

2. **Admin Panel Theming**
   - Modify CSS files in `assets/css/`
   - Update color variables
   - Customize layout components

#### Localization

1. **Adding New Language**
   ```dart
// Add to supported locales
   static const List<Locale> supportedLocales = [
     Locale('en', 'US'),
     Locale('es', 'ES'), // New language
   ];
```

2. **Translation Files**
   - Add JSON files for new languages
   - Update translation keys
   - Test RTL support if needed

#### Configuration

1. **App Configuration**
   ```dart
// In core/config/config.dart
   const String panelUrl = 'https://your-domain.com/adminpanel';
   const String appName = 'Your Quiz App';
   const bool enableDebugMode = false;
```

2. **Admin Panel Configuration**
   ```php
// In application/config/config.php
   $config['base_url'] = 'https://your-domain.com/';
   $config['encryption_key'] = 'your-encryption-key';
```

### Deployment

#### Flutter App Deployment

1. **Android Release**
   ```bash
# Generate signed APK
   flutter build apk --release

   # Generate App Bundle
   flutter build appbundle --release
```

2. **iOS Release**
   ```bash
# Build for iOS
   flutter build ios --release

   # Archive in Xcode for App Store
```

#### Admin Panel Deployment

1. **Server Setup**
   - Configure web server (Apache/Nginx)
   - Set up SSL certificate
   - Configure database connection
   - Set proper file permissions

2. **Security Considerations**
   - Change default admin credentials
   - Update encryption keys
   - Configure firewall rules
   - Enable HTTPS

---

This comprehensive documentation covers all aspects of the Elite Quiz project, from basic setup to advanced development practices. Use this as a reference for understanding, maintaining, and extending the application.
or Android development)
- Xcode (for iOS development, macOS only)
- Firebase project setup

#### For Admin Panel
- PHP 7.4 or higher
- MySQL 5.7 or higher
- Apache/Nginx web server
- Composer (for PHP dependencies)

### Flutter App Setup

1. **Clone the Repository**
   ```bash
   git clone <repository-url>
   cd elite_quiz_app-2.3.4
   ```

2. **Install Dependencies**
   ```bash
   flutter pub get
   ```

3. **Firebase Configuration**
   - Create a Firebase project
   - Add Android/iOS apps to Firebase project
   - Download `google-services.json` (Android) and `GoogleService-Info.plist` (iOS)
   - Place files in respective platform directories
   - Configure Firebase Authentication, Firestore, and FCM

4. **Configure API Endpoints**
   - Update `lib/core/config/config.dart` with your server URL
   - Set `panelUrl` to your admin panel domain

5. **Generate App Icons**
   ```bash
   dart run flutter_launcher_icons
   ```

6. **Run the App**
   ```bash
   flutter run
   ```

### Admin Panel Setup

1. **Server Requirements**
   - PHP 7.4+
   - MySQL 5.7+
   - Apache/Nginx with mod_rewrite enabled
   - Composer

2. **Installation Steps**
   ```bash
   # Extract admin panel files
   cd adminpanel2.3.4

   # Install PHP dependencies
   composer install

   # Set proper permissions
   chmod -R 755 application/
   chmod -R 777 images/
   chmod -R 777 upload/
   ```

3. **Database Setup**
   - Create MySQL database
   - Import database schema from `install/` directory
   - Update database configuration in `application/config/database.php`

4. **Configuration**
   - Update `application/config/config.php` with your domain
   - Configure email settings in `application/config/email.php`
   - Set up Firebase service account for push notifications

5. **Access Admin Panel**
   - Navigate to `http://your-domain.com/adminpanel2.3.4/`
   - Use default admin credentials (change after first login)

### Firebase Setup

1. **Create Firebase Project**
   - Go to Firebase Console
   - Create new project
   - Enable Authentication, Firestore, and Cloud Messaging

2. **Authentication Setup**
   - Enable Email/Password, Google, Apple, Phone authentication
   - Configure OAuth providers

3. **Firestore Setup**
   - Create Firestore database
   - Set up security rules for battle rooms
   - Configure indexes for queries

4. **Cloud Messaging Setup**
   - Generate server key for admin panel
   - Configure FCM in admin panel settings

---

## User Guide

### Mobile App User Guide

#### Getting Started

1. **Download and Install**
   - Download from Google Play Store or Apple App Store
   - Install and open the app

2. **Registration/Login**
   - Choose registration method (Email, Google, Apple, Phone)
   - Complete profile setup
   - Verify email/phone if required

3. **Home Screen Navigation**
   - **Quiz Zone**: Main quiz categories
   - **Daily Quiz**: Time-limited daily challenges
   - **Contests**: Competitive timed quizzes
   - **Battle**: Multiplayer quiz battles
   - **Profile**: User statistics and settings

#### Quiz Features

1. **Quiz Zone**
   - Browse categories by subject
   - Select difficulty level
   - Answer questions within time limit
   - Earn coins and XP for correct answers
   - Use lifelines (50:50, Audience Poll, etc.)

2. **Daily Quiz**
   - Available once per day
   - Limited time window
   - Special rewards for completion
   - Streak bonuses for consecutive days

3. **Battle Mode**
   - **1v1 Battle**: Challenge friends or random players
   - **Group Battle**: Up to 4 players in one room
   - **Random Battle**: Quick match with random opponent
   - Real-time competition with live scoring

4. **Contest Mode**
   - Participate in timed contests
   - Compete with all users globally
   - View live leaderboards
   - Win special prizes and recognition

5. **Exam Mode**
   - Structured examination format
   - Time-limited with strict rules
   - Comprehensive result analysis
   - Certificate generation

#### Coin System

1. **Earning Coins**
   - Complete quizzes successfully
   - Daily login bonuses
   - Watch advertisement videos
   - Refer friends to the app
   - Win battles and contests

2. **Spending Coins**
   - Unlock premium categories
   - Purchase lifelines
   - Enter paid battles
   - Buy hints in word games

#### Badge System

Earn badges for various achievements:
- **Dashing Debut**: Complete first quiz
- **Combat Winner**: Win battles
- **Quiz Warrior**: Answer questions correctly
- **Streak Master**: Maintain daily quiz streaks
- **Elite Player**: Reach top rankings

#### Social Features

1. **Leaderboards**
   - Daily, monthly, and all-time rankings
   - Category-wise leaderboards
   - Friend comparisons

2. **Sharing**
   - Share quiz results on social media
   - Invite friends via referral codes
   - Challenge friends to battles

### Admin Panel User Guide

#### Dashboard Overview

1. **Main Dashboard**
   - User statistics and growth metrics
   - Quiz performance analytics
   - Revenue and coin usage reports
   - System health monitoring

2. **Quick Actions**
   - Add new questions
   - Create contests
   - Send notifications
   - Manage user accounts

#### User Management

1. **User List**
   - View all registered users
   - Search and filter users
   - View user details and statistics
   - Manage user status (active/inactive)

2. **User Details**
   - Profile information
   - Quiz performance history
   - Coin transaction history
   - Badge achievements

#### Content Management

1. **Categories**
   - Create quiz categories
   - Set category images and descriptions
   - Configure premium status
   - Set coin requirements

2. **Questions**
   - Add questions with multiple choice options
   - Upload question images
   - Set difficulty levels
   - Categorize questions
   - Bulk import from CSV

3. **Contests**
   - Create timed contests
   - Set start/end dates
   - Configure prize structures
   - Monitor participation

4. **Exams**
   - Create structured exam modules
   - Set time limits and rules
   - Configure passing criteria
   - Generate certificates

#### System Configuration

1. **App Settings**
   - Configure coin rewards
   - Set quiz parameters
   - Manage advertisement settings
   - Configure in-app purchases

2. **Notifications**
   - Send push notifications
   - Schedule announcements
   - Target specific user groups
   - Track delivery statistics

3. **Languages**
   - Add new languages
   - Manage translations
   - Set RTL/LTR preferences
   - Configure language-specific content

---

## Developer Guide

### Flutter App Development

#### Project Architecture

The app follows **Clean Architecture** principles with **BLoC** state management:

```
Presentation Layer (UI)
    ↓
Business Logic Layer (BLoC/Cubit)
    ↓
Data Layer (Repository)
    ↓
Data Sources (Remote API, Local Storage)
```

#### Key Architectural Patterns

1. **BLoC Pattern**
   - Separates business logic from UI
   - Reactive programming with streams
   - Testable and maintainable code

2. **Repository Pattern**
   - Abstracts data sources
   - Provides clean API for business logic
   - Enables easy testing and mocking

3. **Dependency Injection**
   - Uses BlocProvider for dependency injection
   - Singleton pattern for repositories
   - Easy to test and maintain

#### Adding New Features

1. **Create Feature Structure**
   ```
   features/new_feature/
   ├── models/
   ├── repository/
   ├── cubits/
   └── screens/
   ```

2. **Implement Data Models**
   ```dart
   class NewFeatureModel {
     final String id;
     final String name;

     NewFeatureModel({required this.id, required this.name});

     factory NewFeatureModel.fromJson(Map<String, dynamic> json) {
       return NewFeatureModel(
         id: json['id'],
         name: json['name'],
       );
     }
   }
   ```

3. **Create Repository**
   ```dart
   class NewFeatureRepository {
     final NewFeatureRemoteDataSource _remoteDataSource;

     NewFeatureRepository(this._remoteDataSource);

     Future<List<NewFeatureModel>> getFeatures() async {
       return await _remoteDataSource.getFeatures();
     }
   }
   ```

4. **Implement BLoC/Cubit**
   ```dart
   class NewFeatureCubit extends Cubit<NewFeatureState> {
     final NewFeatureRepository _repository;

     NewFeatureCubit(this._repository) : super(NewFeatureInitial());

     Future<void> loadFeatures() async {
       emit(NewFeatureLoading());
       try {
         final features = await _repository.getFeatures();
         emit(NewFeatureLoaded(features));
       } catch (e) {
         emit(NewFeatureError(e.toString()));
       }
     }
   }
   ```

#### State Management Best Practices

1. **Cubit vs BLoC**
   - Use Cubit for simple state management
   - Use BLoC for complex event-driven scenarios

2. **State Classes**
   ```dart
   abstract class NewFeatureState {}

   class NewFeatureInitial extends NewFeatureState {}
   class NewFeatureLoading extends NewFeatureState {}
   class NewFeatureLoaded extends NewFeatureState {
     final List<NewFeatureModel> features;
     NewFeatureLoaded(this.features);
   }
   class NewFeatureError extends NewFeatureState {
     final String message;
     NewFeatureError(this.message);
   }
   ```

3. **UI Integration**
   ```dart
   BlocBuilder<NewFeatureCubit, NewFeatureState>(
     builder: (context, state) {
       if (state is NewFeatureLoading) {
         return CircularProgressIndicator();
       } else if (state is NewFeatureLoaded) {
         return ListView.builder(
           itemCount: state.features.length,
           itemBuilder: (context, index) {
             return ListTile(title: Text(state.features[index].name));
           },
         );
       } else if (state is NewFeatureError) {
         return Text('Error: ${state.message}');
       }
       return Container();
     },
   );
   ```

#### Testing

1. **Unit Tests**
   ```dart
   void main() {
     group('NewFeatureCubit', () {
       late NewFeatureCubit cubit;
       late MockNewFeatureRepository mockRepository;

       setUp(() {
         mockRepository = MockNewFeatureRepository();
         cubit = NewFeatureCubit(mockRepository);
       });

       test('should emit loading then loaded when loadFeatures succeeds', () async {
         // Arrange
         when(mockRepository.getFeatures()).thenAnswer((_) async => []);

         // Act
         cubit.loadFeatures();

         // Assert
         expectLater(
           cubit.stream,
           emitsInOrder([NewFeatureLoading(), NewFeatureLoaded([])]),
         );
       });
     });
   }
   ```

2. **Widget Tests**
   ```dart
   void main() {
     testWidgets('should display features when loaded', (tester) async {
       // Arrange
       final mockCubit = MockNewFeatureCubit();
       when(mockCubit.state).thenReturn(NewFeatureLoaded([]));

       // Act
       await tester.pumpWidget(
         BlocProvider<NewFeatureCubit>.value(
           value: mockCubit,
           child: MaterialApp(home: NewFeatureScreen()),
         ),
       );

       // Assert
       expect(find.byType(ListView), findsOneWidget);
     });
   }
   ```

### Admin Panel Development

#### CodeIgniter Structure

1. **MVC Pattern**
   - **Models**: Database operations
   - **Views**: HTML templates
   - **Controllers**: Business logic

2. **Adding New Controller**
   ```php
   <?php
   class New_Feature extends CI_Controller {

       public function __construct() {
           parent::__construct();
           if (!$this->session->userdata('isLoggedIn')) {
               redirect('/');
           }
           $this->load->model('New_Feature_model');
       }

       public function index() {
           $data['features'] = $this->New_Feature_model->get_all();
           $this->load->view('new_feature/index', $data);
       }

       public function add() {
           if ($this->input->post('submit')) {
               $this->New_Feature_model->add_feature();
               redirect('new-feature');
           }
           $this->load->view('new_feature/add');
       }
   }
   ```

3. **Creating Model**
   ```php
   <?php
   class New_Feature_model extends CI_Model {

       public function get_all() {
           return $this->db->get('tbl_new_feature')->result_array();
       }

       public function add_feature() {
           $data = array(
               'name' => $this->input->post('name'),
               'description' => $this->input->post('description'),
               'created_at' => date('Y-m-d H:i:s')
           );
           return $this->db->insert('tbl_new_feature', $data);
       }
   }
   ```

#### API Development

1. **Adding New API Endpoint**
   ```php
   // In Api.php controller
   public function get_new_feature_post() {
       $user_id = $this->verify_token();
       if (!$user_id) {
           $this->response(['error' => true, 'message' => 'Unauthorized']);
           return;
       }

       $features = $this->New_Feature_model->get_user_features($user_id);
       $this->response([
           'error' => false,
           'message' => 'Features retrieved successfully',
           'data' => $features
       ]);
   }
   ```

2. **API Response Format**
   ```php
   private function response($data) {
       $this->output
           ->set_content_type('application/json')
           ->set_output(json_encode($data));
   }
   ```

#### Database Operations

1. **Migration Scripts**
   ```sql
   CREATE TABLE `tbl_new_feature` (
     `id` int(11) NOT NULL AUTO_INCREMENT,
     `name` varchar(255) NOT NULL,
     `description` text,
     `status` tinyint(1) DEFAULT 1,
     `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
     PRIMARY KEY (`id`)
   );
   ```

2. **Model Best Practices**
   ```php
   public function get_paginated($offset = 0, $limit = 10) {
       $this->db->limit($limit, $offset);
       return $this->db->get('tbl_new_feature')->result_array();
   }

   public function search($keyword) {
       $this->db->like('name', $keyword);
       return $this->db->get('tbl_new_feature')->result_array();
   }
   ```

### Customization Guide

#### Theming

1. **Flutter App Theming**
   ```dart
   // In core/theme/app_theme.dart
   final ThemeData lightTheme = ThemeData(
     primarySwatch: Colors.blue,
     brightness: Brightness.light,
     // Customize colors, fonts, etc.
   );
   ```

2. **Admin Panel Theming**
   - Modify CSS files in `assets/css/`
   - Update color variables
   - Customize layout components

#### Localization

1. **Adding New Language**
   ```dart
   // Add to supported locales
   static const List<Locale> supportedLocales = [
     Locale('en', 'US'),
     Locale('es', 'ES'), // New language
   ];
   ```

2. **Translation Files**
   - Add JSON files for new languages
   - Update translation keys
   - Test RTL support if needed

#### Configuration

1. **App Configuration**
   ```dart
   // In core/config/config.dart
   const String panelUrl = 'https://your-domain.com/adminpanel';
   const String appName = 'Your Quiz App';
   const bool enableDebugMode = false;
   ```

2. **Admin Panel Configuration**
   ```php
   // In application/config/config.php
   $config['base_url'] = 'https://your-domain.com/';
   $config['encryption_key'] = 'your-encryption-key';
   ```

### Deployment

#### Flutter App Deployment

1. **Android Release**
   ```bash
   # Generate signed APK
   flutter build apk --release

   # Generate App Bundle
   flutter build appbundle --release
   ```

2. **iOS Release**
   ```bash
   # Build for iOS
   flutter build ios --release

   # Archive in Xcode for App Store
   ```

#### Admin Panel Deployment

1. **Server Setup**
   - Configure web server (Apache/Nginx)
   - Set up SSL certificate
   - Configure database connection
   - Set proper file permissions

2. **Security Considerations**
   - Change default admin credentials
   - Update encryption keys
   - Configure firewall rules
   - Enable HTTPS

---

This comprehensive documentation covers all aspects of the Elite Quiz project, from basic setup to advanced development practices. Use this as a reference for understanding, maintaining, and extending the application.
