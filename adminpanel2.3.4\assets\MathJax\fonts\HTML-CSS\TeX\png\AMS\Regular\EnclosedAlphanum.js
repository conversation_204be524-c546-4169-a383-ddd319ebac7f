/*
 *  /MathJax/fonts/HTML-CSS/TeX/png/AMS/Regular/EnclosedAlphanum.js
 *  
 *  Copyright (c) 2010-2013 The MathJax Consortium
 *
 *  Part of the MathJax library.
 *  See http://www.mathjax.org for details.
 * 
 *  Licensed under the Apache License, Version 2.0;
 *  you may not use this file except in compliance with the License.
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 */

MathJax.OutputJax["HTML-CSS"].defineImageData({MathJax_AMS:{9416:[[7,6,1],[8,8,2],[9,9,2],[11,10,2],[13,12,2],[15,15,3],[18,17,3],[21,20,4],[25,24,5],[30,29,6],[35,34,7],[42,41,8],[50,49,10],[59,58,12]]}});MathJax.Ajax.loadComplete(MathJax.OutputJax["HTML-CSS"].imgDir+"/AMS/Regular"+MathJax.OutputJax["HTML-CSS"].imgPacked+"/EnclosedAlphanum.js");

