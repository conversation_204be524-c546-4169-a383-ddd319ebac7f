[{"locale": "en"}, {"category": "Po", "mappings": {"default": {"default": "factorial operator", "alternative": "exclamation mark", "short": "factorial"}, "mathspeak": {"default": "exclamation-mark"}}, "key": "0021"}, {"category": "Po", "mappings": {"default": {"default": "quotation mark"}, "mathspeak": {"default": "quotation-mark"}}, "key": "0022"}, {"category": "Po", "mappings": {"default": {"default": "number sign", "alternative": "hash", "short": "number"}, "mathspeak": {"default": "number-sign", "brief": "num-sign", "sbrief": "num-sign"}}, "key": "0023"}, {"category": "Sc", "mappings": {"default": {"default": "dollar sign", "short": "dollar"}, "mathspeak": {"default": "dollar-sign"}}, "key": "0024"}, {"category": "Po", "mappings": {"default": {"default": "percent sign", "short": "percent"}, "mathspeak": {"default": "percent-sign"}}, "key": "0025"}, {"category": "Po", "mappings": {"default": {"default": "ampersand"}}, "key": "0026"}, {"category": "Po", "mappings": {"default": {"default": "apostrophe", "alternative": "apostrophe quote"}, "mathspeak": {"default": "prime"}}, "key": "0027"}, {"category": "Po", "mappings": {"default": {"default": "asterisk"}}, "key": "002A"}, {"category": "Sm", "mappings": {"default": {"default": "plus sign", "short": "plus"}}, "key": "002B"}, {"category": "Po", "mappings": {"default": {"default": "comma"}}, "key": "002C"}, {"category": "Pd", "mappings": {"default": {"default": "hyphen minus", "short": "minus"}, "mathspeak": {"default": "hyphen"}}, "key": "002D"}, {"category": "Po", "mappings": {"default": {"default": "full stop", "alternative": "period"}, "mathspeak": {"default": "period"}}, "key": "002E"}, {"category": "Po", "mappings": {"default": {"default": "solidus", "alternative": "slash"}, "mathspeak": {"default": "slash"}, "emacspeak": {"default": "slash"}}, "key": "002F"}, {"category": "Po", "mappings": {"default": {"default": "colon"}}, "key": "003A"}, {"category": "Po", "mappings": {"default": {"default": "semicolon"}}, "key": "003B"}, {"category": "Sm", "mappings": {"default": {"default": "less than sign", "short": "less than"}, "mathspeak": {"default": "less-than"}}, "key": "003C"}, {"category": "Sm", "mappings": {"default": {"default": "equals sign", "short": "equals"}}, "key": "003D"}, {"category": "Sm", "mappings": {"default": {"default": "greater than sign", "short": "greater than"}, "mathspeak": {"default": "greater-than"}}, "key": "003E"}, {"category": "Po", "mappings": {"default": {"default": "question mark"}, "mathspeak": {"default": "question-mark"}}, "key": "003F"}, {"category": "Po", "mappings": {"default": {"default": "commercial at", "short": "at"}, "mathspeak": {"default": "commercial-at"}}, "key": "0040"}, {"category": "Po", "mappings": {"default": {"default": "reverse solidus", "alternative": "backslash"}, "mathspeak": {"default": "reverse-solidus"}}, "key": "005C"}, {"category": "Sk", "mappings": {"default": {"default": "circumflex accent", "alternative": "spacing circumflex", "short": "hat"}, "mathspeak": {"default": "caret"}}, "key": "005E"}, {"category": "Pc", "mappings": {"default": {"default": "low line", "alternative": "spacing underscore"}, "mathspeak": {"default": "bar"}}, "key": "005F"}, {"category": "Sk", "mappings": {"default": {"default": "grave accent", "alternative": "spacing grave", "short": "grave"}, "mathspeak": {"default": "grave"}}, "key": "0060"}, {"category": "Sm", "mappings": {"default": {"default": "vertical line", "alternative": "vertical bar"}, "mathspeak": {"default": "vertical-bar"}}, "key": "007C"}, {"category": "Sm", "mappings": {"default": {"default": "tilde"}}, "key": "007E"}, {"category": "Po", "mappings": {"default": {"default": "inverted exclamation mark"}, "mathspeak": {"default": "inverted-exclamation-mark"}}, "key": "00A1"}, {"category": "Sc", "mappings": {"default": {"default": "cent sign", "short": "cent"}, "mathspeak": {"default": "cent-sign"}}, "key": "00A2"}, {"category": "Sc", "mappings": {"default": {"default": "pound sign", "short": "pound"}, "mathspeak": {"default": "pound-sign"}}, "key": "00A3"}, {"category": "Sc", "mappings": {"default": {"default": "currency sign", "short": "currency"}, "mathspeak": {"default": "currency-sign"}}, "key": "00A4"}, {"category": "Sc", "mappings": {"default": {"default": "yen sign", "short": "yen"}, "mathspeak": {"default": "yen-sign"}}, "key": "00A5"}, {"category": "So", "mappings": {"default": {"default": "broken bar", "alternative": "broken vertical bar"}, "mathspeak": {"default": "broken-vertical-bar"}}, "key": "00A6"}, {"category": "Po", "mappings": {"default": {"default": "section sign", "short": "section"}, "mathspeak": {"default": "section-sign"}}, "key": "00A7"}, {"category": "Sk", "mappings": {"default": {"default": "diaeresis", "alternative": "spacing diaeresis", "short": "double dot"}, "mathspeak": {"default": "two-dots"}}, "key": "00A8"}, {"category": "So", "mappings": {"default": {"default": "copyright sign", "short": "copyright"}, "mathspeak": {"default": "copyright-sign"}}, "key": "00A9"}, {"category": "Lo", "mappings": {"default": {"default": "feminine ordinal indicator"}, "mathspeak": {"default": "feminine-ordinal-indicator"}}, "key": "00AA"}, {"category": "Pi", "mappings": {"default": {"default": "left pointing double angle quotation mark", "alternative": "left pointing guillemet"}, "mathspeak": {"default": "left-pointing-guil<PERSON><PERSON>"}}, "key": "00AB"}, {"category": "Sm", "mappings": {"default": {"default": "not sign", "short": "not"}, "mathspeak": {"default": "not-sign"}}, "key": "00AC"}, {"category": "So", "mappings": {"default": {"default": "registered sign", "alternative": "registered trade mark sign", "short": "registered"}, "mathspeak": {"default": "registered-trade-mark-sign"}}, "key": "00AE"}, {"category": "Sk", "mappings": {"default": {"default": "macron", "alternative": "spacing macron"}, "mathspeak": {"default": "bar"}}, "key": "00AF"}, {"category": "So", "mappings": {"default": {"default": "degree sign", "short": "degree"}, "mathspeak": {"default": "degree"}}, "key": "00B0"}, {"category": "Sm", "mappings": {"default": {"default": "plus minus sign", "alternative": "plus or minus sign", "short": "plus minus"}, "mathspeak": {"default": "plus-or-minus"}}, "key": "00B1"}, {"category": "Sk", "mappings": {"default": {"default": "acute accent", "alternative": "spacing acute", "short": "acute"}, "mathspeak": {"default": "acute"}}, "key": "00B4"}, {"category": "Ll", "mappings": {"default": {"default": "micro sign", "short": "micro"}, "mathspeak": {"default": "micro-sign"}}, "key": "00B5"}, {"category": "Po", "mappings": {"default": {"default": "pilcrow sign", "alternative": "paragraph sign", "short": "pilcrow"}, "mathspeak": {"default": "paragraph-sign"}}, "key": "00B6"}, {"category": "Po", "mappings": {"default": {"default": "middle dot"}, "mathspeak": {"default": "dot"}}, "key": "00B7"}, {"category": "Sk", "mappings": {"default": {"default": "cedilla", "alternative": "spacing cedilla"}, "mathspeak": {"default": "cedilla"}}, "key": "00B8"}, {"category": "Lo", "mappings": {"default": {"default": "masculine ordinal indicator"}, "mathspeak": {"default": "masculine-ordinal-indicator"}}, "key": "00BA"}, {"category": "Pf", "mappings": {"default": {"default": "right pointing double angle quotation mark", "alternative": "right pointing guillemet"}, "mathspeak": {"default": "right-pointing-guil<PERSON><PERSON>"}}, "key": "00BB"}, {"category": "Po", "mappings": {"default": {"default": "inverted question mark"}, "mathspeak": {"default": "inverted-question-mark"}}, "key": "00BF"}, {"category": "Sm", "mappings": {"default": {"default": "multiplication sign", "short": "multiplication"}, "mathspeak": {"default": "times"}}, "key": "00D7"}, {"category": "Sm", "mappings": {"default": {"default": "division sign", "short": "division"}, "mathspeak": {"default": "division-sign"}}, "key": "00F7"}, {"category": "Sk", "mappings": {"default": {"default": "breve", "alternative": "spacing breve"}, "mathspeak": {"default": "breve"}}, "key": "02D8"}, {"category": "Sk", "mappings": {"default": {"default": "dot above", "alternative": "spacing dot above"}, "mathspeak": {"default": "dot"}}, "key": "02D9"}, {"category": "Sk", "mappings": {"default": {"default": "ring above", "alternative": "spacing ring above"}, "mathspeak": {"default": "ring-above"}}, "key": "02DA"}, {"category": "Sk", "mappings": {"default": {"default": "ogonek", "alternative": "spacing ogonek"}, "mathspeak": {"default": "ogonek"}}, "key": "02DB"}, {"category": "Sk", "mappings": {"default": {"default": "small tilde", "alternative": "spacing tilde"}, "mathspeak": {"default": "tilde"}}, "key": "02DC"}, {"category": "Sk", "mappings": {"default": {"default": "double acute accent", "alternative": "spacing double acute"}, "mathspeak": {"default": "double-acute"}}, "key": "02DD"}, {"category": "Pd", "mappings": {"default": {"default": "hyphen"}}, "key": "2010"}, {"category": "Pd", "mappings": {"default": {"default": "non breaking hyphen"}, "mathspeak": {"default": "non-breaking-hyphen"}}, "key": "2011"}, {"category": "Pd", "mappings": {"default": {"default": "figure dash"}, "mathspeak": {"default": "figure-dash"}}, "key": "2012"}, {"category": "Pd", "mappings": {"default": {"default": "en dash"}, "mathspeak": {"default": "en-dash"}}, "key": "2013"}, {"category": "Pd", "mappings": {"default": {"default": "em dash"}, "mathspeak": {"default": "em-dash"}}, "key": "2014"}, {"category": "Pd", "mappings": {"default": {"default": "horizontal bar", "alternative": "quotation dash"}, "mathspeak": {"default": "quotation-dash"}}, "key": "2015"}, {"category": "Po", "mappings": {"default": {"default": "double vertical line", "alternative": "double vertical bar"}, "mathspeak": {"default": "double-vertical-bar"}}, "key": "2016"}, {"category": "Po", "mappings": {"default": {"default": "double low line", "alternative": "spacing double underscore"}, "mathspeak": {"default": "double-underscore"}}, "key": "2017"}, {"category": "Pi", "mappings": {"default": {"default": "left single quotation mark", "alternative": "single turned comma quotation mark"}, "mathspeak": {"default": "single-turned-comma-quotation-mark"}}, "key": "2018"}, {"category": "Pf", "mappings": {"default": {"default": "right single quotation mark", "alternative": "single comma quotation mark"}, "mathspeak": {"default": "single-comma-quotation-mark"}}, "key": "2019"}, {"category": "Ps", "mappings": {"default": {"default": "single low 9 quotation mark", "alternative": "low single comma quotation mark"}, "mathspeak": {"default": "low-single-comma-quotation-mark"}}, "key": "201A"}, {"category": "Pi", "mappings": {"default": {"default": "single high reversed 9 quotation mark", "alternative": "single reversed comma quotation mark"}, "mathspeak": {"default": "single-reversed-comma-quotation-mark"}}, "key": "201B"}, {"category": "Pi", "mappings": {"default": {"default": "left double quotation mark", "alternative": "double turned comma quotation mark"}, "mathspeak": {"default": "double-turned-comma-quotation-mark"}}, "key": "201C"}, {"category": "Pf", "mappings": {"default": {"default": "right double quotation mark", "alternative": "double comma quotation mark"}, "mathspeak": {"default": "double-comma-quotation-mark"}}, "key": "201D"}, {"category": "Ps", "mappings": {"default": {"default": "double low 9 quotation mark", "alternative": "low double comma quotation mark"}, "mathspeak": {"default": "low-double-comma-quotation-mark"}}, "key": "201E"}, {"category": "Pi", "mappings": {"default": {"default": "double high reversed 9 quotation mark", "alternative": "double reversed comma quotation mark"}, "mathspeak": {"default": "double-reversed-comma-quotation-mark"}}, "key": "201F"}, {"category": "Po", "mappings": {"default": {"default": "dagger"}}, "key": "2020"}, {"category": "Po", "mappings": {"default": {"default": "double dagger"}, "mathspeak": {"default": "double-dagger"}}, "key": "2021"}, {"category": "Po", "mappings": {"default": {"default": "bullet"}}, "key": "2022"}, {"category": "Po", "mappings": {"default": {"default": "triangular bullet"}, "mathspeak": {"default": "triangular-bullet"}}, "key": "2023"}, {"category": "Po", "mappings": {"default": {"default": "one dot leader"}, "mathspeak": {"default": "one-dot-leader"}}, "key": "2024"}, {"category": "Po", "mappings": {"default": {"default": "two dot leader"}, "mathspeak": {"default": "two-dot-leader"}}, "key": "2025"}, {"category": "Po", "mappings": {"default": {"default": "horizontal ellipsis"}, "mathspeak": {"default": "ellipsis"}}, "key": "2026"}, {"category": "Po", "mappings": {"default": {"default": "hyphenation point"}, "mathspeak": {"default": "hyphenation-point"}}, "key": "2027"}, {"category": "Po", "mappings": {"default": {"default": "per mille sign", "short": "per mille"}, "mathspeak": {"default": "per-mille"}}, "key": "2030"}, {"category": "Po", "mappings": {"default": {"default": "per ten thousand sign", "short": "per ten thousand"}, "mathspeak": {"default": "per-ten-thousand"}}, "key": "2031"}, {"category": "Po", "mappings": {"default": {"default": "prime"}}, "key": "2032"}, {"category": "Po", "mappings": {"default": {"default": "double prime"}, "mathspeak": {"default": "double-prime"}}, "key": "2033"}, {"category": "Po", "mappings": {"default": {"default": "triple prime"}, "mathspeak": {"default": "triple-prime"}}, "key": "2034"}, {"category": "Po", "mappings": {"default": {"default": "reversed prime"}, "mathspeak": {"default": "reversed-prime"}}, "key": "2035"}, {"category": "Po", "mappings": {"default": {"default": "reversed double prime"}, "mathspeak": {"default": "reversed-double-prime"}}, "key": "2036"}, {"category": "Po", "mappings": {"default": {"default": "reversed triple prime"}, "mathspeak": {"default": "reversed-triple-prime"}}, "key": "2037"}, {"category": "Po", "mappings": {"default": {"default": "caret"}}, "key": "2038"}, {"category": "Pi", "mappings": {"default": {"default": "single left pointing angle quotation mark", "alternative": "left pointing single guillemet"}, "mathspeak": {"default": "left-pointing-single-guillemet"}}, "key": "2039"}, {"category": "Pf", "mappings": {"default": {"default": "single right pointing angle quotation mark", "alternative": "right pointing single guillemet"}, "mathspeak": {"default": "right-pointing-single-guillem<PERSON>"}}, "key": "203A"}, {"category": "Po", "mappings": {"default": {"default": "reference mark"}, "mathspeak": {"default": "reference-mark"}}, "key": "203B"}, {"category": "Po", "mappings": {"default": {"default": "double exclamation mark"}, "mathspeak": {"default": "double-exclamation-mark"}}, "key": "203C"}, {"category": "Po", "mappings": {"default": {"default": "interrobang"}}, "key": "203D"}, {"category": "Po", "mappings": {"default": {"default": "overline", "alternative": "spacing overscore"}, "mathspeak": {"default": "bar"}}, "key": "203E"}, {"category": "Pc", "mappings": {"default": {"default": "undertie"}}, "key": "203F"}, {"category": "Pc", "mappings": {"default": {"default": "character tie"}, "mathspeak": {"default": "character-tie"}}, "key": "2040"}, {"category": "Po", "mappings": {"default": {"default": "caret insertion point"}, "mathspeak": {"default": "caret-insertion-point"}}, "key": "2041"}, {"category": "Po", "mappings": {"default": {"default": "asterism"}}, "key": "2042"}, {"category": "Po", "mappings": {"default": {"default": "hyphen bullet"}, "mathspeak": {"default": "hyphen-bullet"}}, "key": "2043"}, {"category": "Sm", "mappings": {"default": {"default": "fraction slash"}, "mathspeak": {"default": "fraction-slash"}}, "key": "2044"}, {"category": "Po", "mappings": {"default": {"default": "double question mark"}, "mathspeak": {"default": "double-question-mark"}}, "key": "2047"}, {"category": "Po", "mappings": {"default": {"default": "question exclamation mark"}, "mathspeak": {"default": "question-exclamation-mark"}}, "key": "2048"}, {"category": "Po", "mappings": {"default": {"default": "exclamation question mark"}, "mathspeak": {"default": "exclamation-question-mark"}}, "key": "2049"}, {"category": "Po", "mappings": {"default": {"default": "reversed pilcrow sign", "short": "reversed pilcrow"}, "mathspeak": {"default": "reversed-pilcrow"}}, "key": "204B"}, {"category": "Po", "mappings": {"default": {"default": "black leftwards bullet"}, "mathspeak": {"default": "black-leftwards-bullet"}}, "key": "204C"}, {"category": "Po", "mappings": {"default": {"default": "black rightwards bullet"}, "mathspeak": {"default": "black-rightwards-bullet"}}, "key": "204D"}, {"category": "Po", "mappings": {"default": {"default": "low asterisk"}, "mathspeak": {"default": "low-asterisk"}}, "key": "204E"}, {"category": "Po", "mappings": {"default": {"default": "reversed semicolon"}, "mathspeak": {"default": "reversed-semicolon"}}, "key": "204F"}, {"category": "Po", "mappings": {"default": {"default": "close up"}, "mathspeak": {"default": "close-up"}}, "key": "2050"}, {"category": "Po", "mappings": {"default": {"default": "two asterisks aligned vertically"}, "mathspeak": {"default": "two-asterisks-aligned-vertically"}}, "key": "2051"}, {"category": "Sm", "mappings": {"default": {"default": "commercial minus sign", "short": "commercial minus"}, "mathspeak": {"default": "commercial-minus"}}, "key": "2052"}, {"category": "Po", "mappings": {"default": {"default": "swung dash"}, "mathspeak": {"default": "swung-dash"}}, "key": "2053"}, {"category": "Pc", "mappings": {"default": {"default": "inverted undertie"}, "mathspeak": {"default": "inverted-undertie"}}, "key": "2054"}, {"category": "Po", "mappings": {"default": {"default": "flower punctuation mark"}, "mathspeak": {"default": "flower-punctuation-mark"}}, "key": "2055"}, {"category": "Po", "mappings": {"default": {"default": "three dot punctuation"}, "mathspeak": {"default": "three-dot-punctuation"}}, "key": "2056"}, {"category": "Po", "mappings": {"default": {"default": "quadruple prime"}, "mathspeak": {"default": "quadruple-prime"}}, "key": "2057"}, {"category": "Po", "mappings": {"default": {"default": "four dot punctuation"}, "mathspeak": {"default": "four-dot-punctuation"}}, "key": "2058"}, {"category": "Po", "mappings": {"default": {"default": "five dot punctuation"}, "mathspeak": {"default": "five-dot-punctuation"}}, "key": "2059"}, {"category": "Po", "mappings": {"default": {"default": "two dot punctuation"}, "mathspeak": {"default": "two-dot-punctuation"}}, "key": "205A"}, {"category": "Po", "mappings": {"default": {"default": "four dot mark"}, "mathspeak": {"default": "four-dot-mark"}}, "key": "205B"}, {"category": "Po", "mappings": {"default": {"default": "dotted cross"}, "mathspeak": {"default": "dotted-cross"}}, "key": "205C"}, {"category": "Po", "mappings": {"default": {"default": "tricolon"}}, "key": "205D"}, {"category": "Po", "mappings": {"default": {"default": "vertical four dots"}, "mathspeak": {"default": "vertical-four-dots"}}, "key": "205E"}, {"category": "Sm", "mappings": {"default": {"default": "superscript plus sign", "short": "superscript plus"}, "mathspeak": {"default": "superscript-plus"}}, "key": "207A"}, {"category": "Sm", "mappings": {"default": {"default": "superscript minus", "alternative": "superscript hyphen minus"}, "mathspeak": {"default": "superscript-hyphen-minus"}}, "key": "207B"}, {"category": "Sm", "mappings": {"default": {"default": "superscript equals sign", "short": "superscript equals"}, "mathspeak": {"default": "superscript-equals"}}, "key": "207C"}, {"category": "Ps", "mappings": {"default": {"default": "superscript left parenthesis", "alternative": "superscript opening parenthesis"}, "mathspeak": {"default": "superscript-opening-parenthesis"}}, "key": "207D"}, {"category": "Pe", "mappings": {"default": {"default": "superscript right parenthesis", "alternative": "superscript closing parenthesis"}, "mathspeak": {"default": "superscript-closing-parenthesis"}}, "key": "207E"}, {"category": "Sm", "mappings": {"default": {"default": "subscript plus sign", "short": "subscript plus"}, "mathspeak": {"default": "subscript-plus"}}, "key": "208A"}, {"category": "Sm", "mappings": {"default": {"default": "subscript minus", "alternative": "subscript hyphen minus"}, "mathspeak": {"default": "subscript-hyphen-minus"}}, "key": "208B"}, {"category": "Sm", "mappings": {"default": {"default": "subscript equals sign", "short": "subscript equals"}, "mathspeak": {"default": "subscript-equals"}}, "key": "208C"}, {"category": "Ps", "mappings": {"default": {"default": "subscript left parenthesis", "alternative": "subscript opening parenthesis"}, "mathspeak": {"default": "subscript-opening-parenthesis"}}, "key": "208D"}, {"category": "Pe", "mappings": {"default": {"default": "subscript right parenthesis", "alternative": "subscript closing parenthesis"}, "mathspeak": {"default": "subscript-closing-parenthesis"}}, "key": "208E"}, {"category": "So", "mappings": {"default": {"default": "property line"}, "mathspeak": {"default": "property-line"}}, "key": "214A"}, {"category": "Sm", "mappings": {"default": {"default": "turned ampersand"}, "mathspeak": {"default": "turned-ampersand"}}, "key": "214B"}, {"category": "So", "mappings": {"default": {"default": "per sign", "short": "per"}, "mathspeak": {"default": "per-sign"}}, "key": "214C"}, {"category": "So", "mappings": {"default": {"default": "aktieselskab"}}, "key": "214D"}, {"category": "Ll", "mappings": {"default": {"default": "turned small f"}, "mathspeak": {"default": "turned-small-f"}}, "key": "214E"}, {"category": "Sm", "mappings": {"default": {"default": "for all"}, "mathspeak": {"default": "for-all"}}, "key": "2200"}, {"category": "Sm", "mappings": {"default": {"default": "complement"}}, "key": "2201"}, {"category": "Sm", "mappings": {"default": {"default": "partial differential"}, "mathspeak": {"default": "partial-differential"}}, "key": "2202"}, {"category": "Sm", "mappings": {"default": {"default": "there exists"}, "mathspeak": {"default": "there-exists"}}, "key": "2203"}, {"category": "Sm", "mappings": {"default": {"default": "there does not exist"}, "mathspeak": {"default": "there-does-not-exist"}}, "key": "2204"}, {"category": "Sm", "mappings": {"default": {"default": "empty set"}, "mathspeak": {"default": "empty-set"}}, "key": "2205"}, {"category": "Sm", "mappings": {"default": {"default": "increment"}}, "key": "2206"}, {"category": "Sm", "mappings": {"default": {"default": "nabla"}}, "key": "2207"}, {"category": "Sm", "mappings": {"default": {"default": "element of"}, "mathspeak": {"default": "element-of"}}, "key": "2208"}, {"category": "Sm", "mappings": {"default": {"default": "not an element of"}, "mathspeak": {"default": "not-an-element-of"}}, "key": "2209"}, {"category": "Sm", "mappings": {"default": {"default": "small element of"}, "mathspeak": {"default": "small-element-of"}}, "key": "220A"}, {"category": "Sm", "mappings": {"default": {"default": "contains as member"}, "mathspeak": {"default": "contains-as-member"}}, "key": "220B"}, {"category": "Sm", "mappings": {"default": {"default": "does not contain as member"}, "mathspeak": {"default": "does-not-contain-as-member"}}, "key": "220C"}, {"category": "Sm", "mappings": {"default": {"default": "small contains as member"}, "mathspeak": {"default": "small-contains-as-member"}}, "key": "220D"}, {"category": "Sm", "mappings": {"default": {"default": "end of proof"}, "mathspeak": {"default": "end-of-proof"}}, "key": "220E"}, {"category": "Sm", "mappings": {"default": {"default": "n ary product"}, "mathspeak": {"default": "product"}}, "key": "220F"}, {"category": "Sm", "mappings": {"default": {"default": "n ary coproduct"}, "mathspeak": {"default": "coproduct"}}, "key": "2210"}, {"category": "Sm", "mappings": {"default": {"default": "n ary summation"}, "mathspeak": {"default": "sigma-summation"}}, "key": "2211"}, {"category": "Sm", "mappings": {"default": {"default": "minus sign", "short": "minus"}}, "key": "2212"}, {"category": "Sm", "mappings": {"default": {"default": "minus or plus sign", "short": "minus or plus"}, "mathspeak": {"default": "minus-or-plus"}}, "key": "2213"}, {"category": "Sm", "mappings": {"default": {"default": "dot plus"}, "mathspeak": {"default": "dot-plus"}}, "key": "2214"}, {"category": "Sm", "mappings": {"default": {"default": "division slash"}, "mathspeak": {"default": "division-slash"}}, "key": "2215"}, {"category": "Sm", "mappings": {"default": {"default": "set minus"}, "mathspeak": {"default": "set-minus"}}, "key": "2216"}, {"category": "Sm", "mappings": {"default": {"default": "asterisk operator"}, "mathspeak": {"default": "asterisk"}}, "key": "2217"}, {"category": "Sm", "mappings": {"default": {"default": "ring operator"}, "mathspeak": {"default": "ring"}}, "key": "2218"}, {"category": "Sm", "mappings": {"default": {"default": "bullet operator"}, "mathspeak": {"default": "bullet"}}, "key": "2219"}, {"category": "Sm", "mappings": {"default": {"default": "square root"}, "mathspeak": {"default": "square-root"}}, "key": "221A"}, {"category": "Sm", "mappings": {"default": {"default": "cube root"}, "mathspeak": {"default": "cube-root"}}, "key": "221B"}, {"category": "Sm", "mappings": {"default": {"default": "fourth root"}, "mathspeak": {"default": "fourth-root"}}, "key": "221C"}, {"category": "Sm", "mappings": {"default": {"default": "proportional to"}, "mathspeak": {"default": "proportional-to"}}, "key": "221D"}, {"category": "Sm", "mappings": {"default": {"default": "infinity"}}, "key": "221E"}, {"category": "Sm", "mappings": {"default": {"default": "right angle"}, "mathspeak": {"default": "right-angle"}}, "key": "221F"}, {"category": "Sm", "mappings": {"default": {"default": "angle"}}, "key": "2220"}, {"category": "Sm", "mappings": {"default": {"default": "measured angle"}, "mathspeak": {"default": "measured-angle"}}, "key": "2221"}, {"category": "Sm", "mappings": {"default": {"default": "spherical angle"}, "mathspeak": {"default": "spherical-angle"}}, "key": "2222"}, {"category": "Sm", "mappings": {"default": {"default": "divides", "short": "bar"}}, "key": "2223"}, {"category": "Sm", "mappings": {"default": {"default": "does not divide"}, "mathspeak": {"default": "does-not-divide"}}, "key": "2224"}, {"category": "Sm", "mappings": {"default": {"default": "parallel to"}, "mathspeak": {"default": "parallel-to"}}, "key": "2225"}, {"category": "Sm", "mappings": {"default": {"default": "not parallel to"}, "mathspeak": {"default": "not-parallel-to"}}, "key": "2226"}, {"category": "Sm", "mappings": {"default": {"default": "logical and"}, "mathspeak": {"default": "logical-and"}}, "key": "2227"}, {"category": "Sm", "mappings": {"default": {"default": "logical or"}, "mathspeak": {"default": "logical-or"}}, "key": "2228"}, {"category": "Sm", "mappings": {"default": {"default": "intersection"}}, "key": "2229"}, {"category": "Sm", "mappings": {"default": {"default": "union"}}, "key": "222A"}, {"category": "Sm", "mappings": {"default": {"default": "integral"}}, "key": "222B"}, {"category": "Sm", "mappings": {"default": {"default": "double integral"}, "mathspeak": {"default": "double-integral"}}, "key": "222C"}, {"category": "Sm", "mappings": {"default": {"default": "triple integral"}, "mathspeak": {"default": "triple-integral"}}, "key": "222D"}, {"category": "Sm", "mappings": {"default": {"default": "contour integral"}, "mathspeak": {"default": "contour-integral"}}, "key": "222E"}, {"category": "Sm", "mappings": {"default": {"default": "surface integral"}, "mathspeak": {"default": "surface-integral"}}, "key": "222F"}, {"category": "Sm", "mappings": {"default": {"default": "volume integral"}, "mathspeak": {"default": "volume-integral"}}, "key": "2230"}, {"category": "Sm", "mappings": {"default": {"default": "clockwise integral"}, "mathspeak": {"default": "clockwise-integral"}}, "key": "2231"}, {"category": "Sm", "mappings": {"default": {"default": "clockwise contour integral"}, "mathspeak": {"default": "clockwise-contour-integral"}}, "key": "2232"}, {"category": "Sm", "mappings": {"default": {"default": "anticlockwise contour integral"}, "mathspeak": {"default": "anticlockwise-contour-integral"}}, "key": "2233"}, {"category": "Sm", "mappings": {"default": {"default": "therefore"}}, "key": "2234"}, {"category": "Sm", "mappings": {"default": {"default": "because"}}, "key": "2235"}, {"category": "Sm", "mappings": {"default": {"default": "ratio"}}, "key": "2236"}, {"category": "Sm", "mappings": {"default": {"default": "proportion"}}, "key": "2237"}, {"category": "Sm", "mappings": {"default": {"default": "dot minus"}, "mathspeak": {"default": "dot-minus"}}, "key": "2238"}, {"category": "Sm", "mappings": {"default": {"default": "excess"}}, "key": "2239"}, {"category": "Sm", "mappings": {"default": {"default": "geometric proportion"}, "mathspeak": {"default": "geometric-proportion"}}, "key": "223A"}, {"category": "Sm", "mappings": {"default": {"default": "homothetic"}}, "key": "223B"}, {"category": "Sm", "mappings": {"default": {"default": "tilde operator"}, "mathspeak": {"default": "tilde"}}, "key": "223C"}, {"category": "Sm", "mappings": {"default": {"default": "reversed tilde"}, "mathspeak": {"default": "reversed-tilde"}}, "key": "223D"}, {"category": "Sm", "mappings": {"default": {"default": "inverted lazy s"}, "mathspeak": {"default": "inverted-lazy-s"}}, "key": "223E"}, {"category": "Sm", "mappings": {"default": {"default": "sine wave"}, "mathspeak": {"default": "sine-wave"}}, "key": "223F"}, {"category": "Sm", "mappings": {"default": {"default": "wreath product"}, "mathspeak": {"default": "wreath-product"}}, "key": "2240"}, {"category": "Sm", "mappings": {"default": {"default": "not tilde"}, "mathspeak": {"default": "not-tilde"}}, "key": "2241"}, {"category": "Sm", "mappings": {"default": {"default": "minus tilde"}, "mathspeak": {"default": "minus-tilde"}}, "key": "2242"}, {"category": "Sm", "mappings": {"default": {"default": "asymptotically equals"}, "mathspeak": {"default": "asymptotically-equals"}}, "key": "2243"}, {"category": "Sm", "mappings": {"default": {"default": "not asymptotically equals"}, "mathspeak": {"default": "not-asymptotically-equals"}}, "key": "2244"}, {"category": "Sm", "mappings": {"default": {"default": "approximately equals"}, "mathspeak": {"default": "approximately-equals"}}, "key": "2245"}, {"category": "Sm", "mappings": {"default": {"default": "approximately but not actually equals"}, "mathspeak": {"default": "approximately-but-not-actually-equals"}}, "key": "2246"}, {"category": "Sm", "mappings": {"default": {"default": "neither approximately nor actually equals"}, "mathspeak": {"default": "neither-approximately-nor-actually-equals"}}, "key": "2247"}, {"category": "Sm", "mappings": {"default": {"default": "almost equals"}, "mathspeak": {"default": "almost-equals"}}, "key": "2248"}, {"category": "Sm", "mappings": {"default": {"default": "not almost equals"}, "mathspeak": {"default": "not-almost-equals"}}, "key": "2249"}, {"category": "Sm", "mappings": {"default": {"default": "almost equal or equals"}, "mathspeak": {"default": "almost-equal-or-equal-to"}}, "key": "224A"}, {"category": "Sm", "mappings": {"default": {"default": "triple tilde"}, "mathspeak": {"default": "triple-tilde"}}, "key": "224B"}, {"category": "Sm", "mappings": {"default": {"default": "all equals"}, "mathspeak": {"default": "all-equals"}}, "key": "224C"}, {"category": "Sm", "mappings": {"default": {"default": "equivalent to"}, "mathspeak": {"default": "equivalent-to"}}, "key": "224D"}, {"category": "Sm", "mappings": {"default": {"default": "geometrically equivalent to"}, "mathspeak": {"default": "geometrically-equivalent-to"}}, "key": "224E"}, {"category": "Sm", "mappings": {"default": {"default": "difference between"}, "mathspeak": {"default": "difference-between"}}, "key": "224F"}, {"category": "Sm", "mappings": {"default": {"default": "approaches the limit"}, "mathspeak": {"default": "approaches-the-limit"}}, "key": "2250"}, {"category": "Sm", "mappings": {"default": {"default": "geometrically equals"}, "mathspeak": {"default": "geometrically-equals"}}, "key": "2251"}, {"category": "Sm", "mappings": {"default": {"default": "approximately equals or the image of"}, "mathspeak": {"default": "approximately-equals-or-the-image-of"}}, "key": "2252"}, {"category": "Sm", "mappings": {"default": {"default": "image of or approximately equals"}, "mathspeak": {"default": "image-of-or-approximately-equals"}}, "key": "2253"}, {"category": "Sm", "mappings": {"default": {"default": "colon equals", "alternative": "colon equal"}, "mathspeak": {"default": "colon-equal"}}, "key": "2254"}, {"category": "Sm", "mappings": {"default": {"default": "equals colon", "alternative": "equal colon"}, "mathspeak": {"default": "equal-colon"}}, "key": "2255"}, {"category": "Sm", "mappings": {"default": {"default": "ring in equals"}, "mathspeak": {"default": "ring-in-equals"}}, "key": "2256"}, {"category": "Sm", "mappings": {"default": {"default": "ring equals"}, "mathspeak": {"default": "ring-equals"}}, "key": "2257"}, {"category": "Sm", "mappings": {"default": {"default": "corresponds to"}, "mathspeak": {"default": "corresponds-to"}}, "key": "2258"}, {"category": "Sm", "mappings": {"default": {"default": "estimates"}}, "key": "2259"}, {"category": "Sm", "mappings": {"default": {"default": "equiangular to"}, "mathspeak": {"default": "equiangular-to"}}, "key": "225A"}, {"category": "Sm", "mappings": {"default": {"default": "star equals"}, "mathspeak": {"default": "star-equals"}}, "key": "225B"}, {"category": "Sm", "mappings": {"default": {"default": "delta equals"}, "mathspeak": {"default": "delta-equals"}}, "key": "225C"}, {"category": "Sm", "mappings": {"default": {"default": "equals by definition"}, "mathspeak": {"default": "equals-by-definition"}}, "key": "225D"}, {"category": "Sm", "mappings": {"default": {"default": "measured by"}, "mathspeak": {"default": "measured-by"}}, "key": "225E"}, {"category": "Sm", "mappings": {"default": {"default": "questioned equals"}, "mathspeak": {"default": "questioned-equals"}}, "key": "225F"}, {"category": "Sm", "mappings": {"default": {"default": "not equals"}, "mathspeak": {"default": "not-equals"}}, "key": "2260"}, {"category": "Sm", "mappings": {"default": {"default": "identical to"}, "mathspeak": {"default": "identical-to"}}, "key": "2261"}, {"category": "Sm", "mappings": {"default": {"default": "not identical to"}, "mathspeak": {"default": "not-identical-to"}}, "key": "2262"}, {"category": "Sm", "mappings": {"default": {"default": "strictly equivalent to"}, "mathspeak": {"default": "strictly-equivalent-to"}}, "key": "2263"}, {"category": "Sm", "mappings": {"default": {"default": "less than or equals", "alternative": "less than or equals"}, "mathspeak": {"default": "less-than-or-equal-to"}}, "key": "2264"}, {"category": "Sm", "mappings": {"default": {"default": "greater than or equals", "alternative": "greater than or equals"}, "mathspeak": {"default": "greater-than-or-equal-to"}}, "key": "2265"}, {"category": "Sm", "mappings": {"default": {"default": "less than over equals", "alternative": "less than over equals"}, "mathspeak": {"default": "less-than-over-equals"}}, "key": "2266"}, {"category": "Sm", "mappings": {"default": {"default": "greater than over equals", "alternative": "greater than over equals"}, "mathspeak": {"default": "greater-than-over-equals"}}, "key": "2267"}, {"category": "Sm", "mappings": {"default": {"default": "less than but not equals", "alternative": "less than but not equals"}, "mathspeak": {"default": "less-than-but-not-equals"}}, "key": "2268"}, {"category": "Sm", "mappings": {"default": {"default": "greater than but not equals", "alternative": "greater than but not equals"}, "mathspeak": {"default": "greater-than-but-not-equals"}}, "key": "2269"}, {"category": "Sm", "mappings": {"default": {"default": "much less than", "alternative": "much less than"}, "mathspeak": {"default": "much-less-than"}}, "key": "226A"}, {"category": "Sm", "mappings": {"default": {"default": "much greater than", "alternative": "much greater than"}, "mathspeak": {"default": "much-greater-than"}}, "key": "226B"}, {"category": "Sm", "mappings": {"default": {"default": "between"}}, "key": "226C"}, {"category": "Sm", "mappings": {"default": {"default": "not equivalent to"}, "mathspeak": {"default": "not-equivalent-to"}}, "key": "226D"}, {"category": "Sm", "mappings": {"default": {"default": "not less than", "alternative": "not less than"}, "mathspeak": {"default": "not-less-than"}}, "key": "226E"}, {"category": "Sm", "mappings": {"default": {"default": "not greater than", "alternative": "not greater than"}, "mathspeak": {"default": "not-greater-than"}}, "key": "226F"}, {"category": "Sm", "mappings": {"default": {"default": "neither less than nor equals", "alternative": "neither less than nor equals"}, "mathspeak": {"default": "neither-less-than-nor-equal-to"}}, "key": "2270"}, {"category": "Sm", "mappings": {"default": {"default": "neither greater than nor equals", "alternative": "neither greater than nor equals"}, "mathspeak": {"default": "neither-greater-than-nor-equal-to"}}, "key": "2271"}, {"category": "Sm", "mappings": {"default": {"default": "less than or equivalent to", "alternative": "less than or equivalent to"}, "mathspeak": {"default": "less-than-or-equivalent-to"}}, "key": "2272"}, {"category": "Sm", "mappings": {"default": {"default": "greater than or equivalent to", "alternative": "greater than or equivalent to"}, "mathspeak": {"default": "greater-than-or-equivalent-to"}}, "key": "2273"}, {"category": "Sm", "mappings": {"default": {"default": "neither less than nor equivalent to", "alternative": "neither less than nor equivalent to"}, "mathspeak": {"default": "neither-less-than-nor-equivalent-to"}}, "key": "2274"}, {"category": "Sm", "mappings": {"default": {"default": "neither greater than nor equivalent to", "alternative": "neither greater than nor equivalent to"}, "mathspeak": {"default": "neither-greater-than-nor-equivalent-to"}}, "key": "2275"}, {"category": "Sm", "mappings": {"default": {"default": "less than or greater than", "alternative": "less than or greater than"}, "mathspeak": {"default": "less-than-or-greater-than"}}, "key": "2276"}, {"category": "Sm", "mappings": {"default": {"default": "greater than or less than", "alternative": "greater than or less than"}, "mathspeak": {"default": "greater-than-or-less-than"}}, "key": "2277"}, {"category": "Sm", "mappings": {"default": {"default": "neither less than nor greater than", "alternative": "neither less than nor greater than"}, "mathspeak": {"default": "neither-less-than-nor-greater-than"}}, "key": "2278"}, {"category": "Sm", "mappings": {"default": {"default": "neither greater than nor less than", "alternative": "neither greater than nor less than"}, "mathspeak": {"default": "neither-greater-than-nor-less-than"}}, "key": "2279"}, {"category": "Sm", "mappings": {"default": {"default": "precedes"}}, "key": "227A"}, {"category": "Sm", "mappings": {"default": {"default": "succeeds"}}, "key": "227B"}, {"category": "Sm", "mappings": {"default": {"default": "precedes or equals"}, "mathspeak": {"default": "precedes-or-equal-to"}}, "key": "227C"}, {"category": "Sm", "mappings": {"default": {"default": "succeeds or equals"}, "mathspeak": {"default": "succeeds-or-equal-to"}}, "key": "227D"}, {"category": "Sm", "mappings": {"default": {"default": "precedes or equivalent to"}, "mathspeak": {"default": "precedes-or-equivalent-to"}}, "key": "227E"}, {"category": "Sm", "mappings": {"default": {"default": "succeeds or equivalent to"}, "mathspeak": {"default": "succeeds-or-equivalent-to"}}, "key": "227F"}, {"category": "Sm", "mappings": {"default": {"default": "does not precede"}, "mathspeak": {"default": "does-not-precede"}}, "key": "2280"}, {"category": "Sm", "mappings": {"default": {"default": "does not succeed"}, "mathspeak": {"default": "does-not-succeed"}}, "key": "2281"}, {"category": "Sm", "mappings": {"default": {"default": "subset of"}, "mathspeak": {"default": "subset-of"}}, "key": "2282"}, {"category": "Sm", "mappings": {"default": {"default": "superset of"}, "mathspeak": {"default": "superset-of"}}, "key": "2283"}, {"category": "Sm", "mappings": {"default": {"default": "not a subset of"}, "mathspeak": {"default": "not-a-subset-of"}}, "key": "2284"}, {"category": "Sm", "mappings": {"default": {"default": "not a superset of"}, "mathspeak": {"default": "not-a-superset-of"}}, "key": "2285"}, {"category": "Sm", "mappings": {"default": {"default": "subset of or equals"}, "mathspeak": {"default": "subset-of-or-equal-to"}}, "key": "2286"}, {"category": "Sm", "mappings": {"default": {"default": "superset of or equals"}, "mathspeak": {"default": "superset-of-or-equal-to"}}, "key": "2287"}, {"category": "Sm", "mappings": {"default": {"default": "neither a subset of nor equals"}, "mathspeak": {"default": "neither-a-subset-of-nor-equal-to"}}, "key": "2288"}, {"category": "Sm", "mappings": {"default": {"default": "neither a superset of nor equals"}, "mathspeak": {"default": "neither-a-superset-of-nor-equal-to"}}, "key": "2289"}, {"category": "Sm", "mappings": {"default": {"default": "subset of with not equals", "alternative": "subset of or not equals", "short": "subset of or not equals"}, "mathspeak": {"default": "subset-of-or-not-equals"}}, "key": "228A"}, {"category": "Sm", "mappings": {"default": {"default": "superset of with not equals", "alternative": "superset of or not equals", "short": "superset of or not equals"}, "mathspeak": {"default": "superset-of-or-not-equals"}}, "key": "228B"}, {"category": "Sm", "mappings": {"default": {"default": "multiset"}}, "key": "228C"}, {"category": "Sm", "mappings": {"default": {"default": "multiset multiplication"}, "mathspeak": {"default": "multiset-multiplication"}}, "key": "228D"}, {"category": "Sm", "mappings": {"default": {"default": "multiset union"}, "mathspeak": {"default": "multiset-union"}}, "key": "228E"}, {"category": "Sm", "mappings": {"default": {"default": "square image of"}, "mathspeak": {"default": "square-image-of"}}, "key": "228F"}, {"category": "Sm", "mappings": {"default": {"default": "square original of"}, "mathspeak": {"default": "square-original-of"}}, "key": "2290"}, {"category": "Sm", "mappings": {"default": {"default": "square image of or equals"}, "mathspeak": {"default": "square-image-of-or-equal-to"}}, "key": "2291"}, {"category": "Sm", "mappings": {"default": {"default": "square original of or equals"}, "mathspeak": {"default": "square-original-of-or-equal-to"}}, "key": "2292"}, {"category": "Sm", "mappings": {"default": {"default": "square cap"}, "mathspeak": {"default": "square-cap"}}, "key": "2293"}, {"category": "Sm", "mappings": {"default": {"default": "square cup"}, "mathspeak": {"default": "square-cup"}}, "key": "2294"}, {"category": "Sm", "mappings": {"default": {"default": "circled plus"}, "mathspeak": {"default": "circled-plus"}}, "key": "2295"}, {"category": "Sm", "mappings": {"default": {"default": "circled minus"}, "mathspeak": {"default": "circled-minus"}}, "key": "2296"}, {"category": "Sm", "mappings": {"default": {"default": "circled times"}, "mathspeak": {"default": "circled-times"}}, "key": "2297"}, {"category": "Sm", "mappings": {"default": {"default": "circled division slash"}, "mathspeak": {"default": "circled-division-slash"}}, "key": "2298"}, {"category": "Sm", "mappings": {"default": {"default": "circled dot operator"}, "mathspeak": {"default": "circled-dot"}}, "key": "2299"}, {"category": "Sm", "mappings": {"default": {"default": "circled ring operator"}, "mathspeak": {"default": "circled-ring"}}, "key": "229A"}, {"category": "Sm", "mappings": {"default": {"default": "circled asterisk operator"}, "mathspeak": {"default": "circled-asterisk"}}, "key": "229B"}, {"category": "Sm", "mappings": {"default": {"default": "circled equals"}, "mathspeak": {"default": "circled-equals"}}, "key": "229C"}, {"category": "Sm", "mappings": {"default": {"default": "circled dash"}, "mathspeak": {"default": "circled-dash"}}, "key": "229D"}, {"category": "Sm", "mappings": {"default": {"default": "squared plus"}, "mathspeak": {"default": "squared-plus"}}, "key": "229E"}, {"category": "Sm", "mappings": {"default": {"default": "squared minus"}, "mathspeak": {"default": "squared-minus"}}, "key": "229F"}, {"category": "Sm", "mappings": {"default": {"default": "squared times"}, "mathspeak": {"default": "squared-times"}}, "key": "22A0"}, {"category": "Sm", "mappings": {"default": {"default": "squared dot operator"}, "mathspeak": {"default": "squared-dot"}}, "key": "22A1"}, {"category": "Sm", "mappings": {"default": {"default": "right tack"}, "mathspeak": {"default": "right-tack"}}, "key": "22A2"}, {"category": "Sm", "mappings": {"default": {"default": "left tack"}, "mathspeak": {"default": "left-tack"}}, "key": "22A3"}, {"category": "Sm", "mappings": {"default": {"default": "down tack"}, "mathspeak": {"default": "down-tack"}}, "key": "22A4"}, {"category": "Sm", "mappings": {"default": {"default": "up tack"}, "mathspeak": {"default": "up-tack"}}, "key": "22A5"}, {"category": "Sm", "mappings": {"default": {"default": "assertion"}}, "key": "22A6"}, {"category": "Sm", "mappings": {"default": {"default": "models"}}, "key": "22A7"}, {"category": "Sm", "mappings": {"default": {"default": "true"}}, "key": "22A8"}, {"category": "Sm", "mappings": {"default": {"default": "forces"}}, "key": "22A9"}, {"category": "Sm", "mappings": {"default": {"default": "triple vertical bar right turnstile"}, "mathspeak": {"default": "triple-vertical-bar-right-turnstile"}}, "key": "22AA"}, {"category": "Sm", "mappings": {"default": {"default": "double vertical bar double right turnstile"}, "mathspeak": {"default": "double-vertical-bar-double-right-turnstile"}}, "key": "22AB"}, {"category": "Sm", "mappings": {"default": {"default": "does not prove"}, "mathspeak": {"default": "does-not-prove"}}, "key": "22AC"}, {"category": "Sm", "mappings": {"default": {"default": "not true"}, "mathspeak": {"default": "not-true"}}, "key": "22AD"}, {"category": "Sm", "mappings": {"default": {"default": "does not force"}, "mathspeak": {"default": "does-not-force"}}, "key": "22AE"}, {"category": "Sm", "mappings": {"default": {"default": "negated double vertical bar double right turnstile"}, "mathspeak": {"default": "negated-double-vertical-bar-double-right-turnstile"}}, "key": "22AF"}, {"category": "Sm", "mappings": {"default": {"default": "precedes under relation"}, "mathspeak": {"default": "precedes-under-relation"}}, "key": "22B0"}, {"category": "Sm", "mappings": {"default": {"default": "succeeds under relation"}, "mathspeak": {"default": "succeeds-under-relation"}}, "key": "22B1"}, {"category": "Sm", "mappings": {"default": {"default": "normal subgroup of"}, "mathspeak": {"default": "normal-subgroup-of"}}, "key": "22B2"}, {"category": "Sm", "mappings": {"default": {"default": "contains as normal subgroup"}, "mathspeak": {"default": "contains-as-normal-subgroup"}}, "key": "22B3"}, {"category": "Sm", "mappings": {"default": {"default": "normal subgroup of or equals"}, "mathspeak": {"default": "normal-subgroup-of-or-equal-to"}}, "key": "22B4"}, {"category": "Sm", "mappings": {"default": {"default": "contains as normal subgroup or equals"}, "mathspeak": {"default": "contains-as-normal-subgroup-or-equal-to"}}, "key": "22B5"}, {"category": "Sm", "mappings": {"default": {"default": "original of"}, "mathspeak": {"default": "original-of"}}, "key": "22B6"}, {"category": "Sm", "mappings": {"default": {"default": "image of"}, "mathspeak": {"default": "image-of"}}, "key": "22B7"}, {"category": "Sm", "mappings": {"default": {"default": "multimap"}}, "key": "22B8"}, {"category": "Sm", "mappings": {"default": {"default": "hermitian conjugate matrix"}, "mathspeak": {"default": "hermitian-conjugate-matrix"}}, "key": "22B9"}, {"category": "Sm", "mappings": {"default": {"default": "intercalate"}}, "key": "22BA"}, {"category": "Sm", "mappings": {"default": {"default": "xor"}}, "key": "22BB"}, {"category": "Sm", "mappings": {"default": {"default": "nand"}}, "key": "22BC"}, {"category": "Sm", "mappings": {"default": {"default": "nor"}}, "key": "22BD"}, {"category": "Sm", "mappings": {"default": {"default": "right triangle"}, "mathspeak": {"default": "right-triangle"}}, "key": "22BF"}, {"category": "Sm", "mappings": {"default": {"default": "n ary logical and"}, "mathspeak": {"default": "logical-and"}}, "key": "22C0"}, {"category": "Sm", "mappings": {"default": {"default": "n ary logical or"}, "mathspeak": {"default": "logical-or"}}, "key": "22C1"}, {"category": "Sm", "mappings": {"default": {"default": "n ary intersection"}, "mathspeak": {"default": "intersection"}}, "key": "22C2"}, {"category": "Sm", "mappings": {"default": {"default": "n ary union"}, "mathspeak": {"default": "union"}}, "key": "22C3"}, {"category": "Sm", "mappings": {"default": {"default": "diamond operator"}, "mathspeak": {"default": "diamond"}}, "key": "22C4"}, {"category": "Sm", "mappings": {"default": {"default": "dot operator"}, "mathspeak": {"default": "dot"}}, "key": "22C5"}, {"category": "Sm", "mappings": {"default": {"default": "star operator"}, "mathspeak": {"default": "star"}}, "key": "22C6"}, {"category": "Sm", "mappings": {"default": {"default": "division times"}, "mathspeak": {"default": "division-times"}}, "key": "22C7"}, {"category": "Sm", "mappings": {"default": {"default": "bowtie"}}, "key": "22C8"}, {"category": "Sm", "mappings": {"default": {"default": "left normal factor semidirect product"}, "mathspeak": {"default": "left-normal-factor-semidirect-product"}}, "key": "22C9"}, {"category": "Sm", "mappings": {"default": {"default": "right normal factor semidirect product"}, "mathspeak": {"default": "right-normal-factor-semidirect-product"}}, "key": "22CA"}, {"category": "Sm", "mappings": {"default": {"default": "left semidirect product"}, "mathspeak": {"default": "left-semidirect-product"}}, "key": "22CB"}, {"category": "Sm", "mappings": {"default": {"default": "right semidirect product"}, "mathspeak": {"default": "right-semidirect-product"}}, "key": "22CC"}, {"category": "Sm", "mappings": {"default": {"default": "reversed tilde equals"}, "mathspeak": {"default": "reversed-tilde-equals"}}, "key": "22CD"}, {"category": "Sm", "mappings": {"default": {"default": "curly logical or"}, "mathspeak": {"default": "curly-logical-or"}}, "key": "22CE"}, {"category": "Sm", "mappings": {"default": {"default": "curly logical and"}, "mathspeak": {"default": "curly-logical-and"}}, "key": "22CF"}, {"category": "Sm", "mappings": {"default": {"default": "double subset"}, "mathspeak": {"default": "double-subset"}}, "key": "22D0"}, {"category": "Sm", "mappings": {"default": {"default": "double superset"}, "mathspeak": {"default": "double-superset"}}, "key": "22D1"}, {"category": "Sm", "mappings": {"default": {"default": "double intersection"}, "mathspeak": {"default": "double-intersection"}}, "key": "22D2"}, {"category": "Sm", "mappings": {"default": {"default": "double union"}, "mathspeak": {"default": "double-union"}}, "key": "22D3"}, {"category": "Sm", "mappings": {"default": {"default": "pitchfork"}}, "key": "22D4"}, {"category": "Sm", "mappings": {"default": {"default": "equal and parallel to"}, "mathspeak": {"default": "equal-and-parallel-to"}}, "key": "22D5"}, {"category": "Sm", "mappings": {"default": {"default": "less than with dot", "alternative": "less than with dot", "short": "less than dot"}, "mathspeak": {"default": "less-than-dot"}}, "key": "22D6"}, {"category": "Sm", "mappings": {"default": {"default": "greater than with dot", "alternative": "greater than with dot", "short": "greater than dot"}, "mathspeak": {"default": "greater-than-dot"}}, "key": "22D7"}, {"category": "Sm", "mappings": {"default": {"default": "very much less than", "alternative": "very much less than"}, "mathspeak": {"default": "very-much-less-than"}}, "key": "22D8"}, {"category": "Sm", "mappings": {"default": {"default": "very much greater than", "alternative": "very much greater than"}, "mathspeak": {"default": "very-much-greater-than"}}, "key": "22D9"}, {"category": "Sm", "mappings": {"default": {"default": "less than equals or greater than", "alternative": "less than equals or greater than"}, "mathspeak": {"default": "less-than-equals-or-greater-than"}}, "key": "22DA"}, {"category": "Sm", "mappings": {"default": {"default": "greater than equals or less than", "alternative": "greater than equals or less than"}, "mathspeak": {"default": "greater-than-equals-or-less-than"}}, "key": "22DB"}, {"category": "Sm", "mappings": {"default": {"default": "equals or less than", "alternative": "equals or less than"}, "mathspeak": {"default": "equals-or-less-than"}}, "key": "22DC"}, {"category": "Sm", "mappings": {"default": {"default": "equals or greater than", "alternative": "equals or greater than"}, "mathspeak": {"default": "equals-or-greater-than"}}, "key": "22DD"}, {"category": "Sm", "mappings": {"default": {"default": "equals or precedes"}, "mathspeak": {"default": "equals-or-precedes"}}, "key": "22DE"}, {"category": "Sm", "mappings": {"default": {"default": "equals or succeeds"}, "mathspeak": {"default": "equals-or-succeeds"}}, "key": "22DF"}, {"category": "Sm", "mappings": {"default": {"default": "does not precede or equal"}, "mathspeak": {"default": "does-not-precede-or-equal"}}, "key": "22E0"}, {"category": "Sm", "mappings": {"default": {"default": "does not succeed or equal"}, "mathspeak": {"default": "does-not-succeed-or-equal"}}, "key": "22E1"}, {"category": "Sm", "mappings": {"default": {"default": "not square image of or equals"}, "mathspeak": {"default": "not-square-image-of-or-equal-to"}}, "key": "22E2"}, {"category": "Sm", "mappings": {"default": {"default": "not square original of or equals"}, "mathspeak": {"default": "not-square-original-of-or-equal-to"}}, "key": "22E3"}, {"category": "Sm", "mappings": {"default": {"default": "square image of or not equals"}, "mathspeak": {"default": "square-image-of-or-not-equals"}}, "key": "22E4"}, {"category": "Sm", "mappings": {"default": {"default": "square original of or not equals"}, "mathspeak": {"default": "square-original-of-or-not-equals"}}, "key": "22E5"}, {"category": "Sm", "mappings": {"default": {"default": "less than but not equivalent to", "alternative": "less than but not equivalent to"}, "mathspeak": {"default": "less-than-but-not-equivalent-to"}}, "key": "22E6"}, {"category": "Sm", "mappings": {"default": {"default": "greater than but not equivalent to", "alternative": "greater than but not equivalent to"}, "mathspeak": {"default": "greater-than-but-not-equivalent-to"}}, "key": "22E7"}, {"category": "Sm", "mappings": {"default": {"default": "precedes but not equivalent to"}, "mathspeak": {"default": "precedes-but-not-equivalent-to"}}, "key": "22E8"}, {"category": "Sm", "mappings": {"default": {"default": "succeeds but not equivalent to"}, "mathspeak": {"default": "succeeds-but-not-equivalent-to"}}, "key": "22E9"}, {"category": "Sm", "mappings": {"default": {"default": "not normal subgroup of"}, "mathspeak": {"default": "not-normal-subgroup-of"}}, "key": "22EA"}, {"category": "Sm", "mappings": {"default": {"default": "does not contain as normal subgroup"}, "mathspeak": {"default": "does-not-contain-as-normal-subgroup"}}, "key": "22EB"}, {"category": "Sm", "mappings": {"default": {"default": "not normal subgroup of or equals"}, "mathspeak": {"default": "not-normal-subgroup-of-or-equal-to"}}, "key": "22EC"}, {"category": "Sm", "mappings": {"default": {"default": "does not contain as normal subgroup or equal"}, "mathspeak": {"default": "does-not-contain-as-normal-subgroup-or-equal"}}, "key": "22ED"}, {"category": "Sm", "mappings": {"default": {"default": "vertical ellipsis"}, "mathspeak": {"default": "vertical-ellipsis"}}, "key": "22EE"}, {"category": "Sm", "mappings": {"default": {"default": "midline horizontal ellipsis"}, "mathspeak": {"default": "midline-horizontal-ellipsis"}}, "key": "22EF"}, {"category": "Sm", "mappings": {"default": {"default": "up right diagonal ellipsis"}, "mathspeak": {"default": "up-right-diagonal-ellipsis"}}, "key": "22F0"}, {"category": "Sm", "mappings": {"default": {"default": "down right diagonal ellipsis"}, "mathspeak": {"default": "down-right-diagonal-ellipsis"}}, "key": "22F1"}, {"category": "Sm", "mappings": {"default": {"default": "element of with long horizontal stroke"}, "mathspeak": {"default": "element-of-with-long-horizontal-stroke"}}, "key": "22F2"}, {"category": "Sm", "mappings": {"default": {"default": "element of with vertical bar at end of horizontal stroke"}, "mathspeak": {"default": "element-of-with-vertical-bar-at-end-of-horizontal-stroke"}}, "key": "22F3"}, {"category": "Sm", "mappings": {"default": {"default": "small element of with vertical bar at end of horizontal stroke"}, "mathspeak": {"default": "small-element-of-with-vertical-bar-at-end-of-horizontal-stroke"}}, "key": "22F4"}, {"category": "Sm", "mappings": {"default": {"default": "element of with dot above"}, "mathspeak": {"default": "element-of-with-dot-above"}}, "key": "22F5"}, {"category": "Sm", "mappings": {"default": {"default": "element of with overbar"}, "mathspeak": {"default": "element-of-with-overbar"}}, "key": "22F6"}, {"category": "Sm", "mappings": {"default": {"default": "small element of with overbar"}, "mathspeak": {"default": "small-element-of-with-overbar"}}, "key": "22F7"}, {"category": "Sm", "mappings": {"default": {"default": "element of with underbar"}, "mathspeak": {"default": "element-of-with-underbar"}}, "key": "22F8"}, {"category": "Sm", "mappings": {"default": {"default": "element of with two horizontal strokes"}, "mathspeak": {"default": "element-of-with-two-horizontal-strokes"}}, "key": "22F9"}, {"category": "Sm", "mappings": {"default": {"default": "contains with long horizontal stroke"}, "mathspeak": {"default": "contains-with-long-horizontal-stroke"}}, "key": "22FA"}, {"category": "Sm", "mappings": {"default": {"default": "contains with vertical bar at end of horizontal stroke"}, "mathspeak": {"default": "contains-with-vertical-bar-at-end-of-horizontal-stroke"}}, "key": "22FB"}, {"category": "Sm", "mappings": {"default": {"default": "small contains with vertical bar at end of horizontal stroke"}, "mathspeak": {"default": "small-contains-with-vertical-bar-at-end-of-horizontal-stroke"}}, "key": "22FC"}, {"category": "Sm", "mappings": {"default": {"default": "contains with overbar"}, "mathspeak": {"default": "contains-with-overbar"}}, "key": "22FD"}, {"category": "Sm", "mappings": {"default": {"default": "small contains with overbar"}, "mathspeak": {"default": "small-contains-with-overbar"}}, "key": "22FE"}, {"category": "Sm", "mappings": {"default": {"default": "z notation bag membership"}, "mathspeak": {"default": "z-notation-bag-membership"}}, "key": "22FF"}, {"category": "So", "mappings": {"default": {"default": "diameter sign", "short": "diameter"}, "mathspeak": {"default": "diameter-sign"}}, "key": "2300"}, {"category": "So", "mappings": {"default": {"default": "house"}}, "key": "2302"}, {"category": "So", "mappings": {"default": {"default": "projective"}}, "key": "2305"}, {"category": "So", "mappings": {"default": {"default": "perspective"}}, "key": "2306"}, {"category": "So", "mappings": {"default": {"default": "wavy line"}, "mathspeak": {"default": "wavy-line"}}, "key": "2307"}, {"category": "So", "mappings": {"default": {"default": "reversed not sign", "short": "reversed not"}, "mathspeak": {"default": "reversed-not"}}, "key": "2310"}, {"category": "So", "mappings": {"default": {"default": "square lozenge"}, "mathspeak": {"default": "square-lozenge"}}, "key": "2311"}, {"category": "So", "mappings": {"default": {"default": "arc"}}, "key": "2312"}, {"category": "So", "mappings": {"default": {"default": "segment"}}, "key": "2313"}, {"category": "So", "mappings": {"default": {"default": "sector"}}, "key": "2314"}, {"category": "So", "mappings": {"default": {"default": "heavy plus sign", "alternative": "heavy plus", "short": "bold plus"}, "mathspeak": {"default": "bold-plus"}}, "key": "2795"}, {"category": "So", "mappings": {"default": {"default": "heavy minus sign", "alternative": "heavy minus", "short": "bold minus"}, "mathspeak": {"default": "bold-minus"}}, "key": "2796"}, {"category": "So", "mappings": {"default": {"default": "heavy division sign", "alternative": "heavy division", "short": "bold division"}, "mathspeak": {"default": "bold-division"}}, "key": "2797"}, {"category": "So", "mappings": {"default": {"default": "curly loop"}, "mathspeak": {"default": "curly-loop"}}, "key": "27B0"}, {"category": "So", "mappings": {"default": {"default": "double curly loop"}, "mathspeak": {"default": "double-curly-loop"}}, "key": "27BF"}, {"category": "Sm", "mappings": {"default": {"default": "white triangle containing small white triangle"}, "mathspeak": {"default": "white-triangle-containing-small-white-triangle"}}, "key": "27C1"}, {"category": "Sm", "mappings": {"default": {"default": "perpendicular"}}, "key": "27C2"}, {"category": "Sm", "mappings": {"default": {"default": "open subset"}, "mathspeak": {"default": "open-subset"}}, "key": "27C3"}, {"category": "Sm", "mappings": {"default": {"default": "open superset"}, "mathspeak": {"default": "open-superset"}}, "key": "27C4"}, {"category": "Sm", "mappings": {"default": {"default": "or with dot inside"}, "mathspeak": {"default": "or-with-dot-inside"}}, "key": "27C7"}, {"category": "Sm", "mappings": {"default": {"default": "reverse solidus preceding subset"}, "mathspeak": {"default": "reverse-solidus-preceding-subset"}}, "key": "27C8"}, {"category": "Sm", "mappings": {"default": {"default": "superset preceding solidus"}, "mathspeak": {"default": "superset-preceding-solidus"}}, "key": "27C9"}, {"category": "Sm", "mappings": {"default": {"default": "vertical bar with horizontal stroke"}, "mathspeak": {"default": "vertical-bar-with-horizontal-stroke"}}, "key": "27CA"}, {"category": "Sm", "mappings": {"default": {"default": "mathematical rising diagonal"}, "mathspeak": {"default": "mathematical-rising-diagonal"}}, "key": "27CB"}, {"category": "Sm", "mappings": {"default": {"default": "long division"}, "mathspeak": {"default": "long-division"}}, "key": "27CC"}, {"category": "Sm", "mappings": {"default": {"default": "mathematical falling diagonal"}, "mathspeak": {"default": "mathematical-falling-diagonal"}}, "key": "27CD"}, {"category": "Sm", "mappings": {"default": {"default": "squared logical and"}, "mathspeak": {"default": "squared-logical-and"}}, "key": "27CE"}, {"category": "Sm", "mappings": {"default": {"default": "squared logical or"}, "mathspeak": {"default": "squared-logical-or"}}, "key": "27CF"}, {"category": "Sm", "mappings": {"default": {"default": "white diamond with centered dot"}, "mathspeak": {"default": "white-diamond-with-centered-dot"}}, "key": "27D0"}, {"category": "Sm", "mappings": {"default": {"default": "and with dot"}, "mathspeak": {"default": "and-with-dot"}}, "key": "27D1"}, {"category": "Sm", "mappings": {"default": {"default": "element of opening upwards"}, "mathspeak": {"default": "element-of-opening-upwards"}}, "key": "27D2"}, {"category": "Sm", "mappings": {"default": {"default": "lower right corner with dot"}, "mathspeak": {"default": "lower-right-corner-with-dot"}}, "key": "27D3"}, {"category": "Sm", "mappings": {"default": {"default": "upper left corner with dot"}, "mathspeak": {"default": "upper-left-corner-with-dot"}}, "key": "27D4"}, {"category": "Sm", "mappings": {"default": {"default": "left outer join"}, "mathspeak": {"default": "left-outer-join"}}, "key": "27D5"}, {"category": "Sm", "mappings": {"default": {"default": "right outer join"}, "mathspeak": {"default": "right-outer-join"}}, "key": "27D6"}, {"category": "Sm", "mappings": {"default": {"default": "full outer join"}, "mathspeak": {"default": "full-outer-join"}}, "key": "27D7"}, {"category": "Sm", "mappings": {"default": {"default": "large up tack"}, "mathspeak": {"default": "large-up-tack"}}, "key": "27D8"}, {"category": "Sm", "mappings": {"default": {"default": "large down tack"}, "mathspeak": {"default": "large-down-tack"}}, "key": "27D9"}, {"category": "Sm", "mappings": {"default": {"default": "left and right double turnstile"}, "mathspeak": {"default": "left-and-right-double-turnstile"}}, "key": "27DA"}, {"category": "Sm", "mappings": {"default": {"default": "left and right tack"}, "mathspeak": {"default": "left-and-right-tack"}}, "key": "27DB"}, {"category": "Sm", "mappings": {"default": {"default": "left multimap"}, "mathspeak": {"default": "left-multimap"}}, "key": "27DC"}, {"category": "Sm", "mappings": {"default": {"default": "long right tack"}, "mathspeak": {"default": "long-right-tack"}}, "key": "27DD"}, {"category": "Sm", "mappings": {"default": {"default": "long left tack"}, "mathspeak": {"default": "long-left-tack"}}, "key": "27DE"}, {"category": "Sm", "mappings": {"default": {"default": "up tack with circle above"}, "mathspeak": {"default": "up-tack-with-circle-above"}}, "key": "27DF"}, {"category": "Sm", "mappings": {"default": {"default": "lozenge divided by horizontal rule"}, "mathspeak": {"default": "lozenge-divided-by-horizontal-rule"}}, "key": "27E0"}, {"category": "Sm", "mappings": {"default": {"default": "white concave sided diamond"}, "mathspeak": {"default": "white-concave-sided-diamond"}}, "key": "27E1"}, {"category": "Sm", "mappings": {"default": {"default": "white concave sided diamond with leftwards tick"}, "mathspeak": {"default": "white-concave-sided-diamond-with-leftwards-tick"}}, "key": "27E2"}, {"category": "Sm", "mappings": {"default": {"default": "white concave sided diamond with rightwards tick"}, "mathspeak": {"default": "white-concave-sided-diamond-with-rightwards-tick"}}, "key": "27E3"}, {"category": "Sm", "mappings": {"default": {"default": "white square with leftwards tick"}, "mathspeak": {"default": "white-square-with-leftwards-tick"}}, "key": "27E4"}, {"category": "Sm", "mappings": {"default": {"default": "white square with rightwards tick"}, "mathspeak": {"default": "white-square-with-rightwards-tick"}}, "key": "27E5"}, {"category": "Sm", "mappings": {"default": {"default": "rising diagonal crossing falling diagonal"}, "mathspeak": {"default": "rising-diagonal-crossing-falling-diagonal"}}, "key": "292B"}, {"category": "Sm", "mappings": {"default": {"default": "falling diagonal crossing rising diagonal"}, "mathspeak": {"default": "falling-diagonal-crossing-rising-diagonal"}}, "key": "292C"}, {"category": "Sm", "mappings": {"default": {"default": "triple vertical bar delimiter"}, "mathspeak": {"default": "triple-vertical-bar-delimiter"}}, "key": "2980"}, {"category": "Sm", "mappings": {"default": {"default": "z notation spot"}, "mathspeak": {"default": "z-notation-spot"}}, "key": "2981"}, {"category": "Sm", "mappings": {"default": {"default": "z notation type colon"}, "mathspeak": {"default": "z-notation-type-colon"}}, "key": "2982"}, {"category": "Sm", "mappings": {"default": {"default": "dotted fence"}, "mathspeak": {"default": "dotted-fence"}}, "key": "2999"}, {"category": "Sm", "mappings": {"default": {"default": "vertical zigzag line"}, "mathspeak": {"default": "vertical-zigzag-line"}}, "key": "299A"}, {"category": "Sm", "mappings": {"default": {"default": "reversed empty set"}, "mathspeak": {"default": "reversed-empty-set"}}, "key": "29B0"}, {"category": "Sm", "mappings": {"default": {"default": "empty set with overbar"}, "mathspeak": {"default": "empty-set-with-overbar"}}, "key": "29B1"}, {"category": "Sm", "mappings": {"default": {"default": "empty set with small circle above"}, "mathspeak": {"default": "empty-set-with-small-circle-above"}}, "key": "29B2"}, {"category": "Sm", "mappings": {"default": {"default": "circle with horizontal bar"}, "mathspeak": {"default": "circle-with-horizontal-bar"}}, "key": "29B5"}, {"category": "Sm", "mappings": {"default": {"default": "circled vertical bar"}, "mathspeak": {"default": "circled-vertical-bar"}}, "key": "29B6"}, {"category": "Sm", "mappings": {"default": {"default": "circled parallel"}, "mathspeak": {"default": "circled-parallel"}}, "key": "29B7"}, {"category": "Sm", "mappings": {"default": {"default": "circled reverse solidus"}, "mathspeak": {"default": "circled-reverse-solidus"}}, "key": "29B8"}, {"category": "Sm", "mappings": {"default": {"default": "circled perpendicular"}, "mathspeak": {"default": "circled-perpendicular"}}, "key": "29B9"}, {"category": "Sm", "mappings": {"default": {"default": "circle divided by horizontal bar and top half divided by vertical bar"}, "mathspeak": {"default": "circle-divided-by-horizontal-bar-and-top-half-divided-by-vertical-bar"}}, "key": "29BA"}, {"category": "Sm", "mappings": {"default": {"default": "circle with superimposed x"}, "mathspeak": {"default": "circle-with-superimposed-x"}}, "key": "29BB"}, {"category": "Sm", "mappings": {"default": {"default": "circled anticlockwise rotated division sign", "short": "circled anticlockwise rotated division"}, "mathspeak": {"default": "circled-anticlockwise-rotated-division"}}, "key": "29BC"}, {"category": "Sm", "mappings": {"default": {"default": "circled white bullet"}, "mathspeak": {"default": "circled-white-bullet"}}, "key": "29BE"}, {"category": "Sm", "mappings": {"default": {"default": "circled bullet"}, "mathspeak": {"default": "circled-bullet"}}, "key": "29BF"}, {"category": "Sm", "mappings": {"default": {"default": "circled less than"}, "mathspeak": {"default": "circled-less-than"}}, "key": "29C0"}, {"category": "Sm", "mappings": {"default": {"default": "circled greater than"}, "mathspeak": {"default": "circled-greater-than"}}, "key": "29C1"}, {"category": "Sm", "mappings": {"default": {"default": "circle with small circle to the right"}, "mathspeak": {"default": "circle-with-small-circle-to-the-right"}}, "key": "29C2"}, {"category": "Sm", "mappings": {"default": {"default": "circle with two horizontal strokes to the right"}, "mathspeak": {"default": "circle-with-two-horizontal-strokes-to-the-right"}}, "key": "29C3"}, {"category": "Sm", "mappings": {"default": {"default": "squared rising diagonal slash"}, "mathspeak": {"default": "squared-rising-diagonal-slash"}}, "key": "29C4"}, {"category": "Sm", "mappings": {"default": {"default": "squared falling diagonal slash"}, "mathspeak": {"default": "squared-falling-diagonal-slash"}}, "key": "29C5"}, {"category": "Sm", "mappings": {"default": {"default": "squared asterisk"}, "mathspeak": {"default": "squared-asterisk"}}, "key": "29C6"}, {"category": "Sm", "mappings": {"default": {"default": "squared small circle"}, "mathspeak": {"default": "squared-small-circle"}}, "key": "29C7"}, {"category": "Sm", "mappings": {"default": {"default": "squared square"}, "mathspeak": {"default": "squared-square"}}, "key": "29C8"}, {"category": "Sm", "mappings": {"default": {"default": "two joined squares"}, "mathspeak": {"default": "two-joined-squares"}}, "key": "29C9"}, {"category": "Sm", "mappings": {"default": {"default": "triangle with dot above"}, "mathspeak": {"default": "triangle-with-dot-above"}}, "key": "29CA"}, {"category": "Sm", "mappings": {"default": {"default": "triangle with underbar"}, "mathspeak": {"default": "triangle-with-underbar"}}, "key": "29CB"}, {"category": "Sm", "mappings": {"default": {"default": "s in triangle"}, "mathspeak": {"default": "s-in-triangle"}}, "key": "29CC"}, {"category": "Sm", "mappings": {"default": {"default": "triangle with serifs at bottom"}, "mathspeak": {"default": "triangle-with-serifs-at-bottom"}}, "key": "29CD"}, {"category": "Sm", "mappings": {"default": {"default": "right triangle above left triangle"}, "mathspeak": {"default": "right-triangle-above-left-triangle"}}, "key": "29CE"}, {"category": "Sm", "mappings": {"default": {"default": "left triangle beside vertical bar"}, "mathspeak": {"default": "left-triangle-beside-vertical-bar"}}, "key": "29CF"}, {"category": "Sm", "mappings": {"default": {"default": "vertical bar beside right triangle"}, "mathspeak": {"default": "vertical-bar-beside-right-triangle"}}, "key": "29D0"}, {"category": "Sm", "mappings": {"default": {"default": "bowtie with left half black"}, "mathspeak": {"default": "bowtie-with-left-half-black"}}, "key": "29D1"}, {"category": "Sm", "mappings": {"default": {"default": "bowtie with right half black"}, "mathspeak": {"default": "bowtie-with-right-half-black"}}, "key": "29D2"}, {"category": "Sm", "mappings": {"default": {"default": "black bowtie"}, "mathspeak": {"default": "black-bowtie"}}, "key": "29D3"}, {"category": "Sm", "mappings": {"default": {"default": "times with left half black"}, "mathspeak": {"default": "times-with-left-half-black"}}, "key": "29D4"}, {"category": "Sm", "mappings": {"default": {"default": "times with right half black"}, "mathspeak": {"default": "times-with-right-half-black"}}, "key": "29D5"}, {"category": "Sm", "mappings": {"default": {"default": "white hourglass"}, "mathspeak": {"default": "white-hourglass"}}, "key": "29D6"}, {"category": "Sm", "mappings": {"default": {"default": "black hourglass"}, "mathspeak": {"default": "black-hourglass"}}, "key": "29D7"}, {"category": "Sm", "mappings": {"default": {"default": "incomplete infinity"}, "mathspeak": {"default": "incomplete-infinity"}}, "key": "29DC"}, {"category": "Sm", "mappings": {"default": {"default": "tie over infinity"}, "mathspeak": {"default": "tie-over-infinity"}}, "key": "29DD"}, {"category": "Sm", "mappings": {"default": {"default": "infinity negated with vertical bar"}, "mathspeak": {"default": "infinity-negated-with-vertical-bar"}}, "key": "29DE"}, {"category": "Sm", "mappings": {"default": {"default": "double ended multimap"}, "mathspeak": {"default": "double-ended-multimap"}}, "key": "29DF"}, {"category": "Sm", "mappings": {"default": {"default": "square with contoured outline"}, "mathspeak": {"default": "square-with-contoured-outline"}}, "key": "29E0"}, {"category": "Sm", "mappings": {"default": {"default": "increases as"}, "mathspeak": {"default": "increases-as"}}, "key": "29E1"}, {"category": "Sm", "mappings": {"default": {"default": "shuffle product"}, "mathspeak": {"default": "shuffle-product"}}, "key": "29E2"}, {"category": "Sm", "mappings": {"default": {"default": "equals sign and slanted parallel"}, "mathspeak": {"default": "equals-and-slanted-parallel"}}, "key": "29E3"}, {"category": "Sm", "mappings": {"default": {"default": "equals sign and slanted parallel with tilde above"}, "mathspeak": {"default": "equals-and-slanted-parallel-with-tilde-above"}}, "key": "29E4"}, {"category": "Sm", "mappings": {"default": {"default": "identical to and slanted parallel"}, "mathspeak": {"default": "identical-to-and-slanted-parallel"}}, "key": "29E5"}, {"category": "Sm", "mappings": {"default": {"default": "gleich stark"}, "mathspeak": {"default": "gleich-stark"}}, "key": "29E6"}, {"category": "Sm", "mappings": {"default": {"default": "thermodynamic"}}, "key": "29E7"}, {"category": "Sm", "mappings": {"default": {"default": "down pointing triangle with left half black"}, "mathspeak": {"default": "down-pointing-triangle-with-left-half-black"}}, "key": "29E8"}, {"category": "Sm", "mappings": {"default": {"default": "down pointing triangle with right half black"}, "mathspeak": {"default": "down-pointing-triangle-with-right-half-black"}}, "key": "29E9"}, {"category": "Sm", "mappings": {"default": {"default": "black lozenge"}, "mathspeak": {"default": "black-lozenge"}}, "key": "29EB"}, {"category": "Sm", "mappings": {"default": {"default": "error barred white square"}, "mathspeak": {"default": "error-barred-white-square"}}, "key": "29EE"}, {"category": "Sm", "mappings": {"default": {"default": "error barred black square"}, "mathspeak": {"default": "error-barred-black-square"}}, "key": "29EF"}, {"category": "Sm", "mappings": {"default": {"default": "error barred white diamond"}, "mathspeak": {"default": "error-barred-white-diamond"}}, "key": "29F0"}, {"category": "Sm", "mappings": {"default": {"default": "error barred black diamond"}, "mathspeak": {"default": "error-barred-black-diamond"}}, "key": "29F1"}, {"category": "Sm", "mappings": {"default": {"default": "error barred white circle"}, "mathspeak": {"default": "error-barred-white-circle"}}, "key": "29F2"}, {"category": "Sm", "mappings": {"default": {"default": "error barred black circle"}, "mathspeak": {"default": "error-barred-black-circle"}}, "key": "29F3"}, {"category": "Sm", "mappings": {"default": {"default": "rule delayed"}, "mathspeak": {"default": "rule-delayed"}}, "key": "29F4"}, {"category": "Sm", "mappings": {"default": {"default": "reverse solidus operator"}, "mathspeak": {"default": "reverse-solidus"}}, "key": "29F5"}, {"category": "Sm", "mappings": {"default": {"default": "solidus with overbar"}, "mathspeak": {"default": "solidus-with-overbar"}}, "key": "29F6"}, {"category": "Sm", "mappings": {"default": {"default": "reverse solidus with horizontal stroke"}, "mathspeak": {"default": "reverse-solidus-with-horizontal-stroke"}}, "key": "29F7"}, {"category": "Sm", "mappings": {"default": {"default": "big solidus"}, "mathspeak": {"default": "solidus"}}, "key": "29F8"}, {"category": "Sm", "mappings": {"default": {"default": "big reverse solidus"}, "mathspeak": {"default": "reverse-solidus"}}, "key": "29F9"}, {"category": "Sm", "mappings": {"default": {"default": "double plus"}, "mathspeak": {"default": "double-plus"}}, "key": "29FA"}, {"category": "Sm", "mappings": {"default": {"default": "triple plus"}, "mathspeak": {"default": "triple-plus"}}, "key": "29FB"}, {"category": "Sm", "mappings": {"default": {"default": "tiny"}}, "key": "29FE"}, {"category": "Sm", "mappings": {"default": {"default": "miny"}}, "key": "29FF"}, {"category": "Sm", "mappings": {"default": {"default": "n ary circled dot operator"}, "mathspeak": {"default": "circled-dot"}}, "key": "2A00"}, {"category": "Sm", "mappings": {"default": {"default": "n ary circled plus operator"}, "mathspeak": {"default": "circled-plus"}}, "key": "2A01"}, {"category": "Sm", "mappings": {"default": {"default": "n ary circled times operator"}, "mathspeak": {"default": "circled-times"}}, "key": "2A02"}, {"category": "Sm", "mappings": {"default": {"default": "n ary union operator with dot"}, "mathspeak": {"default": "union-with-dot"}}, "key": "2A03"}, {"category": "Sm", "mappings": {"default": {"default": "n ary union operator with plus"}, "mathspeak": {"default": "union-with-plus"}}, "key": "2A04"}, {"category": "Sm", "mappings": {"default": {"default": "n ary square intersection operator"}, "mathspeak": {"default": "square-intersection"}}, "key": "2A05"}, {"category": "Sm", "mappings": {"default": {"default": "n ary square union operator"}, "mathspeak": {"default": "square-union"}}, "key": "2A06"}, {"category": "Sm", "mappings": {"default": {"default": "two logical and operator"}, "mathspeak": {"default": "two-logical-and"}}, "key": "2A07"}, {"category": "Sm", "mappings": {"default": {"default": "two logical or operator"}, "mathspeak": {"default": "two-logical-or"}}, "key": "2A08"}, {"category": "Sm", "mappings": {"default": {"default": "n ary times operator"}, "mathspeak": {"default": "times"}}, "key": "2A09"}, {"category": "Sm", "mappings": {"default": {"default": "modulo two sum"}, "mathspeak": {"default": "modulo-two-sum"}}, "key": "2A0A"}, {"category": "Sm", "mappings": {"default": {"default": "summation with integral"}, "mathspeak": {"default": "summation-with-integral"}}, "key": "2A0B"}, {"category": "Sm", "mappings": {"default": {"default": "quadruple integral operator"}, "mathspeak": {"default": "quadruple-integral"}}, "key": "2A0C"}, {"category": "Sm", "mappings": {"default": {"default": "finite part integral"}, "mathspeak": {"default": "finite-part-integral"}}, "key": "2A0D"}, {"category": "Sm", "mappings": {"default": {"default": "integral with double stroke"}, "mathspeak": {"default": "integral-with-double-stroke"}}, "key": "2A0E"}, {"category": "Sm", "mappings": {"default": {"default": "integral average with slash"}, "mathspeak": {"default": "integral-average-with-slash"}}, "key": "2A0F"}, {"category": "Sm", "mappings": {"default": {"default": "circulation function"}, "mathspeak": {"default": "circulation-function"}}, "key": "2A10"}, {"category": "Sm", "mappings": {"default": {"default": "anticlockwise integration"}, "mathspeak": {"default": "anticlockwise-integration"}}, "key": "2A11"}, {"category": "Sm", "mappings": {"default": {"default": "line integration with rectangular path around pole"}, "mathspeak": {"default": "line-integration-with-rectangular-path-around-pole"}}, "key": "2A12"}, {"category": "Sm", "mappings": {"default": {"default": "line integration with semicircular path around pole"}, "mathspeak": {"default": "line-integration-with-semicircular-path-around-pole"}}, "key": "2A13"}, {"category": "Sm", "mappings": {"default": {"default": "line integration not including the pole"}, "mathspeak": {"default": "line-integration-not-including-the-pole"}}, "key": "2A14"}, {"category": "Sm", "mappings": {"default": {"default": "integral around a point operator"}, "mathspeak": {"default": "integral-around-a-point"}}, "key": "2A15"}, {"category": "Sm", "mappings": {"default": {"default": "quaternion integral operator"}, "mathspeak": {"default": "quaternion-integral"}}, "key": "2A16"}, {"category": "Sm", "mappings": {"default": {"default": "integral with times sign", "short": "integral with times"}, "mathspeak": {"default": "integral-with-times"}}, "key": "2A18"}, {"category": "Sm", "mappings": {"default": {"default": "integral with intersection"}, "mathspeak": {"default": "integral-with-intersection"}}, "key": "2A19"}, {"category": "Sm", "mappings": {"default": {"default": "integral with union"}, "mathspeak": {"default": "integral-with-union"}}, "key": "2A1A"}, {"category": "Sm", "mappings": {"default": {"default": "integral with overbar"}, "mathspeak": {"default": "integral-with-overbar"}}, "key": "2A1B"}, {"category": "Sm", "mappings": {"default": {"default": "integral with underbar"}, "mathspeak": {"default": "integral-with-underbar"}}, "key": "2A1C"}, {"category": "Sm", "mappings": {"default": {"default": "join"}}, "key": "2A1D"}, {"category": "Sm", "mappings": {"default": {"default": "large left triangle operator"}, "mathspeak": {"default": "large-left-triangle"}}, "key": "2A1E"}, {"category": "Sm", "mappings": {"default": {"default": "z notation schema composition"}, "mathspeak": {"default": "z-notation-schema-composition"}}, "key": "2A1F"}, {"category": "Sm", "mappings": {"default": {"default": "z notation schema piping"}, "mathspeak": {"default": "z-notation-schema-piping"}}, "key": "2A20"}, {"category": "Sm", "mappings": {"default": {"default": "z notation schema projection"}, "mathspeak": {"default": "z-notation-schema-projection"}}, "key": "2A21"}, {"category": "Sm", "mappings": {"default": {"default": "plus sign with small circle above", "short": "plus with circle above"}, "mathspeak": {"default": "plus-with-circle-above"}}, "key": "2A22"}, {"category": "Sm", "mappings": {"default": {"default": "plus sign with circumflex accent above", "short": "plus hat"}, "mathspeak": {"default": "plus-hat"}}, "key": "2A23"}, {"category": "Sm", "mappings": {"default": {"default": "plus sign with tilde above", "short": "plus tilde"}, "mathspeak": {"default": "plus-tilde"}}, "key": "2A24"}, {"category": "Sm", "mappings": {"default": {"default": "plus sign with dot below", "short": "plus underdot"}, "mathspeak": {"default": "plus-underdot"}}, "key": "2A25"}, {"category": "Sm", "mappings": {"default": {"default": "plus sign with tilde below"}, "mathspeak": {"default": "plus-sign-with-tilde-below"}}, "key": "2A26"}, {"category": "Sm", "mappings": {"default": {"default": "plus sign with subscript two"}, "mathspeak": {"default": "plus-sign-with-subscript-two"}}, "key": "2A27"}, {"category": "Sm", "mappings": {"default": {"default": "plus sign with black triangle"}, "mathspeak": {"default": "plus-sign-with-black-triangle"}}, "key": "2A28"}, {"category": "Sm", "mappings": {"default": {"default": "minus sign with comma above"}, "mathspeak": {"default": "minus-sign-with-comma-above"}}, "key": "2A29"}, {"category": "Sm", "mappings": {"default": {"default": "minus sign with dot below"}, "mathspeak": {"default": "minus-sign-with-dot-below"}}, "key": "2A2A"}, {"category": "Sm", "mappings": {"default": {"default": "minus sign with falling dots"}, "mathspeak": {"default": "minus-sign-with-falling-dots"}}, "key": "2A2B"}, {"category": "Sm", "mappings": {"default": {"default": "minus sign with rising dots"}, "mathspeak": {"default": "minus-sign-with-rising-dots"}}, "key": "2A2C"}, {"category": "Sm", "mappings": {"default": {"default": "plus sign in left half circle"}, "mathspeak": {"default": "plus-sign-in-left-half-circle"}}, "key": "2A2D"}, {"category": "Sm", "mappings": {"default": {"default": "plus sign in right half circle"}, "mathspeak": {"default": "plus-sign-in-right-half-circle"}}, "key": "2A2E"}, {"category": "Sm", "mappings": {"default": {"default": "vector or cross product"}, "mathspeak": {"default": "vector-or-cross-product"}}, "key": "2A2F"}, {"category": "Sm", "mappings": {"default": {"default": "multiplication sign with dot above"}, "mathspeak": {"default": "multiplication-sign-with-dot-above"}}, "key": "2A30"}, {"category": "Sm", "mappings": {"default": {"default": "multiplication sign with underbar"}, "mathspeak": {"default": "multiplication-sign-with-underbar"}}, "key": "2A31"}, {"category": "Sm", "mappings": {"default": {"default": "semidirect product with bottom closed"}, "mathspeak": {"default": "semidirect-product-with-bottom-closed"}}, "key": "2A32"}, {"category": "Sm", "mappings": {"default": {"default": "smash product"}, "mathspeak": {"default": "smash-product"}}, "key": "2A33"}, {"category": "Sm", "mappings": {"default": {"default": "multiplication sign in left half circle"}, "mathspeak": {"default": "multiplication-sign-in-left-half-circle"}}, "key": "2A34"}, {"category": "Sm", "mappings": {"default": {"default": "multiplication sign in right half circle"}, "mathspeak": {"default": "multiplication-sign-in-right-half-circle"}}, "key": "2A35"}, {"category": "Sm", "mappings": {"default": {"default": "circled multiplication sign with circumflex accent"}, "mathspeak": {"default": "circled-multiplication-sign-with-circumflex-accent"}}, "key": "2A36"}, {"category": "Sm", "mappings": {"default": {"default": "multiplication sign in double circle"}, "mathspeak": {"default": "multiplication-sign-in-double-circle"}}, "key": "2A37"}, {"category": "Sm", "mappings": {"default": {"default": "circled division sign", "short": "circled division"}, "mathspeak": {"default": "circled-division"}}, "key": "2A38"}, {"category": "Sm", "mappings": {"default": {"default": "plus sign in triangle"}, "mathspeak": {"default": "plus-sign-in-triangle"}}, "key": "2A39"}, {"category": "Sm", "mappings": {"default": {"default": "minus sign in triangle"}, "mathspeak": {"default": "minus-sign-in-triangle"}}, "key": "2A3A"}, {"category": "Sm", "mappings": {"default": {"default": "multiplication sign in triangle"}, "mathspeak": {"default": "multiplication-sign-in-triangle"}}, "key": "2A3B"}, {"category": "Sm", "mappings": {"default": {"default": "interior product"}, "mathspeak": {"default": "interior-product"}}, "key": "2A3C"}, {"category": "Sm", "mappings": {"default": {"default": "righthand interior product"}, "mathspeak": {"default": "righthand-interior-product"}}, "key": "2A3D"}, {"category": "Sm", "mappings": {"default": {"default": "z notation relational composition"}, "mathspeak": {"default": "z-notation-relational-composition"}}, "key": "2A3E"}, {"category": "Sm", "mappings": {"default": {"default": "amalgamation or coproduct"}, "mathspeak": {"default": "amalgamation-or-coproduct"}}, "key": "2A3F"}, {"category": "Sm", "mappings": {"default": {"default": "intersection with dot"}, "mathspeak": {"default": "intersection-with-dot"}}, "key": "2A40"}, {"category": "Sm", "mappings": {"default": {"default": "union with minus sign", "short": "union with minus"}, "mathspeak": {"default": "union-with-minus"}}, "key": "2A41"}, {"category": "Sm", "mappings": {"default": {"default": "union with overbar"}, "mathspeak": {"default": "union-with-overbar"}}, "key": "2A42"}, {"category": "Sm", "mappings": {"default": {"default": "intersection with overbar"}, "mathspeak": {"default": "intersection-with-overbar"}}, "key": "2A43"}, {"category": "Sm", "mappings": {"default": {"default": "intersection with logical and"}, "mathspeak": {"default": "intersection-with-logical-and"}}, "key": "2A44"}, {"category": "Sm", "mappings": {"default": {"default": "union with logical or"}, "mathspeak": {"default": "union-with-logical-or"}}, "key": "2A45"}, {"category": "Sm", "mappings": {"default": {"default": "union above intersection"}, "mathspeak": {"default": "union-above-intersection"}}, "key": "2A46"}, {"category": "Sm", "mappings": {"default": {"default": "intersection above union"}, "mathspeak": {"default": "intersection-above-union"}}, "key": "2A47"}, {"category": "Sm", "mappings": {"default": {"default": "union above bar above intersection"}, "mathspeak": {"default": "union-above-bar-above-intersection"}}, "key": "2A48"}, {"category": "Sm", "mappings": {"default": {"default": "intersection above bar above union"}, "mathspeak": {"default": "intersection-above-bar-above-union"}}, "key": "2A49"}, {"category": "Sm", "mappings": {"default": {"default": "union beside and joined with union"}, "mathspeak": {"default": "union-beside-and-joined-with-union"}}, "key": "2A4A"}, {"category": "Sm", "mappings": {"default": {"default": "intersection beside and joined with intersection"}, "mathspeak": {"default": "intersection-beside-and-joined-with-intersection"}}, "key": "2A4B"}, {"category": "Sm", "mappings": {"default": {"default": "closed union with serifs"}, "mathspeak": {"default": "closed-union-with-serifs"}}, "key": "2A4C"}, {"category": "Sm", "mappings": {"default": {"default": "closed intersection with serifs"}, "mathspeak": {"default": "closed-intersection-with-serifs"}}, "key": "2A4D"}, {"category": "Sm", "mappings": {"default": {"default": "double square intersection"}, "mathspeak": {"default": "double-square-intersection"}}, "key": "2A4E"}, {"category": "Sm", "mappings": {"default": {"default": "double square union"}, "mathspeak": {"default": "double-square-union"}}, "key": "2A4F"}, {"category": "Sm", "mappings": {"default": {"default": "closed union with serifs and smash product"}, "mathspeak": {"default": "closed-union-with-serifs-and-smash-product"}}, "key": "2A50"}, {"category": "Sm", "mappings": {"default": {"default": "logical and with dot above"}, "mathspeak": {"default": "logical-and-with-dot-above"}}, "key": "2A51"}, {"category": "Sm", "mappings": {"default": {"default": "logical or with dot above"}, "mathspeak": {"default": "logical-or-with-dot-above"}}, "key": "2A52"}, {"category": "Sm", "mappings": {"default": {"default": "double logical and"}, "mathspeak": {"default": "double-logical-and"}}, "key": "2A53"}, {"category": "Sm", "mappings": {"default": {"default": "double logical or"}, "mathspeak": {"default": "double-logical-or"}}, "key": "2A54"}, {"category": "Sm", "mappings": {"default": {"default": "two intersecting logical and"}, "mathspeak": {"default": "two-intersecting-logical-and"}}, "key": "2A55"}, {"category": "Sm", "mappings": {"default": {"default": "two intersecting logical or"}, "mathspeak": {"default": "two-intersecting-logical-or"}}, "key": "2A56"}, {"category": "Sm", "mappings": {"default": {"default": "sloping large or"}, "mathspeak": {"default": "sloping-large-or"}}, "key": "2A57"}, {"category": "Sm", "mappings": {"default": {"default": "sloping large and"}, "mathspeak": {"default": "sloping-large-and"}}, "key": "2A58"}, {"category": "Sm", "mappings": {"default": {"default": "logical or overlapping logical and"}, "mathspeak": {"default": "logical-or-overlapping-logical-and"}}, "key": "2A59"}, {"category": "Sm", "mappings": {"default": {"default": "logical and with middle stem"}, "mathspeak": {"default": "logical-and-with-middle-stem"}}, "key": "2A5A"}, {"category": "Sm", "mappings": {"default": {"default": "logical or with middle stem"}, "mathspeak": {"default": "logical-or-with-middle-stem"}}, "key": "2A5B"}, {"category": "Sm", "mappings": {"default": {"default": "logical and with horizontal dash"}, "mathspeak": {"default": "logical-and-with-horizontal-dash"}}, "key": "2A5C"}, {"category": "Sm", "mappings": {"default": {"default": "logical or with horizontal dash"}, "mathspeak": {"default": "logical-or-with-horizontal-dash"}}, "key": "2A5D"}, {"category": "Sm", "mappings": {"default": {"default": "logical and with double overbar"}, "mathspeak": {"default": "logical-and-with-double-overbar"}}, "key": "2A5E"}, {"category": "Sm", "mappings": {"default": {"default": "logical and with underbar"}, "mathspeak": {"default": "logical-and-with-underbar"}}, "key": "2A5F"}, {"category": "Sm", "mappings": {"default": {"default": "logical and with double underbar"}, "mathspeak": {"default": "logical-and-with-double-underbar"}}, "key": "2A60"}, {"category": "Sm", "mappings": {"default": {"default": "small vee with underbar"}, "mathspeak": {"default": "small-vee-with-underbar"}}, "key": "2A61"}, {"category": "Sm", "mappings": {"default": {"default": "logical or with double overbar"}, "mathspeak": {"default": "logical-or-with-double-overbar"}}, "key": "2A62"}, {"category": "Sm", "mappings": {"default": {"default": "logical or with double underbar"}, "mathspeak": {"default": "logical-or-with-double-underbar"}}, "key": "2A63"}, {"category": "Sm", "mappings": {"default": {"default": "z notation domain antirestriction"}, "mathspeak": {"default": "z-notation-domain-antirestriction"}}, "key": "2A64"}, {"category": "Sm", "mappings": {"default": {"default": "z notation range antirestriction"}, "mathspeak": {"default": "z-notation-range-antirestriction"}}, "key": "2A65"}, {"category": "Sm", "mappings": {"default": {"default": "equals sign with dot below"}, "mathspeak": {"default": "equals-with-dot-below"}}, "key": "2A66"}, {"category": "Sm", "mappings": {"default": {"default": "identical with dot above"}, "mathspeak": {"default": "identical-with-dot-above"}}, "key": "2A67"}, {"category": "Sm", "mappings": {"default": {"default": "triple horizontal bar with double vertical stroke"}, "mathspeak": {"default": "triple-horizontal-bar-with-double-vertical-stroke"}}, "key": "2A68"}, {"category": "Sm", "mappings": {"default": {"default": "triple horizontal bar with triple vertical stroke"}, "mathspeak": {"default": "triple-horizontal-bar-with-triple-vertical-stroke"}}, "key": "2A69"}, {"category": "Sm", "mappings": {"default": {"default": "tilde operator with dot above"}, "mathspeak": {"default": "tilde-with-dot-above"}}, "key": "2A6A"}, {"category": "Sm", "mappings": {"default": {"default": "tilde operator with rising dots"}, "mathspeak": {"default": "tilde-with-rising-dots"}}, "key": "2A6B"}, {"category": "Sm", "mappings": {"default": {"default": "similar minus similar"}, "mathspeak": {"default": "similar-minus-similar"}}, "key": "2A6C"}, {"category": "Sm", "mappings": {"default": {"default": "congruent with dot above"}, "mathspeak": {"default": "congruent-with-dot-above"}}, "key": "2A6D"}, {"category": "Sm", "mappings": {"default": {"default": "equals with asterisk"}, "mathspeak": {"default": "equals-with-asterisk"}}, "key": "2A6E"}, {"category": "Sm", "mappings": {"default": {"default": "almost equals with circumflex accent", "short": "almost equal hat"}, "mathspeak": {"default": "almost-equal-hat"}}, "key": "2A6F"}, {"category": "Sm", "mappings": {"default": {"default": "approximately equal or equals"}, "mathspeak": {"default": "approximately-equal-or-equal-to"}}, "key": "2A70"}, {"category": "Sm", "mappings": {"default": {"default": "equals sign above plus sign", "short": "equals above plus"}, "mathspeak": {"default": "equals-above-plus"}}, "key": "2A71"}, {"category": "Sm", "mappings": {"default": {"default": "plus sign above equals sign", "short": "plus above equals"}, "mathspeak": {"default": "plus-above-equals"}}, "key": "2A72"}, {"category": "Sm", "mappings": {"default": {"default": "equals sign above tilde operator", "short": "equals above tilde operator"}, "mathspeak": {"default": "equals-above-tilde"}}, "key": "2A73"}, {"category": "Sm", "mappings": {"default": {"default": "double colon equal"}, "mathspeak": {"default": "double-colon-equal"}}, "key": "2A74"}, {"category": "Sm", "mappings": {"default": {"default": "two consecutive equals signs", "short": "two consecutive equals"}, "mathspeak": {"default": "two-consecutive-equals"}}, "key": "2A75"}, {"category": "Sm", "mappings": {"default": {"default": "three consecutive equals signs", "short": "three consecutive equals"}, "mathspeak": {"default": "three-consecutive-equals"}}, "key": "2A76"}, {"category": "Sm", "mappings": {"default": {"default": "equals sign with two dots above and two dots below"}, "mathspeak": {"default": "equals-with-two-dots-above-and-two-dots-below"}}, "key": "2A77"}, {"category": "Sm", "mappings": {"default": {"default": "equivalent with four dots above"}, "mathspeak": {"default": "equivalent-with-four-dots-above"}}, "key": "2A78"}, {"category": "Sm", "mappings": {"default": {"default": "less than with circle inside"}, "mathspeak": {"default": "less-than-with-circle-inside"}}, "key": "2A79"}, {"category": "Sm", "mappings": {"default": {"default": "greater than with circle inside"}, "mathspeak": {"default": "greater-than-with-circle-inside"}}, "key": "2A7A"}, {"category": "Sm", "mappings": {"default": {"default": "less than with question mark above"}, "mathspeak": {"default": "less-than-with-question-mark-above"}}, "key": "2A7B"}, {"category": "Sm", "mappings": {"default": {"default": "greater than with question mark above"}, "mathspeak": {"default": "greater-than-with-question-mark-above"}}, "key": "2A7C"}, {"category": "Sm", "mappings": {"default": {"default": "less than or slanted equals"}, "mathspeak": {"default": "less-than-or-slanted-equals"}}, "key": "2A7D"}, {"category": "Sm", "mappings": {"default": {"default": "greater than or slanted equals"}, "mathspeak": {"default": "greater-than-or-slanted-equals"}}, "key": "2A7E"}, {"category": "Sm", "mappings": {"default": {"default": "less than or slanted equals with dot inside"}, "mathspeak": {"default": "less-than-or-slanted-equals-with-dot-inside"}}, "key": "2A7F"}, {"category": "Sm", "mappings": {"default": {"default": "greater than or slanted equals with dot inside"}, "mathspeak": {"default": "greater-than-or-slanted-equals-with-dot-inside"}}, "key": "2A80"}, {"category": "Sm", "mappings": {"default": {"default": "less than or slanted equals with dot above"}, "mathspeak": {"default": "less-than-or-slanted-equals-with-dot-above"}}, "key": "2A81"}, {"category": "Sm", "mappings": {"default": {"default": "greater than or slanted equals with dot above"}, "mathspeak": {"default": "greater-than-or-slanted-equals-with-dot-above"}}, "key": "2A82"}, {"category": "Sm", "mappings": {"default": {"default": "less than or slanted equals with dot above right"}, "mathspeak": {"default": "less-than-or-slanted-equals-with-dot-above-right"}}, "key": "2A83"}, {"category": "Sm", "mappings": {"default": {"default": "greater than or slanted equals with dot above left"}, "mathspeak": {"default": "greater-than-or-slanted-equals-with-dot-above-left"}}, "key": "2A84"}, {"category": "Sm", "mappings": {"default": {"default": "less than or approximate"}, "mathspeak": {"default": "less-than-or-approximate"}}, "key": "2A85"}, {"category": "Sm", "mappings": {"default": {"default": "greater than or approximate"}, "mathspeak": {"default": "greater-than-or-approximate"}}, "key": "2A86"}, {"category": "Sm", "mappings": {"default": {"default": "less than and single line not equals"}, "mathspeak": {"default": "less-than-and-single-line-not-equals"}}, "key": "2A87"}, {"category": "Sm", "mappings": {"default": {"default": "greater than and single line not equals"}, "mathspeak": {"default": "greater-than-and-single-line-not-equals"}}, "key": "2A88"}, {"category": "Sm", "mappings": {"default": {"default": "less than and not approximate"}, "mathspeak": {"default": "less-than-and-not-approximate"}}, "key": "2A89"}, {"category": "Sm", "mappings": {"default": {"default": "greater than and not approximate"}, "mathspeak": {"default": "greater-than-and-not-approximate"}}, "key": "2A8A"}, {"category": "Sm", "mappings": {"default": {"default": "less than above double line equal above greater than"}, "mathspeak": {"default": "less-than-above-double-line-equal-above-greater-than"}}, "key": "2A8B"}, {"category": "Sm", "mappings": {"default": {"default": "greater than above double line equal above less than"}, "mathspeak": {"default": "greater-than-above-double-line-equal-above-less-than"}}, "key": "2A8C"}, {"category": "Sm", "mappings": {"default": {"default": "less than above similar or equal"}, "mathspeak": {"default": "less-than-above-similar-or-equal"}}, "key": "2A8D"}, {"category": "Sm", "mappings": {"default": {"default": "greater than above similar or equal"}, "mathspeak": {"default": "greater-than-above-similar-or-equal"}}, "key": "2A8E"}, {"category": "Sm", "mappings": {"default": {"default": "less than above similar above greater than"}, "mathspeak": {"default": "less-than-above-similar-above-greater-than"}}, "key": "2A8F"}, {"category": "Sm", "mappings": {"default": {"default": "greater than above similar above less than"}, "mathspeak": {"default": "greater-than-above-similar-above-less-than"}}, "key": "2A90"}, {"category": "Sm", "mappings": {"default": {"default": "less than above greater than above double line equal"}, "mathspeak": {"default": "less-than-above-greater-than-above-double-line-equal"}}, "key": "2A91"}, {"category": "Sm", "mappings": {"default": {"default": "greater than above less than above double line equal"}, "mathspeak": {"default": "greater-than-above-less-than-above-double-line-equal"}}, "key": "2A92"}, {"category": "Sm", "mappings": {"default": {"default": "less than above slanted equal above greater than above slanted equal"}, "mathspeak": {"default": "less-than-above-slanted-equal-above-greater-than-above-slanted-equal"}}, "key": "2A93"}, {"category": "Sm", "mappings": {"default": {"default": "greater than above slanted equal above less than above slanted equal"}, "mathspeak": {"default": "greater-than-above-slanted-equal-above-less-than-above-slanted-equal"}}, "key": "2A94"}, {"category": "Sm", "mappings": {"default": {"default": "slanted equals or less than"}, "mathspeak": {"default": "slanted-equals-or-less-than"}}, "key": "2A95"}, {"category": "Sm", "mappings": {"default": {"default": "slanted equals or greater than"}, "mathspeak": {"default": "slanted-equals-or-greater-than"}}, "key": "2A96"}, {"category": "Sm", "mappings": {"default": {"default": "slanted equals or less than with dot inside"}, "mathspeak": {"default": "slanted-equals-or-less-than-with-dot-inside"}}, "key": "2A97"}, {"category": "Sm", "mappings": {"default": {"default": "slanted equals or greater than with dot inside"}, "mathspeak": {"default": "slanted-equals-or-greater-than-with-dot-inside"}}, "key": "2A98"}, {"category": "Sm", "mappings": {"default": {"default": "double line equals or less than"}, "mathspeak": {"default": "double-line-equals-or-less-than"}}, "key": "2A99"}, {"category": "Sm", "mappings": {"default": {"default": "double line equals or greater than"}, "mathspeak": {"default": "double-line-equals-or-greater-than"}}, "key": "2A9A"}, {"category": "Sm", "mappings": {"default": {"default": "double line slanted equals or less than"}, "mathspeak": {"default": "double-line-slanted-equals-or-less-than"}}, "key": "2A9B"}, {"category": "Sm", "mappings": {"default": {"default": "double line slanted equals or greater than"}, "mathspeak": {"default": "double-line-slanted-equals-or-greater-than"}}, "key": "2A9C"}, {"category": "Sm", "mappings": {"default": {"default": "similar or less than"}, "mathspeak": {"default": "similar-or-less-than"}}, "key": "2A9D"}, {"category": "Sm", "mappings": {"default": {"default": "similar or greater than"}, "mathspeak": {"default": "similar-or-greater-than"}}, "key": "2A9E"}, {"category": "Sm", "mappings": {"default": {"default": "similar above less than above equals sign"}, "mathspeak": {"default": "similar-above-less-than-above-equals"}}, "key": "2A9F"}, {"category": "Sm", "mappings": {"default": {"default": "similar above greater than above equals sign"}, "mathspeak": {"default": "similar-above-greater-than-above-equals"}}, "key": "2AA0"}, {"category": "Sm", "mappings": {"default": {"default": "double nested less than"}, "mathspeak": {"default": "double-nested-less-than"}}, "key": "2AA1"}, {"category": "Sm", "mappings": {"default": {"default": "double nested greater than"}, "mathspeak": {"default": "double-nested-greater-than"}}, "key": "2AA2"}, {"category": "Sm", "mappings": {"default": {"default": "double nested less than with underbar"}, "mathspeak": {"default": "double-nested-less-than-with-underbar"}}, "key": "2AA3"}, {"category": "Sm", "mappings": {"default": {"default": "greater than overlapping less than"}, "mathspeak": {"default": "greater-than-overlapping-less-than"}}, "key": "2AA4"}, {"category": "Sm", "mappings": {"default": {"default": "greater than beside less than"}, "mathspeak": {"default": "greater-than-beside-less-than"}}, "key": "2AA5"}, {"category": "Sm", "mappings": {"default": {"default": "less than closed by curve"}, "mathspeak": {"default": "less-than-closed-by-curve"}}, "key": "2AA6"}, {"category": "Sm", "mappings": {"default": {"default": "greater than closed by curve"}, "mathspeak": {"default": "greater-than-closed-by-curve"}}, "key": "2AA7"}, {"category": "Sm", "mappings": {"default": {"default": "less than closed by curve above slanted equal"}, "mathspeak": {"default": "less-than-closed-by-curve-above-slanted-equal"}}, "key": "2AA8"}, {"category": "Sm", "mappings": {"default": {"default": "greater than closed by curve above slanted equal"}, "mathspeak": {"default": "greater-than-closed-by-curve-above-slanted-equal"}}, "key": "2AA9"}, {"category": "Sm", "mappings": {"default": {"default": "smaller than"}, "mathspeak": {"default": "smaller-than"}}, "key": "2AAA"}, {"category": "Sm", "mappings": {"default": {"default": "larger than"}, "mathspeak": {"default": "larger-than"}}, "key": "2AAB"}, {"category": "Sm", "mappings": {"default": {"default": "smaller than or equals"}, "mathspeak": {"default": "smaller-than-or-equal-to"}}, "key": "2AAC"}, {"category": "Sm", "mappings": {"default": {"default": "larger than or equals"}, "mathspeak": {"default": "larger-than-or-equal-to"}}, "key": "2AAD"}, {"category": "Sm", "mappings": {"default": {"default": "equals sign with bumpy above"}, "mathspeak": {"default": "equals-with-bumpy-above"}}, "key": "2AAE"}, {"category": "Sm", "mappings": {"default": {"default": "precedes above single line equals sign"}, "mathspeak": {"default": "precedes-above-single-line-equals"}}, "key": "2AAF"}, {"category": "Sm", "mappings": {"default": {"default": "succeeds above single line equals sign"}, "mathspeak": {"default": "succeeds-above-single-line-equals"}}, "key": "2AB0"}, {"category": "Sm", "mappings": {"default": {"default": "precedes above single line not equals"}, "mathspeak": {"default": "precedes-above-single-line-not-equals"}}, "key": "2AB1"}, {"category": "Sm", "mappings": {"default": {"default": "succeeds above single line not equals"}, "mathspeak": {"default": "succeeds-above-single-line-not-equals"}}, "key": "2AB2"}, {"category": "Sm", "mappings": {"default": {"default": "precedes above equals sign"}, "mathspeak": {"default": "precedes-above-equals"}}, "key": "2AB3"}, {"category": "Sm", "mappings": {"default": {"default": "succeeds above equals sign"}, "mathspeak": {"default": "succeeds-above-equals"}}, "key": "2AB4"}, {"category": "Sm", "mappings": {"default": {"default": "precedes above not equals"}, "mathspeak": {"default": "precedes-above-not-equals"}}, "key": "2AB5"}, {"category": "Sm", "mappings": {"default": {"default": "succeeds above not equals"}, "mathspeak": {"default": "succeeds-above-not-equals"}}, "key": "2AB6"}, {"category": "Sm", "mappings": {"default": {"default": "precedes above almost equals"}, "mathspeak": {"default": "precedes-above-almost-equals"}}, "key": "2AB7"}, {"category": "Sm", "mappings": {"default": {"default": "succeeds above almost equals"}, "mathspeak": {"default": "succeeds-above-almost-equals"}}, "key": "2AB8"}, {"category": "Sm", "mappings": {"default": {"default": "precedes above not almost equals"}, "mathspeak": {"default": "precedes-above-not-almost-equals"}}, "key": "2AB9"}, {"category": "Sm", "mappings": {"default": {"default": "succeeds above not almost equals"}, "mathspeak": {"default": "succeeds-above-not-almost-equals"}}, "key": "2ABA"}, {"category": "Sm", "mappings": {"default": {"default": "double precedes"}, "mathspeak": {"default": "double-precedes"}}, "key": "2ABB"}, {"category": "Sm", "mappings": {"default": {"default": "double succeeds"}, "mathspeak": {"default": "double-succeeds"}}, "key": "2ABC"}, {"category": "Sm", "mappings": {"default": {"default": "subset with dot"}, "mathspeak": {"default": "subset-with-dot"}}, "key": "2ABD"}, {"category": "Sm", "mappings": {"default": {"default": "superset with dot"}, "mathspeak": {"default": "superset-with-dot"}}, "key": "2ABE"}, {"category": "Sm", "mappings": {"default": {"default": "subset with plus sign below"}, "mathspeak": {"default": "subset-with-plus-sign-below"}}, "key": "2ABF"}, {"category": "Sm", "mappings": {"default": {"default": "superset with plus sign below"}, "mathspeak": {"default": "superset-with-plus-sign-below"}}, "key": "2AC0"}, {"category": "Sm", "mappings": {"default": {"default": "subset with multiplication sign below"}, "mathspeak": {"default": "subset-with-multiplication-sign-below"}}, "key": "2AC1"}, {"category": "Sm", "mappings": {"default": {"default": "superset with multiplication sign below"}, "mathspeak": {"default": "superset-with-multiplication-sign-below"}}, "key": "2AC2"}, {"category": "Sm", "mappings": {"default": {"default": "subset of or equals with dot above"}, "mathspeak": {"default": "subset-of-or-equal-to-with-dot-above"}}, "key": "2AC3"}, {"category": "Sm", "mappings": {"default": {"default": "superset of or equals with dot above"}, "mathspeak": {"default": "superset-of-or-equal-to-with-dot-above"}}, "key": "2AC4"}, {"category": "Sm", "mappings": {"default": {"default": "subset of above equals sign"}, "mathspeak": {"default": "subset-of-above-equals"}}, "key": "2AC5"}, {"category": "Sm", "mappings": {"default": {"default": "superset of above equals sign"}, "mathspeak": {"default": "superset-of-above-equals"}}, "key": "2AC6"}, {"category": "Sm", "mappings": {"default": {"default": "subset of above tilde operator"}, "mathspeak": {"default": "subset-of-above-tilde"}}, "key": "2AC7"}, {"category": "Sm", "mappings": {"default": {"default": "superset of above tilde operator"}, "mathspeak": {"default": "superset-of-above-tilde"}}, "key": "2AC8"}, {"category": "Sm", "mappings": {"default": {"default": "subset of above almost equals"}, "mathspeak": {"default": "subset-of-above-almost-equals"}}, "key": "2AC9"}, {"category": "Sm", "mappings": {"default": {"default": "superset of above almost equals"}, "mathspeak": {"default": "superset-of-above-almost-equals"}}, "key": "2ACA"}, {"category": "Sm", "mappings": {"default": {"default": "subset of above not equals"}, "mathspeak": {"default": "subset-of-above-not-equals"}}, "key": "2ACB"}, {"category": "Sm", "mappings": {"default": {"default": "superset of above not equals"}, "mathspeak": {"default": "superset-of-above-not-equals"}}, "key": "2ACC"}, {"category": "Sm", "mappings": {"default": {"default": "square left open box operator"}, "mathspeak": {"default": "square-left-open-box"}}, "key": "2ACD"}, {"category": "Sm", "mappings": {"default": {"default": "square right open box operator"}, "mathspeak": {"default": "square-right-open-box"}}, "key": "2ACE"}, {"category": "Sm", "mappings": {"default": {"default": "closed subset"}, "mathspeak": {"default": "closed-subset"}}, "key": "2ACF"}, {"category": "Sm", "mappings": {"default": {"default": "closed superset"}, "mathspeak": {"default": "closed-superset"}}, "key": "2AD0"}, {"category": "Sm", "mappings": {"default": {"default": "closed subset or equals"}, "mathspeak": {"default": "closed-subset-or-equal-to"}}, "key": "2AD1"}, {"category": "Sm", "mappings": {"default": {"default": "closed superset or equals"}, "mathspeak": {"default": "closed-superset-or-equal-to"}}, "key": "2AD2"}, {"category": "Sm", "mappings": {"default": {"default": "subset above superset"}, "mathspeak": {"default": "subset-above-superset"}}, "key": "2AD3"}, {"category": "Sm", "mappings": {"default": {"default": "superset above subset"}, "mathspeak": {"default": "superset-above-subset"}}, "key": "2AD4"}, {"category": "Sm", "mappings": {"default": {"default": "subset above subset"}, "mathspeak": {"default": "subset-above-subset"}}, "key": "2AD5"}, {"category": "Sm", "mappings": {"default": {"default": "superset above superset"}, "mathspeak": {"default": "superset-above-superset"}}, "key": "2AD6"}, {"category": "Sm", "mappings": {"default": {"default": "superset beside subset"}, "mathspeak": {"default": "superset-beside-subset"}}, "key": "2AD7"}, {"category": "Sm", "mappings": {"default": {"default": "superset beside and joined by dash with subset"}, "mathspeak": {"default": "superset-beside-and-joined-by-dash-with-subset"}}, "key": "2AD8"}, {"category": "Sm", "mappings": {"default": {"default": "element of opening downwards"}, "mathspeak": {"default": "element-of-opening-downwards"}}, "key": "2AD9"}, {"category": "Sm", "mappings": {"default": {"default": "pitchfork with tee top"}, "mathspeak": {"default": "pitchfork-with-tee-top"}}, "key": "2ADA"}, {"category": "Sm", "mappings": {"default": {"default": "transversal intersection"}, "mathspeak": {"default": "transversal-intersection"}}, "key": "2ADB"}, {"category": "Sm", "mappings": {"default": {"default": "forking"}}, "key": "2ADC"}, {"category": "Sm", "mappings": {"default": {"default": "nonforking"}}, "key": "2ADD"}, {"category": "Sm", "mappings": {"default": {"default": "short left tack"}, "mathspeak": {"default": "short-left-tack"}}, "key": "2ADE"}, {"category": "Sm", "mappings": {"default": {"default": "short down tack"}, "mathspeak": {"default": "short-down-tack"}}, "key": "2ADF"}, {"category": "Sm", "mappings": {"default": {"default": "short up tack"}, "mathspeak": {"default": "short-up-tack"}}, "key": "2AE0"}, {"category": "Sm", "mappings": {"default": {"default": "perpendicular with s"}, "mathspeak": {"default": "perpendicular-with-s"}}, "key": "2AE1"}, {"category": "Sm", "mappings": {"default": {"default": "vertical bar triple right turnstile"}, "mathspeak": {"default": "vertical-bar-triple-right-turnstile"}}, "key": "2AE2"}, {"category": "Sm", "mappings": {"default": {"default": "double vertical bar left turnstile"}, "mathspeak": {"default": "double-vertical-bar-left-turnstile"}}, "key": "2AE3"}, {"category": "Sm", "mappings": {"default": {"default": "vertical bar double left turnstile"}, "mathspeak": {"default": "vertical-bar-double-left-turnstile"}}, "key": "2AE4"}, {"category": "Sm", "mappings": {"default": {"default": "double vertical bar double left turnstile"}, "mathspeak": {"default": "double-vertical-bar-double-left-turnstile"}}, "key": "2AE5"}, {"category": "Sm", "mappings": {"default": {"default": "long dash from left member of double vertical"}, "mathspeak": {"default": "long-dash-from-left-member-of-double-vertical"}}, "key": "2AE6"}, {"category": "Sm", "mappings": {"default": {"default": "short down tack with overbar"}, "mathspeak": {"default": "short-down-tack-with-overbar"}}, "key": "2AE7"}, {"category": "Sm", "mappings": {"default": {"default": "short up tack with underbar"}, "mathspeak": {"default": "short-up-tack-with-underbar"}}, "key": "2AE8"}, {"category": "Sm", "mappings": {"default": {"default": "short up tack above short down tack"}, "mathspeak": {"default": "short-up-tack-above-short-down-tack"}}, "key": "2AE9"}, {"category": "Sm", "mappings": {"default": {"default": "double down tack"}, "mathspeak": {"default": "double-down-tack"}}, "key": "2AEA"}, {"category": "Sm", "mappings": {"default": {"default": "double up tack"}, "mathspeak": {"default": "double-up-tack"}}, "key": "2AEB"}, {"category": "Sm", "mappings": {"default": {"default": "double stroke not sign"}, "mathspeak": {"default": "double-stroke-not-sign"}}, "key": "2AEC"}, {"category": "Sm", "mappings": {"default": {"default": "reversed double stroke not sign"}, "mathspeak": {"default": "reversed-double-stroke-not-sign"}}, "key": "2AED"}, {"category": "Sm", "mappings": {"default": {"default": "does not divide with reversed negation slash"}, "mathspeak": {"default": "does-not-divide-with-reversed-negation-slash"}}, "key": "2AEE"}, {"category": "Sm", "mappings": {"default": {"default": "vertical line with circle above"}, "mathspeak": {"default": "vertical-line-with-circle-above"}}, "key": "2AEF"}, {"category": "Sm", "mappings": {"default": {"default": "vertical line with circle below"}, "mathspeak": {"default": "vertical-line-with-circle-below"}}, "key": "2AF0"}, {"category": "Sm", "mappings": {"default": {"default": "down tack with circle below"}, "mathspeak": {"default": "down-tack-with-circle-below"}}, "key": "2AF1"}, {"category": "Sm", "mappings": {"default": {"default": "parallel with horizontal stroke"}, "mathspeak": {"default": "parallel-with-horizontal-stroke"}}, "key": "2AF2"}, {"category": "Sm", "mappings": {"default": {"default": "parallel with tilde operator"}, "mathspeak": {"default": "parallel-with-tilde"}}, "key": "2AF3"}, {"category": "Sm", "mappings": {"default": {"default": "triple vertical bar binary relation"}, "mathspeak": {"default": "triple-vertical-bar-binary-relation"}}, "key": "2AF4"}, {"category": "Sm", "mappings": {"default": {"default": "triple vertical bar with horizontal stroke"}, "mathspeak": {"default": "triple-vertical-bar-with-horizontal-stroke"}}, "key": "2AF5"}, {"category": "Sm", "mappings": {"default": {"default": "triple colon operator"}, "mathspeak": {"default": "triple-colon"}}, "key": "2AF6"}, {"category": "Sm", "mappings": {"default": {"default": "triple nested less than"}, "mathspeak": {"default": "triple-nested-less-than"}}, "key": "2AF7"}, {"category": "Sm", "mappings": {"default": {"default": "triple nested greater than"}, "mathspeak": {"default": "triple-nested-greater-than"}}, "key": "2AF8"}, {"category": "Sm", "mappings": {"default": {"default": "double line slanted less than or equals"}, "mathspeak": {"default": "double-line-slanted-less-than-or-equal-to"}}, "key": "2AF9"}, {"category": "Sm", "mappings": {"default": {"default": "double line slanted greater than or equals"}, "mathspeak": {"default": "double-line-slanted-greater-than-or-equal-to"}}, "key": "2AFA"}, {"category": "Sm", "mappings": {"default": {"default": "triple solidus binary relation"}, "mathspeak": {"default": "triple-solidus-binary-relation"}}, "key": "2AFB"}, {"category": "Sm", "mappings": {"default": {"default": "large triple vertical bar operator"}, "mathspeak": {"default": "large-triple-vertical-bar"}}, "key": "2AFC"}, {"category": "Sm", "mappings": {"default": {"default": "double solidus operator"}, "mathspeak": {"default": "double-solidus"}}, "key": "2AFD"}, {"category": "Sm", "mappings": {"default": {"default": "white vertical bar"}, "mathspeak": {"default": "white-vertical-bar"}}, "key": "2AFE"}, {"category": "Sm", "mappings": {"default": {"default": "n ary white vertical bar"}, "mathspeak": {"default": "white-vertical-bar"}}, "key": "2AFF"}, {"category": "Pd", "mappings": {"default": {"default": "wave dash"}, "mathspeak": {"default": "wave-dash"}}, "key": "301C"}, {"category": "Po", "mappings": {"default": {"default": "presentation form for vertical comma"}, "mathspeak": {"default": "presentation-form-for-vertical-comma"}}, "key": "FE10"}, {"category": "Po", "mappings": {"default": {"default": "presentation form for vertical colon"}, "mathspeak": {"default": "presentation-form-for-vertical-colon"}}, "key": "FE13"}, {"category": "Po", "mappings": {"default": {"default": "presentation form for vertical semicolon"}, "mathspeak": {"default": "presentation-form-for-vertical-semicolon"}}, "key": "FE14"}, {"category": "Po", "mappings": {"default": {"default": "presentation form for vertical exclamation mark"}, "mathspeak": {"default": "presentation-form-for-vertical-exclamation-mark"}}, "key": "FE15"}, {"category": "Po", "mappings": {"default": {"default": "presentation form for vertical question mark"}, "mathspeak": {"default": "presentation-form-for-vertical-question-mark"}}, "key": "FE16"}, {"category": "Po", "mappings": {"default": {"default": "presentation form for vertical horizontal ellipsis"}, "mathspeak": {"default": "presentation-form-for-vertical-horizontal-ellipsis"}}, "key": "FE19"}, {"category": "Po", "mappings": {"default": {"default": "presentation form for vertical two dot leader", "alternative": "glyph for vertical two dot leader"}, "mathspeak": {"default": "glyph-for-vertical-two-dot-leader"}}, "key": "FE30"}, {"category": "Pd", "mappings": {"default": {"default": "presentation form for vertical em dash", "alternative": "glyph for vertical em dash"}, "mathspeak": {"default": "glyph-for-vertical-em-dash"}}, "key": "FE31"}, {"category": "Pd", "mappings": {"default": {"default": "presentation form for vertical en dash", "alternative": "glyph for vertical en dash"}, "mathspeak": {"default": "glyph-for-vertical-en-dash"}}, "key": "FE32"}, {"category": "Pc", "mappings": {"default": {"default": "presentation form for vertical low line", "alternative": "glyph for vertical spacing underscore"}, "mathspeak": {"default": "glyph-for-vertical-underscore"}}, "key": "FE33"}, {"category": "Pc", "mappings": {"default": {"default": "presentation form for vertical wavy low line", "alternative": "glyph for vertical spacing wavy underscore"}, "mathspeak": {"default": "glyph-for-vertical-wavy-underscore"}}, "key": "FE34"}, {"category": "Po", "mappings": {"default": {"default": "sesame dot"}, "mathspeak": {"default": "sesame-dot"}}, "key": "FE45"}, {"category": "Po", "mappings": {"default": {"default": "white sesame dot"}, "mathspeak": {"default": "white-sesame-dot"}}, "key": "FE46"}, {"category": "Po", "mappings": {"default": {"default": "dashed overline", "alternative": "spacing dashed overscore"}, "mathspeak": {"default": "dashed-overscore"}}, "key": "FE49"}, {"category": "Po", "mappings": {"default": {"default": "centerline overline", "alternative": "spacing centerline overscore"}, "mathspeak": {"default": "centerline-overscore"}}, "key": "FE4A"}, {"category": "Po", "mappings": {"default": {"default": "wavy overline", "alternative": "spacing wavy overscore"}, "mathspeak": {"default": "wavy-overscore"}}, "key": "FE4B"}, {"category": "Po", "mappings": {"default": {"default": "double wavy overline", "alternative": "spacing double wavy overscore"}, "mathspeak": {"default": "double-wavy-overscore"}}, "key": "FE4C"}, {"category": "Pc", "mappings": {"default": {"default": "dashed low line", "alternative": "spacing dashed underscore"}, "mathspeak": {"default": "dashed-underscore"}}, "key": "FE4D"}, {"category": "Pc", "mappings": {"default": {"default": "centerline low line", "alternative": "spacing centerline underscore"}, "mathspeak": {"default": "centerline-underscore"}}, "key": "FE4E"}, {"category": "Pc", "mappings": {"default": {"default": "wavy low line", "alternative": "spacing wavy underscore"}, "mathspeak": {"default": "wavy-underscore"}}, "key": "FE4F"}, {"category": "Po", "mappings": {"default": {"default": "small comma"}, "mathspeak": {"default": "small-comma"}}, "key": "FE50"}, {"category": "Po", "mappings": {"default": {"default": "small full stop", "alternative": "small period"}, "mathspeak": {"default": "small-period"}}, "key": "FE52"}, {"category": "Po", "mappings": {"default": {"default": "small semicolon"}, "mathspeak": {"default": "small-semicolon"}}, "key": "FE54"}, {"category": "Po", "mappings": {"default": {"default": "small colon"}, "mathspeak": {"default": "small-colon"}}, "key": "FE55"}, {"category": "Po", "mappings": {"default": {"default": "small question mark"}, "mathspeak": {"default": "small-question-mark"}}, "key": "FE56"}, {"category": "Po", "mappings": {"default": {"default": "small exclamation mark"}, "mathspeak": {"default": "small-exclamation-mark"}}, "key": "FE57"}, {"category": "Pd", "mappings": {"default": {"default": "small em dash"}, "mathspeak": {"default": "small-em-dash"}}, "key": "FE58"}, {"category": "Po", "mappings": {"default": {"default": "small number sign"}, "mathspeak": {"default": "small-number-sign"}}, "key": "FE5F"}, {"category": "Po", "mappings": {"default": {"default": "small ampersand"}, "mathspeak": {"default": "small-ampersand"}}, "key": "FE60"}, {"category": "Po", "mappings": {"default": {"default": "small asterisk"}, "mathspeak": {"default": "small-asterisk"}}, "key": "FE61"}, {"category": "Sm", "mappings": {"default": {"default": "small plus sign"}, "mathspeak": {"default": "small-plus-sign"}}, "key": "FE62"}, {"category": "Pd", "mappings": {"default": {"default": "small hyphen minus"}, "mathspeak": {"default": "small-hyphen-minus"}}, "key": "FE63"}, {"category": "Sm", "mappings": {"default": {"default": "small less than sign"}, "mathspeak": {"default": "small-less-than-sign"}}, "key": "FE64"}, {"category": "Sm", "mappings": {"default": {"default": "small greater than sign"}, "mathspeak": {"default": "small-greater-than-sign"}}, "key": "FE65"}, {"category": "Sm", "mappings": {"default": {"default": "small equals sign"}, "mathspeak": {"default": "small-equals"}}, "key": "FE66"}, {"category": "Po", "mappings": {"default": {"default": "small reverse solidus", "alternative": "small backslash"}, "mathspeak": {"default": "small-backslash"}}, "key": "FE68"}, {"category": "Sc", "mappings": {"default": {"default": "small dollar sign"}, "mathspeak": {"default": "small-dollar-sign"}}, "key": "FE69"}, {"category": "Po", "mappings": {"default": {"default": "small percent sign"}, "mathspeak": {"default": "small-percent-sign"}}, "key": "FE6A"}, {"category": "Po", "mappings": {"default": {"default": "small commercial at"}, "mathspeak": {"default": "small-commercial-at"}}, "key": "FE6B"}, {"category": "Po", "mappings": {"default": {"default": "fullwidth exclamation mark"}, "mathspeak": {"default": "exclamation-mark"}}, "key": "FF01"}, {"category": "Po", "mappings": {"default": {"default": "fullwidth quotation mark"}, "mathspeak": {"default": "quotation-mark"}}, "key": "FF02"}, {"category": "Po", "mappings": {"default": {"default": "fullwidth number sign"}, "mathspeak": {"default": "number-sign"}}, "key": "FF03"}, {"category": "Sc", "mappings": {"default": {"default": "fullwidth dollar sign"}, "mathspeak": {"default": "dollar-sign"}}, "key": "FF04"}, {"category": "Po", "mappings": {"default": {"default": "fullwidth percent sign"}, "mathspeak": {"default": "percent-sign"}}, "key": "FF05"}, {"category": "Po", "mappings": {"default": {"default": "fullwidth ampersand"}, "mathspeak": {"default": "ampersand"}}, "key": "FF06"}, {"category": "Po", "mappings": {"default": {"default": "fullwidth apostrophe"}, "mathspeak": {"default": "apostrophe"}}, "key": "FF07"}, {"category": "Po", "mappings": {"default": {"default": "fullwidth asterisk"}, "mathspeak": {"default": "asterisk"}}, "key": "FF0A"}, {"category": "Sm", "mappings": {"default": {"default": "fullwidth plus sign"}, "mathspeak": {"default": "plus-sign"}}, "key": "FF0B"}, {"category": "Po", "mappings": {"default": {"default": "fullwidth comma"}, "mathspeak": {"default": "comma"}}, "key": "FF0C"}, {"category": "Pd", "mappings": {"default": {"default": "fullwidth hyphen minus"}, "mathspeak": {"default": "hyphen-minus"}}, "key": "FF0D"}, {"category": "Po", "mappings": {"default": {"default": "fullwidth full stop", "alternative": "fullwidth period"}, "mathspeak": {"default": "period"}}, "key": "FF0E"}, {"category": "Po", "mappings": {"default": {"default": "fullwidth solidus", "alternative": "fullwidth slash"}, "mathspeak": {"default": "slash"}}, "key": "FF0F"}, {"category": "Po", "mappings": {"default": {"default": "fullwidth colon"}, "mathspeak": {"default": "colon"}}, "key": "FF1A"}, {"category": "Po", "mappings": {"default": {"default": "fullwidth semicolon"}, "mathspeak": {"default": "semicolon"}}, "key": "FF1B"}, {"category": "Sm", "mappings": {"default": {"default": "fullwidth less than sign"}, "mathspeak": {"default": "less-than-sign"}}, "key": "FF1C"}, {"category": "Sm", "mappings": {"default": {"default": "fullwidth equals sign"}, "mathspeak": {"default": "equals"}}, "key": "FF1D"}, {"category": "Sm", "mappings": {"default": {"default": "fullwidth greater than sign"}, "mathspeak": {"default": "greater-than-sign"}}, "key": "FF1E"}, {"category": "Po", "mappings": {"default": {"default": "fullwidth question mark"}, "mathspeak": {"default": "question-mark"}}, "key": "FF1F"}, {"category": "Po", "mappings": {"default": {"default": "fullwidth commercial at"}, "mathspeak": {"default": "commercial-at"}}, "key": "FF20"}, {"category": "Po", "mappings": {"default": {"default": "fullwidth reverse solidus", "alternative": "fullwidth backslash"}, "mathspeak": {"default": "backslash"}}, "key": "FF3C"}, {"category": "Sk", "mappings": {"default": {"default": "fullwidth circumflex accent", "alternative": "fullwidth spacing circumflex"}, "mathspeak": {"default": "caret"}}, "key": "FF3E"}, {"category": "Pc", "mappings": {"default": {"default": "fullwidth low line", "alternative": "fullwidth spacing underscore"}, "mathspeak": {"default": "bar"}}, "key": "FF3F"}, {"category": "Sk", "mappings": {"default": {"default": "fullwidth grave accent", "alternative": "fullwidth spacing grave"}, "mathspeak": {"default": "grave"}}, "key": "FF40"}, {"category": "Sm", "mappings": {"default": {"default": "fullwidth vertical line", "alternative": "fullwidth vertical bar"}, "mathspeak": {"default": "vertical-bar"}}, "key": "FF5C"}, {"category": "Sm", "mappings": {"default": {"default": "fullwidth tilde", "alternative": "fullwidth spacing tilde"}, "mathspeak": {"default": "tilde"}}, "key": "FF5E"}, {"category": "Sc", "mappings": {"default": {"default": "fullwidth cent sign"}, "mathspeak": {"default": "cent-sign"}}, "key": "FFE0"}, {"category": "Sc", "mappings": {"default": {"default": "fullwidth pound sign"}, "mathspeak": {"default": "pound-sign"}}, "key": "FFE1"}, {"category": "Sm", "mappings": {"default": {"default": "fullwidth not sign"}, "mathspeak": {"default": "not-sign"}}, "key": "FFE2"}, {"category": "Sk", "mappings": {"default": {"default": "fullwidth macron", "alternative": "fullwidth spacing macron"}, "mathspeak": {"default": "bar"}}, "key": "FFE3"}, {"category": "So", "mappings": {"default": {"default": "fullwidth broken bar", "alternative": "fullwidth broken vertical bar"}, "mathspeak": {"default": "broken-vertical-bar"}}, "key": "FFE4"}, {"category": "Sc", "mappings": {"default": {"default": "fullwidth yen sign"}, "mathspeak": {"default": "yen-sign"}}, "key": "FFE5"}, {"category": "Sc", "mappings": {"default": {"default": "fullwidth won sign"}, "mathspeak": {"default": "won-sign"}}, "key": "FFE6"}, {"category": "So", "mappings": {"default": {"default": "halfwidth forms light vertical"}, "mathspeak": {"default": "halfwidth-forms-light-vertical"}}, "key": "FFE8"}, {"category": "So", "mappings": {"default": {"default": "halfwidth black square"}, "mathspeak": {"default": "halfwidth-black-square"}}, "key": "FFED"}, {"category": "So", "mappings": {"default": {"default": "halfwidth white circle"}, "mathspeak": {"default": "halfwidth-white-circle"}}, "key": "FFEE"}]