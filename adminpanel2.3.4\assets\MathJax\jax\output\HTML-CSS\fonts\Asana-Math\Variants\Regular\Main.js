/*
 *  /MathJax/jax/output/HTML-CSS/fonts/Asana-Math/Variants/Regular/Main.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.OutputJax["HTML-CSS"].FONTDATA.FONTS.AsanaMathJax_Variants={directory:"Variants/Regular",family:"AsanaMathJax_Variants",testString:"\uE200\uE201\uE202\uE203\uE204\uE205\uE206\uE207\uE208\uE209\uE20A\uE20B\uE20C\uE20D\uE20E",32:[0,0,249,0,0],57856:[475,20,499,20,471],57857:[483,2,499,63,426],57858:[474,2,499,20,465],57859:[474,240,499,9,437],57860:[480,240,499,3,467],57861:[468,240,499,8,445],57862:[699,20,499,31,468],57863:[469,240,499,35,489],57864:[684,17,499,32,463],57865:[472,247,499,28,466],57866:[692,41,915,7,908],57867:[720,23,755,7,748],57868:[704,52,681,7,675],57869:[707,31,904,7,898],57870:[719,19,654,7,647],57871:[742,69,703,7,897],57872:[715,176,740,7,734],57873:[758,36,921,7,1018],57874:[734,26,683,7,677],57875:[714,157,815,-21,908],57876:[734,29,837,7,939],57877:[725,91,787,7,781],57878:[741,46,1136,7,1129],57879:[720,40,864,7,959],57880:[753,26,739,7,733],57881:[714,39,745,7,746],57882:[753,59,739,7,733],57883:[727,23,715,7,722],57884:[738,29,714,7,707],57885:[717,29,713,7,875],57886:[731,34,943,7,987],57887:[712,39,938,7,955],57888:[734,25,1264,7,1292],57889:[729,31,776,7,769],57890:[759,72,838,7,855],57891:[743,116,910,7,903],57892:[692,41,996,16,980],57893:[720,23,847,18,822],57894:[704,52,635,26,694],57895:[707,31,975,17,949],57896:[719,19,677,13,663],57897:[742,69,760,13,902],57898:[715,176,807,26,795],57899:[761,35,1010,20,1112],57900:[734,26,614,19,731],57901:[714,157,833,11,982],57902:[734,27,887,5,974],57903:[725,91,841,13,828],57904:[741,46,1265,13,1240],57905:[720,40,924,13,1027],57906:[753,26,819,26,794],57907:[714,39,825,17,812],57908:[753,59,815,26,794],57909:[727,8,785,18,778],57910:[738,29,773,26,747],57911:[717,29,693,18,927],57912:[731,34,1028,15,1079],57913:[711,39,968,17,1027],57914:[732,32,1318,5,1382],57915:[761,41,796,11,778],57916:[759,72,814,23,913],57917:[747,112,962,9,948]};MathJax.Callback.Queue(["initFont",MathJax.OutputJax["HTML-CSS"],"AsanaMathJax_Variants"],["loadComplete",MathJax.Ajax,MathJax.OutputJax["HTML-CSS"].fontDir+"/Variants/Regular/Main.js"]);
