/*
 *  /MathJax/jax/output/HTML-CSS/fonts/Asana-Math/Fraktur/Regular/Main.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.OutputJax["HTML-CSS"].FONTDATA.FONTS.AsanaMathJax_Fraktur={directory:"Fraktur/Regular",family:"AsanaMathJax_Fraktur",testString:"\u210C\u2128\u212D\uD835\uDD04\uD835\uDD05\uD835\uDD07\uD835\uDD08\uD835\uDD09\uD835\uDD0A\uD835\uDD0D\uD835\uDD0E\uD835\uDD0F\uD835\uDD10\uD835\uDD11\uD835\uDD12",32:[0,0,249,0,0],8460:[719,166,697,29,657],8488:[709,171,697,-7,608],8493:[719,4,645,53,629],120068:[721,4,697,20,675],120069:[720,7,801,60,747],120071:[708,4,801,69,746],120072:[719,4,645,54,629],120073:[715,157,697,74,663],120074:[721,4,801,88,740],120077:[719,162,645,-1,586],120078:[716,4,697,2,659],120079:[719,4,645,37,603],120080:[714,4,957,11,936],120081:[716,6,748,16,716],120082:[707,4,801,42,754],120083:[721,163,801,37,715],120084:[706,4,801,41,800],120086:[706,4,801,103,757],120087:[707,4,697,42,688],120088:[720,4,697,49,683],120089:[714,4,801,48,705],120090:[713,-2,957,25,931],120091:[719,4,645,29,629],120092:[719,165,748,19,641],120094:[504,6,478,67,469],120095:[683,9,478,23,436],120096:[500,4,374,85,356],120097:[696,4,478,54,447],120098:[503,5,426,78,392],120099:[719,162,322,27,293],120100:[505,163,478,54,443],120101:[696,165,478,25,438],120102:[703,4,270,32,258],120103:[705,169,270,32,229],120104:[702,4,322,21,308],120105:[696,5,270,42,265],120106:[499,4,801,24,774],120107:[499,4,530,16,518],120108:[502,4,478,69,447],120109:[505,161,530,68,496],120110:[499,168,478,66,455],120111:[504,4,374,17,362],120112:[500,6,426,56,409],120113:[696,6,322,19,293],120114:[501,4,530,25,513],120115:[496,4,478,28,434],120116:[501,4,748,46,708],120117:[503,4,426,31,402],120118:[505,163,530,36,465],120119:[505,165,374,39,344],120172:[719,9,748,54,726],120173:[715,7,748,52,723],120174:[718,8,697,77,667],120175:[715,8,697,51,668],120176:[719,8,697,63,684],120177:[719,167,645,37,633],120178:[718,9,801,76,756],120179:[718,167,748,33,709],120180:[718,11,645,29,611],120181:[719,167,645,16,609],120182:[718,14,748,14,732],120183:[718,11,593,32,556],120184:[719,15,968,16,952],120185:[719,11,801,53,785],120186:[718,7,697,69,681],120187:[719,167,748,47,749],120188:[717,11,759,52,748],120189:[719,11,801,49,782],120190:[719,5,697,62,672],120191:[716,8,645,71,632],120192:[718,12,697,32,676],120193:[718,9,748,43,746],120194:[713,4,968,38,968],120195:[718,6,645,32,642],120196:[718,167,748,49,705],120197:[717,167,655,20,601],120198:[537,9,499,63,489],120199:[709,17,520,43,472],120200:[540,7,364,61,354],120201:[717,8,530,52,481],120202:[541,11,416,49,411],120203:[718,166,374,43,348],120204:[536,167,478,43,466],120205:[718,166,520,37,474],120206:[719,11,312,22,302],120207:[718,168,322,35,289],120208:[718,8,374,52,345],120209:[716,9,312,52,304],120210:[537,9,822,27,800],120211:[539,7,541,2,542],120212:[549,8,478,40,455],120213:[544,167,551,36,505],120214:[549,167,488,54,458],120215:[545,8,416,41,414],120216:[542,4,468,60,429],120217:[704,11,322,23,317],120218:[543,11,530,24,529],120219:[536,4,520,28,477],120220:[546,6,748,32,709],120221:[537,8,426,21,417],120222:[536,166,478,25,447],120223:[541,168,374,36,345]};MathJax.Callback.Queue(["initFont",MathJax.OutputJax["HTML-CSS"],"AsanaMathJax_Fraktur"],["loadComplete",MathJax.Ajax,MathJax.OutputJax["HTML-CSS"].fontDir+"/Fraktur/Regular/Main.js"]);
