/*************************************************************
 *
 *  MathJax/fonts/HTML-CSS/TeX/png/SansSerif/Bold/BasicLatin.js
 *  
 *  Defines the image size data needed for the HTML-CSS OutputJax
 *  to display mathematics using fallback images when the fonts
 *  are not available to the client browser.
 *
 *  ---------------------------------------------------------------------
 *
 *  Copyright (c) 2009-2013 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 *
 */

MathJax.OutputJax["HTML-CSS"].defineImageData({
  "MathJax_SansSerif-bold": {
    0x20: [  // SPACE
      [1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],
      [1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0]
    ],
    0x21: [  // EXCLAMATION MARK
      [2,4,0],[3,6,0],[3,7,0],[4,9,0],[4,10,0],[5,12,0],[5,13,0],[6,16,0],
      [8,19,0],[9,23,0],[11,28,0],[12,32,0],[15,38,0],[17,45,0]
    ],
    0x22: [  // QUOTATION MARK
      [3,2,-2],[4,3,-3],[5,3,-4],[5,4,-5],[6,4,-6],[7,5,-7],[8,5,-8],[10,6,-10],
      [12,8,-11],[14,9,-14],[17,10,-18],[20,12,-20],[23,15,-23],[27,17,-28]
    ],
    0x23: [  // NUMBER SIGN
      [6,5,1],[8,8,2],[9,9,2],[11,11,2],[12,13,3],[15,15,3],[17,17,4],[20,21,5],
      [24,24,5],[29,30,7],[34,36,8],[40,41,9],[48,49,11],[57,58,13]
    ],
    0x24: [  // DOLLAR SIGN
      [4,6,1],[5,7,1],[5,9,1],[6,11,1],[7,12,1],[9,14,1],[10,16,1],[12,19,1],
      [14,23,2],[16,28,2],[19,33,2],[23,38,3],[27,45,3],[32,53,3]
    ],
    0x25: [  // PERCENT SIGN
      [7,6,1],[8,7,1],[10,9,1],[12,11,2],[14,14,2],[16,15,2],[19,15,1],[23,19,1],
      [27,22,2],[32,27,2],[38,33,2],[45,38,3],[54,44,3],[64,53,4]
    ],
    0x26: [  // AMPERSAND
      [6,4,0],[7,6,0],[8,7,0],[10,9,0],[11,10,0],[13,12,0],[15,13,0],[18,16,0],
      [22,19,0],[26,24,0],[31,29,0],[36,35,1],[43,41,1],[51,48,1]
    ],
    0x27: [  // APOSTROPHE
      [2,2,-2],[2,3,-3],[3,3,-4],[3,4,-5],[4,4,-6],[4,5,-7],[5,5,-8],[6,6,-10],
      [7,8,-11],[8,9,-14],[9,10,-18],[11,12,-20],[13,15,-23],[15,17,-28]
    ],
    0x28: [  // LEFT PARENTHESIS
      [3,7,2],[4,9,2],[4,11,3],[5,13,3],[6,15,4],[7,17,4],[8,19,5],[9,24,6],
      [11,28,7],[13,34,8],[15,41,10],[17,47,12],[21,55,14],[25,66,16]
    ],
    0x29: [  // RIGHT PARENTHESIS
      [3,7,2],[3,9,2],[4,11,3],[5,13,3],[5,15,4],[6,17,4],[7,20,5],[8,24,6],
      [10,28,7],[12,34,8],[14,41,10],[16,47,12],[20,55,14],[23,66,16]
    ],
    0x2A: [  // ASTERISK
      [4,3,-2],[4,4,-2],[5,5,-3],[6,6,-3],[7,7,-4],[8,8,-5],[10,9,-5],[12,11,-6],
      [14,13,-7],[16,16,-9],[19,19,-12],[23,22,-14],[27,26,-16],[32,30,-19]
    ],
    0x2B: [  // PLUS SIGN
      [6,5,1],[7,7,1],[8,8,1],[10,10,1],[11,11,2],[14,13,2],[16,14,2],[19,18,3],
      [22,21,3],[27,25,4],[32,30,5],[37,35,5],[44,41,6],[53,49,8]
    ],
    0x2C: [  // COMMA
      [2,2,1],[2,3,1],[3,4,2],[3,4,2],[4,5,2],[4,5,2],[5,5,2],[6,7,3],
      [7,8,3],[8,9,4],[9,10,4],[11,12,5],[13,15,6],[15,17,7]
    ],
    0x2D: [  // HYPHEN-MINUS
      [3,1,-1],[3,1,-1],[3,1,-2],[4,2,-2],[5,3,-2],[5,3,-2],[6,2,-4],[7,3,-4],
      [9,3,-5],[10,3,-6],[12,4,-7],[14,5,-9],[17,5,-10],[20,6,-12]
    ],
    0x2E: [  // FULL STOP
      [2,1,0],[2,2,0],[3,2,0],[3,2,0],[4,3,0],[4,3,0],[5,3,0],[6,4,0],
      [7,5,0],[8,5,0],[9,6,0],[11,7,0],[13,9,0],[15,10,0]
    ],
    0x2F: [  // SOLIDUS
      [4,7,2],[5,9,2],[5,11,3],[6,13,3],[7,15,4],[9,17,4],[10,19,5],[12,23,6],
      [14,28,7],[17,34,8],[20,40,10],[23,47,12],[28,55,14],[33,66,16]
    ],
    0x30: [  // DIGIT ZERO
      [4,4,0],[5,6,0],[5,7,0],[6,9,0],[7,10,0],[9,12,0],[10,13,0],[12,16,0],
      [14,19,0],[17,24,0],[20,29,0],[24,35,1],[28,42,2],[34,49,2]
    ],
    0x31: [  // DIGIT ONE
      [4,4,0],[4,6,0],[5,7,0],[6,9,0],[7,10,0],[8,12,0],[10,13,0],[11,16,0],
      [14,19,0],[16,24,0],[19,29,0],[22,34,0],[27,40,0],[32,47,0]
    ],
    0x32: [  // DIGIT TWO
      [4,4,0],[5,6,0],[5,7,0],[6,9,0],[7,10,0],[9,12,0],[10,13,0],[12,16,0],
      [14,19,0],[17,24,0],[20,28,0],[23,33,0],[28,39,0],[33,47,0]
    ],
    0x33: [  // DIGIT THREE
      [4,4,0],[5,6,0],[5,7,0],[6,9,0],[7,10,0],[9,12,0],[10,13,0],[12,16,0],
      [14,19,0],[17,24,0],[20,29,0],[24,34,0],[28,41,1],[34,49,1]
    ],
    0x34: [  // DIGIT FOUR
      [4,5,0],[5,7,0],[6,8,0],[6,10,0],[8,11,0],[9,13,0],[10,14,0],[12,17,0],
      [15,20,0],[17,24,0],[20,29,0],[24,33,0],[29,39,0],[34,47,1]
    ],
    0x35: [  // DIGIT FIVE
      [4,4,0],[5,6,0],[5,7,0],[6,9,0],[7,10,0],[9,12,0],[10,13,0],[12,16,0],
      [14,19,0],[17,23,0],[20,28,0],[23,33,1],[28,39,1],[33,47,2]
    ],
    0x36: [  // DIGIT SIX
      [4,4,0],[5,6,0],[5,7,0],[6,9,0],[7,10,0],[9,12,0],[10,13,0],[12,16,0],
      [14,19,0],[17,24,0],[20,29,0],[24,34,0],[28,41,1],[34,48,1]
    ],
    0x37: [  // DIGIT SEVEN
      [4,4,0],[5,6,0],[5,7,0],[6,9,0],[7,10,0],[9,12,0],[10,13,0],[12,16,0],
      [14,19,0],[17,23,0],[20,28,0],[24,32,0],[28,39,1],[33,46,1]
    ],
    0x38: [  // DIGIT EIGHT
      [4,4,0],[5,6,0],[5,7,0],[6,9,0],[7,10,0],[9,12,0],[10,13,0],[12,16,0],
      [14,19,0],[17,24,0],[20,29,0],[24,35,1],[28,41,1],[34,48,1]
    ],
    0x39: [  // DIGIT NINE
      [4,4,0],[5,6,0],[5,7,0],[6,9,0],[7,10,0],[9,12,0],[10,13,0],[12,16,0],
      [14,19,0],[17,24,0],[20,29,0],[24,35,1],[28,41,1],[34,48,1]
    ],
    0x3A: [  // COLON
      [2,3,0],[2,4,0],[3,5,0],[3,6,0],[4,7,0],[4,8,0],[5,9,0],[6,11,0],
      [7,13,0],[8,16,0],[9,18,0],[11,21,0],[13,25,0],[15,30,0]
    ],
    0x3B: [  // SEMICOLON
      [2,4,1],[2,5,1],[3,7,2],[3,8,2],[4,9,2],[4,10,2],[5,11,2],[6,14,3],
      [7,16,3],[8,20,4],[9,22,4],[11,26,5],[13,31,6],[15,37,7]
    ],
    0x3D: [  // EQUALS SIGN
      [6,2,0],[7,3,-1],[8,3,-1],[10,5,0],[11,6,-1],[14,7,-1],[16,6,-2],[19,7,-2],
      [22,9,-3],[27,11,-3],[32,13,-4],[37,15,-4],[44,17,-5],[53,21,-6]
    ],
    0x3F: [  // QUESTION MARK
      [4,4,0],[4,6,0],[5,7,0],[6,9,0],[7,10,0],[8,12,0],[9,13,0],[11,16,0],
      [13,19,0],[15,24,0],[18,28,0],[21,33,0],[25,39,0],[30,46,0]
    ],
    0x40: [  // COMMERCIAL AT
      [5,4,0],[6,6,0],[7,7,0],[8,9,0],[10,10,0],[12,12,0],[14,13,0],[16,16,0],
      [19,19,0],[23,24,0],[27,28,0],[32,33,0],[38,40,1],[45,46,1]
    ],
    0x41: [  // LATIN CAPITAL LETTER A
      [5,5,1],[6,7,1],[7,8,1],[9,10,1],[10,11,1],[12,13,1],[14,14,1],[16,17,1],
      [19,20,1],[23,24,1],[27,29,1],[32,33,1],[38,39,1],[45,46,1]
    ],
    0x42: [  // LATIN CAPITAL LETTER B
      [5,4,0],[6,6,0],[7,7,0],[8,9,0],[10,10,0],[12,12,0],[14,13,0],[16,16,0],
      [19,19,0],[23,23,0],[27,28,0],[32,32,0],[38,38,0],[45,45,0]
    ],
    0x43: [  // LATIN CAPITAL LETTER C
      [5,4,0],[6,6,0],[7,7,0],[8,9,0],[9,10,0],[11,12,0],[13,13,0],[16,16,0],
      [18,19,0],[22,24,0],[26,28,0],[31,33,0],[36,40,1],[43,46,1]
    ],
    0x44: [  // LATIN CAPITAL LETTER D
      [6,4,0],[7,7,1],[8,8,1],[9,10,1],[11,11,1],[13,13,1],[15,14,1],[17,17,1],
      [21,20,1],[24,24,1],[29,29,1],[34,33,1],[41,39,1],[48,46,1]
    ],
    0x45: [  // LATIN CAPITAL LETTER E
      [5,4,0],[5,6,0],[6,7,0],[8,9,0],[9,10,0],[10,12,0],[12,13,0],[14,16,0],
      [17,19,0],[20,23,0],[24,28,0],[28,32,0],[34,38,0],[40,45,0]
    ],
    0x46: [  // LATIN CAPITAL LETTER F
      [4,4,0],[5,6,0],[6,7,0],[7,9,0],[8,10,0],[10,12,0],[11,13,0],[14,16,0],
      [16,19,0],[19,23,0],[23,28,0],[27,32,0],[32,38,0],[38,45,0]
    ],
    0x47: [  // LATIN CAPITAL LETTER G
      [5,4,0],[6,6,0],[7,7,0],[8,9,0],[10,10,0],[11,12,0],[13,13,0],[16,16,0],
      [19,19,0],[22,24,0],[26,28,0],[31,33,0],[37,40,1],[44,47,1]
    ],
    0x48: [  // LATIN CAPITAL LETTER H
      [5,4,0],[6,6,0],[7,7,0],[9,9,0],[10,10,0],[12,12,0],[14,13,0],[17,16,0],
      [20,19,0],[23,23,0],[28,28,0],[33,32,0],[39,38,0],[46,45,0]
    ],
    0x49: [  // LATIN CAPITAL LETTER I
      [2,4,0],[3,6,0],[3,7,0],[3,9,0],[4,10,0],[5,12,0],[5,13,0],[6,16,0],
      [7,19,0],[8,23,0],[10,28,0],[12,32,0],[14,38,0],[16,45,0]
    ],
    0x4A: [  // LATIN CAPITAL LETTER J
      [3,4,0],[4,6,0],[5,7,0],[5,9,0],[6,10,0],[8,12,0],[9,13,0],[10,16,0],
      [12,19,0],[15,23,0],[17,28,0],[20,34,2],[24,40,2],[29,47,2]
    ],
    0x4B: [  // LATIN CAPITAL LETTER K
      [5,4,0],[6,6,0],[7,7,0],[9,9,0],[10,10,0],[12,12,0],[14,13,0],[17,16,0],
      [20,19,0],[23,23,0],[28,28,0],[33,32,0],[39,38,0],[46,45,0]
    ],
    0x4C: [  // LATIN CAPITAL LETTER L
      [4,4,0],[5,6,0],[6,7,0],[7,9,0],[8,10,0],[9,12,0],[11,13,0],[13,16,0],
      [15,19,0],[18,23,0],[21,28,0],[25,32,0],[30,38,0],[36,45,0]
    ],
    0x4D: [  // LATIN CAPITAL LETTER M
      [7,5,0],[8,6,0],[9,8,0],[11,9,0],[13,11,0],[15,12,0],[17,13,0],[21,16,0],
      [25,19,0],[29,24,0],[35,28,0],[41,33,0],[49,38,0],[58,46,0]
    ],
    0x4E: [  // LATIN CAPITAL LETTER N
      [5,4,0],[6,6,0],[7,7,0],[9,9,0],[10,10,0],[12,12,0],[14,13,0],[17,16,0],
      [20,19,0],[23,23,0],[28,28,0],[33,32,0],[39,38,0],[46,45,0]
    ],
    0x4F: [  // LATIN CAPITAL LETTER O
      [6,4,0],[7,6,0],[8,7,0],[9,9,0],[11,10,0],[13,12,0],[15,13,0],[17,16,0],
      [21,19,0],[25,24,0],[29,29,0],[34,35,1],[41,41,1],[49,49,1]
    ],
    0x50: [  // LATIN CAPITAL LETTER P
      [5,5,0],[6,6,0],[7,8,0],[8,9,0],[9,11,0],[11,12,0],[13,13,0],[15,16,0],
      [18,19,0],[22,24,0],[26,28,0],[30,32,0],[36,38,0],[43,46,0]
    ],
    0x51: [  // LATIN CAPITAL LETTER Q
      [6,5,1],[7,7,1],[8,8,1],[9,10,1],[11,11,1],[13,14,2],[15,15,2],[17,18,2],
      [21,22,3],[25,27,3],[29,33,4],[34,39,5],[41,46,6],[49,54,7]
    ],
    0x52: [  // LATIN CAPITAL LETTER R
      [5,5,1],[6,7,1],[7,8,1],[8,10,1],[10,11,1],[11,13,1],[13,14,1],[16,17,1],
      [19,20,1],[22,24,1],[26,29,1],[31,33,1],[37,39,1],[44,46,1]
    ],
    0x53: [  // LATIN CAPITAL LETTER S
      [4,4,0],[5,6,0],[6,7,0],[7,9,0],[8,10,0],[10,12,0],[11,13,0],[13,16,0],
      [16,19,0],[19,24,0],[22,29,0],[26,35,1],[31,41,1],[37,48,1]
    ],
    0x54: [  // LATIN CAPITAL LETTER T
      [5,5,0],[6,7,0],[7,8,0],[9,10,0],[10,11,0],[12,13,0],[14,14,0],[16,17,0],
      [20,20,0],[23,24,0],[27,29,0],[32,33,0],[39,38,0],[46,46,0]
    ],
    0x55: [  // LATIN CAPITAL LETTER U
      [5,4,0],[6,6,0],[7,7,0],[8,9,0],[10,10,0],[12,12,0],[14,13,0],[16,16,0],
      [19,19,0],[23,23,0],[27,28,0],[32,33,1],[38,40,2],[45,47,2]
    ],
    0x56: [  // LATIN CAPITAL LETTER V
      [5,4,0],[6,6,0],[7,7,0],[9,9,0],[10,10,0],[12,12,0],[14,13,0],[17,16,0],
      [20,19,0],[24,23,0],[28,28,0],[33,32,0],[39,38,0],[47,45,0]
    ],
    0x57: [  // LATIN CAPITAL LETTER W
      [7,4,0],[9,6,0],[10,7,0],[12,9,0],[14,10,0],[17,12,0],[20,13,0],[24,16,0],
      [28,19,0],[34,23,0],[40,28,0],[48,32,0],[56,38,0],[67,45,0]
    ],
    0x58: [  // LATIN CAPITAL LETTER X
      [5,4,0],[6,7,1],[7,8,1],[9,10,1],[10,11,1],[12,13,1],[14,14,1],[17,17,1],
      [20,20,1],[23,24,1],[28,29,1],[33,33,1],[39,39,1],[46,46,1]
    ],
    0x59: [  // LATIN CAPITAL LETTER Y
      [5,5,0],[6,7,0],[7,8,0],[9,10,0],[10,11,0],[12,13,0],[14,14,0],[17,17,0],
      [20,20,0],[24,24,0],[28,29,0],[33,33,0],[40,39,0],[47,46,0]
    ],
    0x5A: [  // LATIN CAPITAL LETTER Z
      [5,4,0],[6,6,0],[7,7,0],[8,9,0],[9,10,0],[11,12,0],[12,13,0],[15,16,0],
      [17,19,0],[21,23,0],[25,28,0],[29,32,0],[34,38,0],[41,45,0]
    ],
    0x5B: [  // LEFT SQUARE BRACKET
      [3,7,2],[3,9,2],[4,11,3],[4,14,4],[5,17,4],[6,18,5],[7,19,5],[8,23,6],
      [9,28,7],[11,34,8],[13,40,10],[15,47,12],[18,55,14],[21,66,16]
    ],
    0x5D: [  // RIGHT SQUARE BRACKET
      [2,7,2],[3,9,2],[3,11,3],[3,14,4],[4,17,4],[5,18,5],[5,19,5],[6,24,6],
      [8,28,7],[9,34,8],[10,40,10],[12,47,12],[15,55,14],[17,66,16]
    ],
    0x5E: [  // CIRCUMFLEX ACCENT
      [4,1,-3],[4,2,-4],[5,2,-5],[6,2,-7],[7,3,-7],[8,3,-9],[9,3,-10],[11,4,-12],
      [13,5,-14],[15,6,-17],[18,7,-21],[21,8,-24],[25,9,-29],[30,11,-34]
    ],
    0x5F: [  // LOW LINE
      [4,2,1],[5,2,1],[6,2,1],[7,3,2],[8,3,2],[10,3,2],[11,3,2],[13,4,3],
      [16,4,3],[19,4,3],[22,5,4],[26,6,5],[31,7,6],[37,8,7]
    ],
    0x61: [  // LATIN SMALL LETTER A
      [4,3,0],[4,4,0],[5,5,0],[6,6,0],[7,7,0],[8,8,0],[10,9,0],[11,11,0],
      [13,13,0],[16,16,0],[19,19,0],[22,22,0],[26,27,1],[31,32,1]
    ],
    0x62: [  // LATIN SMALL LETTER B
      [4,4,0],[5,6,0],[6,7,0],[7,9,0],[8,10,0],[9,12,0],[11,13,0],[13,16,0],
      [15,19,0],[18,23,0],[21,28,0],[25,32,0],[29,39,1],[35,46,1]
    ],
    0x63: [  // LATIN SMALL LETTER C
      [4,3,0],[4,4,0],[5,5,0],[6,6,0],[7,7,0],[8,8,0],[9,9,0],[11,11,0],
      [13,13,0],[16,16,0],[18,19,0],[22,22,0],[26,27,1],[31,32,1]
    ],
    0x64: [  // LATIN SMALL LETTER D
      [4,4,0],[5,6,0],[5,7,0],[6,9,0],[7,10,0],[9,12,0],[10,13,0],[12,16,0],
      [14,19,0],[17,23,0],[20,28,0],[24,32,0],[28,39,1],[33,46,1]
    ],
    0x65: [  // LATIN SMALL LETTER E
      [4,3,0],[4,4,0],[5,5,0],[6,6,0],[7,7,0],[8,8,0],[10,9,0],[12,11,0],
      [14,13,0],[16,16,0],[19,19,0],[23,22,0],[27,27,1],[32,32,1]
    ],
    0x66: [  // LATIN SMALL LETTER F
      [3,4,0],[4,6,0],[4,7,0],[5,9,0],[6,10,0],[7,12,0],[8,13,0],[9,16,0],
      [11,19,0],[13,24,0],[15,28,0],[18,33,0],[21,39,0],[25,46,1]
    ],
    0x67: [  // LATIN SMALL LETTER G
      [4,4,1],[5,6,2],[6,7,2],[7,8,2],[8,10,3],[9,11,3],[11,13,4],[13,16,5],
      [15,18,5],[18,23,7],[21,27,8],[25,31,9],[30,38,12],[35,45,14]
    ],
    0x68: [  // LATIN SMALL LETTER H
      [4,4,0],[5,6,0],[5,7,0],[6,9,0],[7,10,0],[9,12,0],[10,13,0],[12,16,0],
      [14,19,0],[17,23,0],[20,28,0],[24,32,0],[28,38,0],[33,45,0]
    ],
    0x69: [  // LATIN SMALL LETTER I
      [2,4,0],[2,6,0],[3,7,0],[3,9,0],[3,10,0],[4,12,0],[5,13,0],[5,16,0],
      [6,19,0],[7,23,0],[9,28,0],[10,32,0],[12,38,0],[14,45,0]
    ],
    0x6A: [  // LATIN SMALL LETTER J
      [3,5,1],[3,8,2],[4,9,2],[4,11,2],[5,13,3],[6,15,3],[7,17,4],[8,21,5],
      [9,24,5],[11,30,7],[13,36,8],[15,41,9],[17,50,12],[21,59,14]
    ],
    0x6B: [  // LATIN SMALL LETTER K
      [4,4,0],[5,6,0],[5,7,0],[6,9,0],[7,10,0],[9,12,0],[10,13,0],[12,16,0],
      [14,19,0],[17,23,0],[20,28,0],[23,32,0],[28,38,0],[33,45,0]
    ],
    0x6C: [  // LATIN SMALL LETTER L
      [2,4,0],[2,6,0],[2,7,0],[3,9,0],[3,10,0],[4,12,0],[4,13,0],[5,16,0],
      [6,19,0],[7,23,0],[8,28,0],[10,32,0],[11,38,0],[14,45,0]
    ],
    0x6D: [  // LATIN SMALL LETTER M
      [6,3,0],[7,4,0],[8,5,0],[10,6,0],[12,7,0],[14,8,0],[16,9,0],[19,11,0],
      [23,13,0],[27,16,0],[32,19,0],[38,22,0],[45,26,0],[54,31,0]
    ],
    0x6E: [  // LATIN SMALL LETTER N
      [4,3,0],[5,4,0],[5,5,0],[6,6,0],[7,7,0],[9,8,0],[10,9,0],[12,11,0],
      [14,13,0],[17,16,0],[20,19,0],[24,22,0],[28,26,0],[34,31,0]
    ],
    0x6F: [  // LATIN SMALL LETTER O
      [4,3,0],[5,4,0],[6,5,0],[7,6,0],[8,7,0],[9,8,0],[11,9,0],[13,11,0],
      [15,13,0],[18,16,0],[21,19,0],[25,22,0],[29,27,1],[35,32,1]
    ],
    0x70: [  // LATIN SMALL LETTER P
      [4,4,1],[5,6,2],[6,7,2],[7,8,2],[8,10,3],[9,11,3],[11,13,4],[13,16,5],
      [15,18,5],[18,23,7],[21,27,8],[25,31,9],[29,37,11],[35,43,13]
    ],
    0x71: [  // LATIN SMALL LETTER Q
      [4,4,1],[5,6,2],[5,7,2],[6,8,2],[7,10,3],[9,11,3],[10,13,4],[12,16,5],
      [14,18,5],[17,23,7],[20,27,8],[24,31,9],[28,37,11],[33,44,13]
    ],
    0x72: [  // LATIN SMALL LETTER R
      [3,3,0],[3,4,0],[4,5,0],[5,6,0],[5,7,0],[6,8,0],[7,9,0],[9,11,0],
      [10,13,0],[12,16,0],[14,19,0],[17,22,0],[20,26,0],[24,31,0]
    ],
    0x73: [  // LATIN SMALL LETTER S
      [3,3,0],[4,4,0],[4,5,0],[5,6,0],[6,7,0],[7,8,0],[8,9,0],[10,11,0],
      [11,13,0],[14,16,0],[16,19,0],[19,22,0],[22,27,1],[27,32,1]
    ],
    0x74: [  // LATIN SMALL LETTER T
      [3,4,0],[4,5,0],[4,7,0],[5,8,0],[6,9,0],[7,10,0],[8,12,0],[9,14,0],
      [11,17,0],[13,20,0],[15,24,0],[18,28,0],[21,33,0],[25,39,0]
    ],
    0x75: [  // LATIN SMALL LETTER U
      [4,3,0],[5,4,0],[5,5,0],[6,6,0],[7,7,0],[9,8,0],[10,9,0],[12,11,0],
      [14,13,0],[17,16,0],[20,19,0],[24,22,0],[28,27,1],[33,31,1]
    ],
    0x76: [  // LATIN SMALL LETTER V
      [4,3,0],[4,4,0],[5,5,0],[6,6,0],[7,7,0],[8,8,0],[10,9,0],[11,11,0],
      [14,13,0],[16,16,0],[19,19,0],[22,22,0],[27,25,0],[32,30,0]
    ],
    0x77: [  // LATIN SMALL LETTER W
      [5,3,0],[6,4,0],[7,5,0],[9,6,0],[10,7,0],[12,8,0],[14,9,0],[17,11,0],
      [20,13,0],[24,16,0],[28,19,0],[33,22,0],[40,25,0],[47,30,0]
    ],
    0x78: [  // LATIN SMALL LETTER X
      [4,3,0],[4,4,0],[5,5,0],[6,6,0],[7,7,0],[8,8,0],[10,9,0],[11,11,0],
      [14,13,0],[16,16,0],[19,19,0],[22,22,0],[27,26,0],[31,31,0]
    ],
    0x79: [  // LATIN SMALL LETTER Y
      [4,5,1],[4,7,2],[5,8,2],[6,9,2],[7,11,3],[8,12,3],[10,14,4],[12,17,5],
      [14,19,5],[16,23,7],[19,27,8],[23,32,9],[27,38,12],[32,45,14]
    ],
    0x7A: [  // LATIN SMALL LETTER Z
      [4,3,0],[4,4,0],[5,5,0],[6,6,0],[7,7,0],[8,8,0],[9,9,0],[11,11,0],
      [13,13,0],[15,16,0],[18,19,0],[21,21,0],[25,25,0],[29,30,0]
    ],
    0x7E: [  // TILDE
      [4,1,-1],[4,2,-2],[5,2,-2],[6,3,-2],[7,4,-2],[8,4,-3],[9,3,-4],[11,4,-5],
      [13,4,-5],[15,5,-7],[18,6,-8],[22,7,-9],[26,8,-11],[30,10,-13]
    ]
  }
});

MathJax.Ajax.loadComplete(MathJax.OutputJax["HTML-CSS"].imgDir+"/SansSerif/Bold"+
                          MathJax.OutputJax["HTML-CSS"].imgPacked+"/BasicLatin.js");
