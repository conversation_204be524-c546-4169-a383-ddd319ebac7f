<?php
/**
 * Elite Quiz Local Development Configuration Script
 * This script will configure the admin panel for local development
 */

// Database configuration for local development
$local_db_config = '<?php
defined(\'BASEPATH\') OR exit(\'No direct script access allowed\');

$active_group = \'default\';
$query_builder = TRUE;

$db[\'default\'] = array(
    \'dsn\' => \'\',
    \'hostname\' => \'localhost\',
    \'username\' => \'root\',
    \'password\' => \'\',  // Usually empty for XAMPP
    \'database\' => \'elite_quiz_local\',
    \'dbdriver\' => \'mysqli\',
    \'dbprefix\' => \'\',
    \'pconnect\' => FALSE,
    \'db_debug\' => (ENVIRONMENT !== \'production\'),
    \'cache_on\' => FALSE,
    \'cachedir\' => \'\',
    \'char_set\' => \'utf8mb4\',
    \'dbcollat\' => \'utf8mb4_unicode_ci\',
    \'swap_pre\' => \'\',
    \'encrypt\' => FALSE,
    \'compress\' => FALSE,
    \'stricton\' => FALSE,
    \'failover\' => array(),
    \'save_queries\' => TRUE
);
';

// Config.php configuration for local development
$local_config = '<?php
defined(\'BASEPATH\') or exit(\'No direct script access allowed\');

// Base URL for local development
$config[\'base_url\'] = \'http://localhost/elite_quiz_admin/\';

$config[\'index_page\'] = \'\';
$config[\'uri_protocol\'] = \'REQUEST_URI\';
$config[\'url_suffix\'] = \'\';
$config[\'language\'] = \'english\';
$config[\'charset\'] = \'UTF-8\';
$config[\'enable_hooks\'] = FALSE;
$config[\'subclass_prefix\'] = \'MY_\';
$config[\'composer_autoload\'] = FALSE;
$config[\'permitted_uri_chars\'] = \'a-z 0-9~%.:_\-\';
$config[\'enable_query_strings\'] = FALSE;
$config[\'controller_trigger\'] = \'c\';
$config[\'function_trigger\'] = \'m\';
$config[\'directory_trigger\'] = \'d\';
$config[\'allow_get_array\'] = TRUE;
$config[\'log_threshold\'] = 0;
$config[\'log_path\'] = \'\';
$config[\'log_file_extension\'] = \'\';
$config[\'log_file_permissions\'] = 0644;
$config[\'log_date_format\'] = \'Y-m-d H:i:s\';
$config[\'error_views_path\'] = \'\';
$config[\'cache_path\'] = \'\';
$config[\'cache_query_string\'] = FALSE;
$config[\'encryption_key\'] = \'elite_quiz_local_dev_key_2024\';
$config[\'sess_driver\'] = \'files\';
$config[\'sess_cookie_name\'] = \'ci_session\';
$config[\'sess_expiration\'] = 7200;
$config[\'sess_save_path\'] = NULL;
$config[\'sess_match_ip\'] = FALSE;
$config[\'sess_time_to_update\'] = 300;
$config[\'sess_regenerate_destroy\'] = FALSE;
$config[\'cookie_prefix\'] = \'\';
$config[\'cookie_domain\'] = \'\';
$config[\'cookie_path\'] = \'/\';
$config[\'cookie_secure\'] = FALSE;
$config[\'cookie_httponly\'] = FALSE;
$config[\'standardize_newlines\'] = FALSE;
$config[\'global_xss_filtering\'] = FALSE;
$config[\'csrf_protection\'] = FALSE;
$config[\'csrf_token_name\'] = \'csrf_test_name\';
$config[\'csrf_cookie_name\'] = \'csrf_cookie_name\';
$config[\'csrf_expire\'] = 7200;
$config[\'csrf_regenerate\'] = TRUE;
$config[\'csrf_exclude_uris\'] = array();
$config[\'compress_output\'] = FALSE;
$config[\'time_reference\'] = \'local\';
$config[\'rewrite_short_tags\'] = FALSE;
$config[\'proxy_ips\'] = \'\';

// CORS headers for API access
header(\'Access-Control-Allow-Origin: *\');
header(\'Access-Control-Allow-Methods: GET, POST, OPTIONS\');
header(\'Access-Control-Allow-Headers: Content-Type, Authorization\');
';

// Function to setup local configuration
function setupLocalConfig() {
    global $local_db_config, $local_config;
    
    $config_dir = 'adminpanel2.3.4/application/config/';
    
    // Backup original files
    if (file_exists($config_dir . 'database.php')) {
        copy($config_dir . 'database.php', $config_dir . 'database.php.backup');
    }
    if (file_exists($config_dir . 'config.php')) {
        copy($config_dir . 'config.php', $config_dir . 'config.php.backup');
    }
    
    // Write new configuration files
    file_put_contents($config_dir . 'database.php', $local_db_config);
    file_put_contents($config_dir . 'config.php', $local_config);
    
    echo "✅ Local configuration files created successfully!\n";
    echo "📁 Database config: {$config_dir}database.php\n";
    echo "📁 Main config: {$config_dir}config.php\n";
    echo "💾 Original files backed up with .backup extension\n";
}

// Function to create .htaccess for URL rewriting
function createHtaccess() {
    $htaccess_content = 'RewriteEngine On
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule ^(.*)$ index.php/$1 [L]

# Security headers
Header always set X-Content-Type-Options nosniff
Header always set X-Frame-Options DENY
Header always set X-XSS-Protection "1; mode=block"

# CORS headers for API
Header always set Access-Control-Allow-Origin "*"
Header always set Access-Control-Allow-Methods "GET, POST, OPTIONS"
Header always set Access-Control-Allow-Headers "Content-Type, Authorization"

# Handle preflight requests
RewriteCond %{REQUEST_METHOD} OPTIONS
RewriteRule ^(.*)$ $1 [R=200,L]
';
    
    file_put_contents('adminpanel2.3.4/.htaccess', $htaccess_content);
    echo "✅ .htaccess file created for URL rewriting and CORS\n";
}

// Function to create local environment file
function createLocalEnv() {
    $env_content = '# Elite Quiz Local Development Environment
# Copy this to .env and update values as needed

# Database Configuration
DB_HOST=localhost
DB_USERNAME=root
DB_PASSWORD=
DB_NAME=elite_quiz_local

# Application Configuration
APP_ENV=development
APP_DEBUG=true
APP_URL=http://localhost/elite_quiz_admin

# Firebase Configuration (get from Firebase Console)
FIREBASE_API_KEY=your_firebase_api_key
FIREBASE_AUTH_DOMAIN=your_project.firebaseapp.com
FIREBASE_PROJECT_ID=your_project_id
FIREBASE_STORAGE_BUCKET=your_project.appspot.com
FIREBASE_MESSAGING_SENDER_ID=your_sender_id
FIREBASE_APP_ID=your_app_id

# FCM Server Key (for push notifications)
FCM_SERVER_KEY=your_fcm_server_key

# Admin Credentials
ADMIN_USERNAME=admin
ADMIN_PASSWORD=admin123

# Security
ENCRYPTION_KEY=elite_quiz_local_dev_key_2024
SESSION_EXPIRE=7200

# File Upload Settings
MAX_FILE_SIZE=5MB
ALLOWED_EXTENSIONS=jpg,jpeg,png,gif,svg

# API Settings
API_RATE_LIMIT=1000
API_TIMEOUT=30
';
    
    file_put_contents('.env.example', $env_content);
    echo "✅ Environment example file created: .env.example\n";
    echo "📝 Copy this to .env and update with your actual values\n";
}

// Main setup function
function runLocalSetup() {
    echo "🚀 Setting up Elite Quiz for Local Development\n";
    echo "=" . str_repeat("=", 50) . "\n\n";
    
    // Check if admin panel directory exists
    if (!is_dir('adminpanel2.3.4')) {
        echo "❌ Error: adminpanel2.3.4 directory not found!\n";
        echo "Please make sure you're running this script from the project root.\n";
        return;
    }
    
    // Setup configuration files
    setupLocalConfig();
    
    // Create .htaccess
    createHtaccess();
    
    // Create environment file
    createLocalEnv();
    
    echo "\n" . str_repeat("=", 60) . "\n";
    echo "🎉 Local setup completed successfully!\n\n";
    
    echo "📋 Next Steps:\n";
    echo "1. Start XAMPP and ensure Apache + MySQL are running\n";
    echo "2. Create database 'elite_quiz_local' in phpMyAdmin\n";
    echo "3. Import database schema from adminpanel2.3.4/install/assets/quiz.php\n";
    echo "4. Copy admin panel to XAMPP htdocs: cp -r adminpanel2.3.4 C:/xampp/htdocs/elite_quiz_admin\n";
    echo "5. Access admin panel: http://localhost/elite_quiz_admin\n";
    echo "6. Login with: admin / admin123\n";
    echo "7. Configure Flutter app to use local server\n\n";
    
    echo "🔧 Configuration Details:\n";
    echo "- Database: elite_quiz_local\n";
    echo "- Admin URL: http://localhost/elite_quiz_admin\n";
    echo "- API Base: http://localhost/elite_quiz_admin/Api\n";
    echo "- Default Admin: admin / admin123\n\n";
    
    echo "⚠️  Important Notes:\n";
    echo "- Original config files are backed up with .backup extension\n";
    echo "- Update Firebase configuration in Flutter app\n";
    echo "- Use ******** for Android emulator API calls\n";
    echo "- Use localhost for iOS simulator API calls\n";
    echo "- Use your computer's IP for physical device testing\n";
}

// Run the setup if script is executed directly
if (php_sapi_name() === 'cli') {
    runLocalSetup();
} else {
    echo "<pre>";
    runLocalSetup();
    echo "</pre>";
}
?>
