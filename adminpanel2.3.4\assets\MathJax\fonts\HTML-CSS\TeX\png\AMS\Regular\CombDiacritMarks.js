/*
 *  /MathJax/fonts/HTML-CSS/TeX/png/AMS/Regular/CombDiacritMarks.js
 *  
 *  Copyright (c) 2010-2013 The MathJax Consortium
 *
 *  Part of the MathJax library.
 *  See http://www.mathjax.org for details.
 * 
 *  Licensed under the Apache License, Version 2.0;
 *  you may not use this file except in compliance with the License.
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 */

MathJax.OutputJax["HTML-CSS"].defineImageData({MathJax_AMS:{770:[[18,3,-3],[21,3,-4],[25,3,-5],[29,4,-6],[34,5,-8],[40,5,-9],[47,6,-11],[56,8,-12],[66,8,-16],[79,10,-19],[93,12,-21],[111,14,-26],[131,17,-30],[156,20,-36]],771:[[17,2,-4],[20,2,-5],[23,4,-5],[28,5,-7],[33,4,-9],[39,5,-10],[46,6,-12],[55,6,-15],[65,8,-17],[77,9,-21],[92,11,-25],[109,13,-29],[129,15,-35],[154,18,-41]]}});MathJax.Ajax.loadComplete(MathJax.OutputJax["HTML-CSS"].imgDir+"/AMS/Regular"+MathJax.OutputJax["HTML-CSS"].imgPacked+"/CombDiacritMarks.js");

