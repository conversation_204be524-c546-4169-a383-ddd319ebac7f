/*
 *  /MathJax/fonts/HTML-CSS/TeX/png/Main/Bold/CombDiactForSymbols.js
 *  
 *  Copyright (c) 2010-2013 The MathJax Consortium
 *
 *  Part of the MathJax library.
 *  See http://www.mathjax.org for details.
 * 
 *  Licensed under the Apache License, Version 2.0;
 *  you may not use this file except in compliance with the License.
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 */

MathJax.OutputJax["HTML-CSS"].defineImageData({"MathJax_Main-bold":{8407:[[4,2,-3],[5,3,-4],[6,3,-5],[7,3,-6],[8,3,-7],[9,5,-8],[11,5,-10],[13,6,-11],[15,6,-14],[17,8,-16],[21,9,-20],[25,11,-23],[29,13,-28],[34,15,-33]]}});MathJax.Ajax.loadComplete(MathJax.OutputJax["HTML-CSS"].imgDir+"/Main/Bold"+MathJax.OutputJax["HTML-CSS"].imgPacked+"/CombDiactForSymbols.js");

