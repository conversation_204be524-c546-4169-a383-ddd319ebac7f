/*************************************************************
 *
 *  MathJax/fonts/HTML-CSS/TeX/png/SansSerif/Bold/Other.js
 *  
 *  Defines the image size data needed for the HTML-CSS OutputJax
 *  to display mathematics using fallback images when the fonts
 *  are not available to the client browser.
 *
 *  ---------------------------------------------------------------------
 *
 *  Copyright (c) 2009-2013 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 *
 */

MathJax.OutputJax["HTML-CSS"].defineImageData({
  "MathJax_SansSerif-bold": {
    0xA0: [  // NO-BREAK SPACE
      [1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],
      [1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0]
    ],
    0x131: [  // LATIN SMALL LETTER DOTLESS I
      [2,3,0],[2,4,0],[2,5,0],[3,6,0],[3,7,0],[4,8,0],[4,9,0],[5,11,0],
      [6,13,0],[7,16,0],[8,19,0],[10,22,0],[11,26,0],[14,30,0]
    ],
    0x237: [  // LATIN SMALL LETTER DOTLESS J
      [3,4,1],[3,6,2],[4,7,2],[4,8,2],[5,10,3],[6,11,3],[7,13,4],[8,16,5],
      [9,18,5],[11,23,7],[13,27,8],[15,31,9],[17,37,12],[21,44,14]
    ],
    0x300: [  // COMBINING GRAVE ACCENT
      [3,1,-3],[3,2,-4],[3,2,-5],[4,2,-7],[4,3,-7],[5,3,-9],[5,3,-10],[6,4,-12],
      [7,5,-14],[9,6,-17],[10,7,-21],[12,8,-24],[14,9,-29],[17,11,-34]
    ],
    0x301: [  // COMBINING ACUTE ACCENT
      [3,1,-3],[3,2,-4],[4,2,-5],[3,2,-7],[4,3,-7],[5,3,-9],[6,3,-10],[6,4,-12],
      [8,5,-14],[8,6,-17],[11,7,-21],[12,8,-24],[14,9,-29],[16,11,-34]
    ],
    0x302: [  // COMBINING CIRCUMFLEX ACCENT
      [4,1,-3],[4,2,-4],[4,2,-5],[5,2,-7],[6,3,-7],[7,3,-9],[7,3,-10],[9,4,-12],
      [10,5,-14],[12,6,-17],[14,7,-21],[16,8,-24],[19,9,-29],[23,11,-34]
    ],
    0x303: [  // COMBINING TILDE
      [4,1,-3],[4,1,-5],[5,2,-5],[5,3,-6],[6,3,-7],[7,3,-9],[8,3,-10],[9,4,-12],
      [11,4,-15],[13,5,-18],[16,6,-22],[18,7,-25],[21,8,-30],[25,10,-35]
    ],
    0x304: [  // COMBINING MACRON
      [4,1,-3],[4,1,-5],[5,2,-5],[6,3,-6],[6,3,-8],[7,2,-9],[9,2,-11],[11,3,-13],
      [12,3,-15],[14,4,-19],[16,5,-23],[20,5,-26],[23,6,-31],[27,7,-37]
    ],
    0x306: [  // COMBINING BREVE
      [4,1,-3],[4,1,-5],[5,2,-5],[6,3,-6],[6,2,-8],[7,2,-10],[9,3,-10],[10,4,-12],
      [11,4,-15],[14,5,-18],[16,6,-22],[19,7,-25],[22,8,-30],[26,10,-35]
    ],
    0x307: [  // COMBINING DOT ABOVE
      [2,1,-3],[2,1,-5],[3,2,-5],[3,2,-7],[3,2,-8],[3,2,-10],[4,2,-11],[5,3,-13],
      [5,3,-16],[6,4,-19],[7,4,-24],[8,5,-27],[10,6,-32],[12,7,-38]
    ],
    0x308: [  // COMBINING DIAERESIS
      [4,2,-3],[4,2,-5],[5,3,-5],[5,3,-7],[6,3,-8],[7,3,-10],[8,3,-11],[9,4,-13],
      [11,4,-16],[12,5,-19],[15,5,-24],[18,6,-27],[21,7,-32],[25,8,-38]
    ],
    0x30A: [  // COMBINING RING ABOVE
      [3,1,-3],[3,2,-4],[3,2,-5],[4,2,-7],[5,2,-8],[5,3,-9],[6,3,-10],[7,4,-12],
      [8,5,-14],[10,6,-17],[11,6,-22],[13,7,-25],[15,9,-29],[18,10,-35]
    ],
    0x30B: [  // COMBINING DOUBLE ACUTE ACCENT
      [4,1,-3],[4,2,-4],[5,2,-5],[5,2,-7],[6,3,-7],[7,3,-9],[8,3,-10],[9,4,-12],
      [11,5,-14],[12,6,-17],[15,7,-21],[17,8,-24],[20,9,-29],[23,11,-34]
    ],
    0x30C: [  // COMBINING CARON
      [4,1,-3],[4,2,-4],[4,2,-5],[5,2,-6],[6,3,-7],[7,3,-8],[7,3,-10],[9,4,-12],
      [10,5,-14],[12,6,-17],[14,7,-20],[16,8,-23],[19,9,-27],[23,11,-33]
    ],
    0x393: [  // GREEK CAPITAL LETTER GAMMA
      [4,4,0],[5,6,0],[6,7,0],[7,9,0],[8,10,0],[9,12,0],[11,13,0],[13,16,0],
      [15,19,0],[18,23,0],[21,28,0],[25,32,0],[30,38,0],[35,45,0]
    ],
    0x394: [  // GREEK CAPITAL LETTER DELTA
      [6,4,0],[8,6,0],[9,7,0],[11,9,0],[12,10,0],[15,12,0],[17,13,0],[20,16,0],
      [24,19,0],[29,23,0],[34,28,0],[40,32,0],[48,38,0],[57,45,0]
    ],
    0x398: [  // GREEK CAPITAL LETTER THETA
      [6,4,0],[7,6,0],[8,7,0],[10,9,0],[11,10,0],[14,12,0],[16,13,0],[19,16,0],
      [22,19,0],[27,24,0],[32,29,0],[37,35,1],[44,41,1],[53,49,1]
    ],
    0x39B: [  // GREEK CAPITAL LETTER LAMDA
      [5,4,0],[6,6,0],[7,7,0],[8,9,0],[9,10,0],[11,12,0],[13,13,0],[15,16,0],
      [18,19,0],[21,23,0],[25,28,0],[29,32,0],[35,38,0],[41,45,0]
    ],
    0x39E: [  // GREEK CAPITAL LETTER XI
      [5,4,0],[6,6,0],[7,7,0],[9,9,0],[10,10,0],[12,12,0],[14,13,0],[16,16,0],
      [19,19,0],[23,23,0],[27,28,0],[32,32,0],[38,38,0],[45,45,0]
    ],
    0x3A0: [  // GREEK CAPITAL LETTER PI
      [5,4,0],[6,6,0],[7,7,0],[9,9,0],[10,10,0],[12,12,0],[14,13,0],[17,16,0],
      [20,19,0],[23,23,0],[28,28,0],[33,32,0],[39,38,0],[46,45,0]
    ],
    0x3A3: [  // GREEK CAPITAL LETTER SIGMA
      [6,4,0],[7,6,0],[8,7,0],[9,9,0],[11,10,0],[13,12,0],[15,13,0],[17,16,0],
      [21,19,0],[25,23,0],[29,28,0],[34,32,0],[41,38,0],[49,45,0]
    ],
    0x3A5: [  // GREEK CAPITAL LETTER UPSILON
      [6,4,0],[7,6,0],[8,7,0],[10,9,0],[11,10,0],[14,12,0],[16,13,0],[19,16,0],
      [22,19,0],[26,24,0],[31,29,0],[37,34,0],[44,40,0],[52,47,0]
    ],
    0x3A6: [  // GREEK CAPITAL LETTER PHI
      [6,4,0],[7,6,0],[8,7,0],[9,9,0],[11,10,0],[13,12,0],[15,13,0],[17,16,0],
      [21,19,0],[25,23,0],[29,28,0],[34,32,0],[41,38,0],[49,45,0]
    ],
    0x3A8: [  // GREEK CAPITAL LETTER PSI
      [6,4,0],[7,6,0],[9,7,0],[10,9,0],[12,10,0],[14,12,0],[17,13,0],[20,16,0],
      [23,19,0],[28,23,0],[33,28,0],[39,32,0],[46,38,0],[55,45,0]
    ],
    0x3A9: [  // GREEK CAPITAL LETTER OMEGA
      [6,4,0],[7,6,0],[8,7,0],[9,9,0],[11,10,0],[13,12,0],[15,13,0],[18,16,0],
      [21,19,0],[25,24,0],[29,28,0],[35,33,0],[41,39,0],[49,46,0]
    ],
    0x2013: [  // EN DASH
      [4,1,-1],[5,1,-2],[6,2,-2],[7,2,-2],[8,3,-3],[10,3,-3],[11,2,-5],[13,3,-6],
      [16,3,-7],[19,3,-8],[22,4,-10],[26,5,-11],[31,5,-13],[37,6,-16]
    ],
    0x2014: [  // EM DASH
      [8,1,-1],[10,1,-2],[11,1,-2],[13,2,-2],[16,3,-3],[19,3,-3],[22,2,-5],[26,3,-6],
      [31,3,-7],[37,3,-8],[43,4,-10],[52,5,-11],[61,5,-13],[73,6,-16]
    ],
    0x2018: [  // LEFT SINGLE QUOTATION MARK
      [2,2,-2],[2,3,-3],[3,3,-4],[3,3,-6],[4,4,-6],[4,5,-7],[5,5,-8],[6,6,-10],
      [7,7,-12],[8,9,-14],[9,10,-18],[11,12,-20],[13,14,-24],[15,17,-28]
    ],
    0x2019: [  // RIGHT SINGLE QUOTATION MARK
      [2,2,-2],[2,3,-3],[3,3,-4],[3,4,-5],[4,4,-6],[4,5,-7],[5,5,-8],[6,6,-10],
      [7,8,-11],[8,9,-14],[9,10,-18],[11,12,-20],[13,15,-23],[15,17,-28]
    ],
    0x201C: [  // LEFT DOUBLE QUOTATION MARK
      [4,2,-2],[5,3,-3],[6,3,-4],[7,3,-6],[8,4,-6],[9,5,-7],[11,5,-8],[13,6,-10],
      [15,7,-12],[18,9,-14],[21,10,-18],[25,12,-20],[29,14,-24],[35,17,-28]
    ],
    0x201D: [  // RIGHT DOUBLE QUOTATION MARK
      [3,2,-2],[4,3,-3],[5,3,-4],[5,4,-5],[6,4,-6],[7,5,-7],[8,5,-8],[10,6,-10],
      [12,8,-11],[14,9,-14],[17,10,-18],[20,12,-20],[23,15,-23],[27,17,-28]
    ]
  }
});

MathJax.Ajax.loadComplete(MathJax.OutputJax["HTML-CSS"].imgDir+"/SansSerif/Bold"+
                          MathJax.OutputJax["HTML-CSS"].imgPacked+"/Other.js");
