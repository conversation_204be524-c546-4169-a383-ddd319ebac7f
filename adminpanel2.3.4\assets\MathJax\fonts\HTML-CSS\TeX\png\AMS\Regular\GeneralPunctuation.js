/*
 *  /MathJax/fonts/HTML-CSS/TeX/png/AMS/Regular/GeneralPunctuation.js
 *  
 *  Copyright (c) 2010-2013 The MathJax Consortium
 *
 *  Part of the MathJax library.
 *  See http://www.mathjax.org for details.
 * 
 *  Licensed under the Apache License, Version 2.0;
 *  you may not use this file except in compliance with the License.
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 */

MathJax.OutputJax["HTML-CSS"].defineImageData({MathJax_AMS:{8245:[[2,4,0],[3,5,0],[3,5,0],[3,6,-1],[4,7,-1],[5,9,-1],[5,10,-1],[6,12,-1],[7,15,-1],[9,17,-1],[10,20,-2],[12,24,-2],[14,29,-2],[17,34,-3]]}});MathJax.Ajax.loadComplete(MathJax.OutputJax["HTML-CSS"].imgDir+"/AMS/Regular"+MathJax.OutputJax["HTML-CSS"].imgPacked+"/GeneralPunctuation.js");

