/*
 *  /MathJax/fonts/HTML-CSS/TeX/png/Main/Regular/GeneralPunctuation.js
 *  
 *  Copyright (c) 2010-2013 The MathJax Consortium
 *
 *  Part of the MathJax library.
 *  See http://www.mathjax.org for details.
 * 
 *  Licensed under the Apache License, Version 2.0;
 *  you may not use this file except in compliance with the License.
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 */

MathJax.OutputJax["HTML-CSS"].defineImageData({MathJax_Main:{8194:[[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0]],8195:[[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0]],8196:[[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0]],8197:[[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0]],8198:[[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0]],8201:[[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0]],8202:[[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0]],8211:[[4,1,-1],[5,1,-2],[5,1,-2],[6,1,-2],[7,1,-3],[9,1,-4],[10,1,-5],[12,1,-6],[14,2,-6],[17,3,-8],[20,3,-9],[24,2,-12],[28,3,-14],[33,3,-17]],8212:[[7,1,-1],[9,1,-2],[10,1,-2],[12,1,-2],[14,1,-3],[17,1,-4],[20,1,-5],[24,1,-6],[28,2,-6],[33,3,-8],[40,3,-9],[47,2,-12],[56,3,-14],[66,3,-17]],8216:[[2,2,-3],[2,4,-2],[2,4,-4],[3,4,-4],[3,4,-5],[4,6,-6],[4,7,-7],[5,8,-9],[6,9,-11],[7,11,-12],[8,13,-15],[10,15,-18],[11,18,-21],[14,21,-25]],8217:[[2,3,-2],[2,3,-3],[3,4,-4],[3,4,-4],[3,5,-4],[4,6,-6],[5,7,-7],[5,8,-9],[6,10,-10],[7,11,-12],[9,13,-15],[10,15,-18],[12,18,-21],[14,22,-25]],8220:[[4,2,-3],[4,4,-2],[5,4,-4],[6,4,-4],[7,4,-5],[8,6,-6],[10,7,-7],[11,8,-9],[13,9,-11],[16,11,-12],[19,13,-15],[22,15,-18],[26,18,-21],[31,21,-25]],8221:[[3,3,-2],[4,3,-3],[4,4,-4],[5,4,-4],[6,5,-4],[7,6,-6],[8,7,-7],[9,8,-9],[11,10,-10],[13,11,-12],[15,13,-15],[18,15,-18],[21,18,-21],[25,22,-25]],8224:[[3,6,1],[4,8,2],[4,10,2],[5,10,2],[6,12,3],[7,16,4],[8,18,4],[9,23,5],[11,27,6],[13,31,7],[16,37,8],[18,44,11],[22,52,12],[26,62,15]],8225:[[3,6,1],[4,8,2],[4,10,2],[5,10,2],[6,12,3],[7,16,4],[8,18,4],[10,23,5],[11,27,6],[13,31,7],[16,37,8],[19,43,9],[22,51,11],[26,61,13]],8230:[[8,1,0],[10,2,0],[11,2,0],[13,2,0],[16,2,0],[19,3,0],[22,3,0],[26,3,0],[31,4,0],[36,5,0],[43,5,0],[51,6,0],[61,7,0],[72,9,0]],8242:[[2,4,0],[3,5,0],[3,6,0],[4,6,0],[4,7,-1],[5,9,-1],[6,11,-1],[7,13,-1],[8,15,-1],[9,18,-1],[11,21,-2],[13,24,-2],[15,29,-2],[18,35,-3]]}});MathJax.Ajax.loadComplete(MathJax.OutputJax["HTML-CSS"].imgDir+"/Main/Regular"+MathJax.OutputJax["HTML-CSS"].imgPacked+"/GeneralPunctuation.js");

