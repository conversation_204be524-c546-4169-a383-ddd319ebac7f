<?xml version="1.0" encoding="utf-8"?>
<network-security-config>
    <!-- Allow cleartext traffic for local development -->
    <domain-config cleartextTrafficPermitted="true">
        <!-- Android Emulator -->
        <domain includeSubdomains="true">********</domain>
        
        <!-- iOS Simulator / Local -->
        <domain includeSubdomains="true">localhost</domain>
        <domain includeSubdomains="true">127.0.0.1</domain>
        
        <!-- Common local network IP ranges -->
        <domain includeSubdomains="true">*************</domain>
        <domain includeSubdomains="true">*************</domain>
        <domain includeSubdomains="true">*************</domain>
        <domain includeSubdomains="true">*************</domain>
        <domain includeSubdomains="true">*************</domain>
        <domain includeSubdomains="true">*************</domain>
        <domain includeSubdomains="true">*************</domain>
        <domain includeSubdomains="true">*************</domain>
        <domain includeSubdomains="true">*************</domain>
        <domain includeSubdomains="true">*************</domain>
        <domain includeSubdomains="true">*************</domain>
        <domain includeSubdomains="true">*************</domain>
        
        <!-- Add your specific IP address here if different -->
        <!-- <domain includeSubdomains="true">YOUR_IP_ADDRESS</domain> -->
    </domain-config>
    
    <!-- Production domains should use HTTPS -->
    <domain-config cleartextTrafficPermitted="false">
        <domain includeSubdomains="true">guessy.yourdomain.com</domain>
        <domain includeSubdomains="true">yourdomain.com</domain>
    </domain-config>
</network-security-config>
