#!/bin/bash

echo "========================================"
echo "Guessy - Complete Local Development Setup"
echo "========================================"
echo

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

print_guessy() {
    echo -e "${PURPLE}🎯 $1${NC}"
}

# Check if running on macOS or Linux
if [[ "$OSTYPE" == "darwin"* ]]; then
    # macOS
    XAMPP_PATH="/Applications/XAMPP"
    WEB_ROOT="/Applications/XAMPP/htdocs"
elif [[ "$OSTYPE" == "linux-gnu"* ]]; then
    # Linux
    XAMPP_PATH="/opt/lampp"
    WEB_ROOT="/opt/lampp/htdocs"
else
    print_error "Unsupported operating system. Please use Windows batch file or setup manually."
    exit 1
fi

PROJECT_PATH=$(pwd)
ADMIN_PATH="$WEB_ROOT/guessy_admin"

print_guessy "Setting up Guessy Quiz Application"
echo "Current directory: $PROJECT_PATH"
echo "XAMPP path: $XAMPP_PATH"
echo "Guessy admin panel will be installed at: $ADMIN_PATH"
echo

# Check if XAMPP is installed
if [ ! -d "$XAMPP_PATH" ]; then
    print_error "XAMPP not found at $XAMPP_PATH"
    print_info "Please install XAMPP first from https://www.apachefriends.org/"
    exit 1
fi

# Check if admin panel exists
if [ ! -d "adminpanel2.3.4" ]; then
    print_error "adminpanel2.3.4 directory not found!"
    print_info "Please make sure you're running this script from the project root."
    exit 1
fi

# Check required tools
if ! command -v php &> /dev/null; then
    print_error "PHP not found. Please install PHP or add it to PATH."
    exit 1
fi

if ! command -v flutter &> /dev/null; then
    print_warning "Flutter not found. Please install Flutter SDK."
fi

if ! command -v dart &> /dev/null; then
    print_warning "Dart not found. Please install Dart SDK."
fi

echo "Step 1: Configuring Guessy admin panel..."
# Run PHP configuration script for Guessy
php guessy_admin_config.php
if [ $? -ne 0 ]; then
    print_error "Failed to configure Guessy admin panel"
    exit 1
fi

echo
echo "Step 2: Copying Guessy admin panel to XAMPP..."
# Copy admin panel to XAMPP htdocs with Guessy name
if [ -d "$ADMIN_PATH" ]; then
    print_info "Removing existing Guessy admin panel..."
    sudo rm -rf "$ADMIN_PATH"
fi

print_info "Copying Guessy files..."
sudo cp -r "adminpanel2.3.4" "$ADMIN_PATH"
if [ $? -ne 0 ]; then
    print_error "Failed to copy Guessy admin panel files"
    print_info "You may need to run with sudo: sudo ./guessy_setup.sh"
    exit 1
fi

# Set proper permissions
sudo chmod -R 755 "$ADMIN_PATH"
sudo chmod -R 777 "$ADMIN_PATH/images"
sudo chmod -R 777 "$ADMIN_PATH/upload"

print_status "Guessy admin panel copied successfully"

echo
echo "Step 3: Setting up Guessy database..."
# Start XAMPP services
print_info "Starting XAMPP services..."
if [[ "$OSTYPE" == "darwin"* ]]; then
    # macOS
    sudo "$XAMPP_PATH/xamppfiles/xampp" start
elif [[ "$OSTYPE" == "linux-gnu"* ]]; then
    # Linux
    sudo "$XAMPP_PATH/lampp" start
fi

# Wait for services to start
sleep 5

# Create Guessy database
print_info "Creating Guessy database..."
if [[ "$OSTYPE" == "darwin"* ]]; then
    MYSQL_PATH="$XAMPP_PATH/xamppfiles/bin/mysql"
else
    MYSQL_PATH="$XAMPP_PATH/bin/mysql"
fi

sudo "$MYSQL_PATH" -u root -e "CREATE DATABASE IF NOT EXISTS guessy_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;"
if [ $? -eq 0 ]; then
    print_status "Guessy database created successfully"
else
    print_warning "Could not create Guessy database automatically"
    print_info "Please create database 'guessy_db' manually in phpMyAdmin"
fi

# Import database schema
print_info "Importing Guessy database schema..."
sudo "$MYSQL_PATH" -u root guessy_db < "adminpanel2.3.4/install/assets/quiz.php"
if [ $? -eq 0 ]; then
    print_status "Database schema imported successfully"
else
    print_warning "Could not import database automatically"
    print_info "Please import adminpanel2.3.4/install/assets/quiz.php manually in phpMyAdmin"
fi

# Run Guessy-specific database setup
print_info "Setting up Guessy-specific data..."
sudo "$MYSQL_PATH" -u root guessy_db < "guessy_database_setup.sql"
if [ $? -eq 0 ]; then
    print_status "Guessy database setup completed"
else
    print_warning "Could not run Guessy database setup"
    print_info "Please run guessy_database_setup.sql manually in phpMyAdmin"
fi

echo
echo "Step 4: Configuring Guessy Flutter app..."
# Update Flutter package name
if command -v dart &> /dev/null; then
    dart update_flutter_package.dart
    if [ $? -ne 0 ]; then
        print_error "Failed to update Flutter package name"
        print_info "Please run: dart update_flutter_package.dart manually"
    else
        print_status "Flutter package name updated to com.guessy.quiz"
    fi
else
    print_warning "Dart not found, skipping Flutter package update"
fi

echo
echo "Step 5: Installing Flutter dependencies..."
if command -v flutter &> /dev/null; then
    cd elite_quiz_app-2.3.4
    flutter clean
    flutter pub get
    if [ $? -ne 0 ]; then
        print_error "Failed to install Flutter dependencies"
        print_info "Please run 'flutter pub get' manually in elite_quiz_app-2.3.4 directory"
    else
        print_status "Flutter dependencies installed"
    fi
    
    # Generate app icons
    dart run flutter_launcher_icons
    cd ..
else
    print_warning "Flutter not found, skipping dependency installation"
fi

echo
echo "Step 6: Creating Firebase configuration templates..."
print_info "Creating Firebase templates for Guessy..."
print_info "Please update these templates with your actual Firebase project details:"
print_info "- elite_quiz_app-2.3.4/android/app/google-services.json.template"
print_info "- elite_quiz_app-2.3.4/ios/Runner/GoogleService-Info.plist.template"

echo
echo "========================================"
print_guessy "Guessy Setup Complete!"
echo "========================================"
echo
print_guessy "Guessy Configuration:"
echo "Admin Panel: http://localhost/guessy_admin"
echo "Login: guessy_admin / guessy123 (or admin / admin123)"
echo "Database: guessy_db"
echo "Package: com.guessy.quiz"
echo "App Name: Guessy"
echo
print_info "Next Steps:"
echo "1. Create Firebase project: guessy-quiz-app"
echo "2. Add Android app with package: com.guessy.quiz"
echo "3. Download google-services.json and replace template"
echo "4. Update Firebase configuration in admin panel"
echo "5. Test Guessy admin panel: http://localhost/guessy_admin"
echo "6. Run Guessy Flutter app: flutter run"
echo
print_info "Network Configuration:"
echo "- Android Emulator: http://********/guessy_admin"
echo "- iOS Simulator: http://localhost/guessy_admin"
echo "- Physical Device: http://YOUR_IP/guessy_admin"
echo
print_guessy "Guessy Features:"
echo "- Quiz Zone with multiple categories"
echo "- Word Puzzles and Brain Teasers"
echo "- Math Challenges"
echo "- Real-time Battles"
echo "- Daily Quizzes and Contests"
echo "- Coin Rewards System"
echo "- Achievement Badges"
echo
print_info "Troubleshooting:"
echo "- If admin panel doesn't load, check XAMPP Apache service"
echo "- If database errors occur, import schema manually from phpMyAdmin"
echo "- For API connectivity issues, check network security config"
echo "- Update IP address in Flutter config for physical device testing"
echo
print_info "Useful Commands:"
echo "- Start XAMPP: sudo $XAMPP_PATH/xampp start"
echo "- Stop XAMPP: sudo $XAMPP_PATH/xampp stop"
echo "- Restart XAMPP: sudo $XAMPP_PATH/xampp restart"
echo
print_guessy "Your Guessy quiz application is ready for development! 🎉"
echo
