/*
 *  /MathJax/fonts/HTML-CSS/TeX/png/AMS/Regular/Arrows.js
 *  
 *  Copyright (c) 2010-2013 The MathJax Consortium
 *
 *  Part of the MathJax library.
 *  See http://www.mathjax.org for details.
 * 
 *  Licensed under the Apache License, Version 2.0;
 *  you may not use this file except in compliance with the License.
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 */

MathJax.OutputJax["HTML-CSS"].defineImageData({MathJax_AMS:{8592:[[3,3,0],[4,4,0],[5,5,0],[5,5,0],[6,6,0],[7,7,-1],[9,8,-1],[10,10,-1],[12,11,-1],[14,13,-2],[17,16,-2],[20,19,-2],[24,21,-3],[28,25,-4]],8594:[[3,4,0],[4,4,0],[5,5,0],[5,5,0],[6,7,0],[7,7,-1],[9,8,-1],[10,10,-1],[12,12,-1],[14,13,-2],[17,16,-2],[20,18,-3],[23,22,-3],[28,25,-4]],8602:[[7,4,0],[8,4,0],[10,5,0],[12,5,0],[14,7,0],[16,7,-1],[19,8,-1],[22,9,-1],[27,11,-1],[31,14,-1],[37,16,-2],[44,19,-2],[53,22,-3],[62,26,-3]],8603:[[7,4,0],[8,4,0],[10,5,0],[12,5,0],[14,7,0],[16,7,-1],[19,8,-1],[22,9,-1],[27,11,-1],[31,14,-1],[37,16,-2],[44,19,-2],[53,22,-3],[62,26,-3]],8606:[[7,3,0],[8,4,0],[10,5,0],[12,5,0],[14,5,-1],[16,7,-1],[19,7,-1],[23,9,-1],[27,10,-2],[32,12,-2],[38,14,-3],[45,16,-4],[53,20,-4],[63,23,-5]],8608:[[7,3,0],[8,4,0],[10,5,0],[12,5,0],[14,5,-1],[16,7,-1],[19,7,-1],[22,9,-1],[27,10,-2],[32,12,-2],[37,14,-3],[44,16,-4],[53,20,-4],[63,23,-5]],8610:[[8,3,0],[9,4,0],[11,5,0],[13,5,0],[15,5,-1],[18,6,-1],[21,8,-1],[24,9,-1],[29,10,-2],[34,12,-2],[41,14,-3],[48,17,-3],[58,20,-4],[68,23,-5]],8611:[[8,3,0],[9,4,0],[11,5,0],[13,5,0],[15,5,-1],[18,6,-1],[21,8,-1],[25,9,-1],[30,10,-2],[35,12,-2],[42,14,-3],[49,17,-3],[59,20,-4],[70,23,-5]],8619:[[7,5,1],[8,6,1],[10,7,1],[12,8,1],[13,9,1],[16,11,1],[19,12,1],[23,16,2],[27,18,2],[32,21,2],[38,25,2],[45,29,2],[53,35,3],[64,41,3]],8620:[[7,5,1],[8,6,1],[10,7,1],[12,8,1],[13,9,1],[16,11,1],[19,13,2],[22,16,2],[26,18,2],[31,21,2],[37,25,2],[44,29,2],[53,35,3],[62,41,3]],8621:[[10,3,0],[12,4,0],[14,5,0],[16,5,0],[19,5,-1],[23,7,-1],[26,7,-1],[31,9,-1],[37,10,-2],[44,12,-2],[52,14,-3],[62,16,-4],[74,20,-4],[88,23,-5]],8622:[[7,4,0],[8,4,0],[10,5,0],[12,5,0],[14,7,0],[16,7,-1],[19,8,-1],[22,9,-1],[27,11,-1],[31,14,-1],[37,16,-2],[44,19,-2],[53,22,-3],[62,26,-3]],8624:[[4,5,0],[4,6,0],[5,8,0],[6,9,0],[7,10,0],[8,13,0],[9,15,1],[11,18,1],[13,21,0],[15,24,0],[18,29,0],[21,34,0],[25,41,0],[30,48,0]],8625:[[4,5,0],[4,6,0],[5,8,0],[6,9,0],[7,10,0],[8,13,0],[9,15,1],[11,18,1],[13,21,0],[15,24,0],[18,29,0],[21,34,0],[25,41,0],[30,48,0]],8630:[[7,4,0],[8,5,1],[10,6,1],[11,6,0],[13,7,0],[16,9,1],[19,10,1],[23,11,0],[27,13,0],[32,17,1],[38,19,1],[45,23,1],[53,27,1],[63,32,1]],8631:[[7,4,0],[9,5,1],[10,6,1],[12,6,0],[14,7,0],[17,8,0],[20,10,1],[23,11,0],[28,13,0],[33,17,1],[39,19,1],[46,23,1],[55,27,1],[65,32,1]],8634:[[5,6,1],[6,7,1],[7,8,1],[9,9,1],[10,10,1],[12,13,2],[15,15,2],[17,18,2],[21,21,3],[24,25,3],[29,30,4],[34,35,4],[41,41,5],[48,49,6]],8635:[[5,6,1],[6,7,1],[7,8,1],[9,9,1],[10,10,1],[12,13,2],[14,15,2],[17,18,2],[20,21,3],[24,25,3],[29,30,4],[34,35,4],[40,41,5],[48,49,6]],8638:[[3,7,2],[4,8,2],[4,9,2],[5,12,3],[6,13,3],[7,16,4],[8,18,4],[9,22,5],[11,26,6],[13,30,7],[15,36,8],[18,43,10],[21,50,11],[25,59,13]],8639:[[2,7,2],[2,8,2],[3,9,2],[3,12,3],[3,13,3],[4,16,4],[5,18,4],[6,22,5],[7,26,6],[8,30,7],[9,36,8],[11,43,10],[13,50,11],[15,59,13]],8642:[[3,7,2],[4,8,2],[4,9,2],[5,12,3],[6,13,3],[7,16,4],[8,18,4],[9,22,5],[11,26,6],[13,30,7],[15,36,8],[18,43,10],[21,50,11],[25,59,13]],8643:[[2,7,2],[2,8,2],[3,9,2],[3,12,3],[3,13,3],[4,16,4],[5,18,4],[6,22,5],[7,26,6],[8,30,7],[9,36,8],[11,43,10],[13,50,11],[15,59,13]],8644:[[7,5,0],[8,6,0],[10,7,0],[12,9,1],[14,10,0],[16,13,1],[19,13,0],[22,17,1],[27,19,0],[32,22,0],[37,27,0],[44,31,0],[53,37,0],[63,45,1]],8646:[[7,5,0],[8,6,0],[10,7,0],[12,9,1],[14,10,0],[16,13,1],[19,13,0],[22,17,1],[27,19,0],[32,22,0],[37,27,0],[44,31,0],[53,37,0],[63,45,1]],8647:[[7,6,1],[8,6,1],[10,7,1],[12,9,2],[14,11,2],[16,12,2],[19,14,2],[22,17,3],[27,20,3],[32,23,3],[37,27,4],[44,31,4],[53,38,5],[63,45,6]],8648:[[6,7,2],[7,8,2],[8,9,2],[9,12,3],[11,13,3],[13,16,4],[15,18,4],[18,22,5],[21,26,6],[25,30,7],[30,36,8],[35,42,9],[42,50,11],[49,59,13]],8649:[[7,6,1],[8,6,1],[10,7,1],[12,9,2],[14,11,2],[16,12,2],[19,14,2],[22,17,3],[27,20,3],[32,23,3],[37,27,4],[44,31,4],[53,38,5],[63,45,6]],8650:[[6,7,2],[7,8,2],[8,9,2],[9,12,3],[11,13,3],[13,16,4],[15,18,4],[18,22,5],[21,26,6],[25,30,7],[30,36,8],[35,42,9],[42,50,11],[50,59,13]],8651:[[7,5,1],[8,6,1],[10,7,1],[12,8,1],[14,9,1],[16,10,1],[19,11,1],[22,14,1],[27,16,1],[32,18,1],[37,22,1],[44,25,1],[53,30,1],[63,35,1]],8652:[[7,5,1],[8,6,1],[10,7,1],[12,8,1],[14,9,1],[16,10,1],[19,11,1],[22,14,1],[27,16,1],[32,18,1],[37,22,1],[44,25,1],[53,30,1],[63,35,1]],8653:[[7,5,1],[8,6,1],[10,7,1],[12,8,1],[14,9,1],[16,10,1],[19,12,1],[22,14,1],[27,16,1],[31,20,2],[37,23,2],[44,27,2],[53,33,3],[62,38,3]],8654:[[7,5,1],[8,6,1],[10,7,1],[12,8,1],[14,9,1],[17,10,1],[19,12,1],[23,14,1],[27,16,1],[32,19,1],[38,24,2],[45,27,2],[54,33,3],[64,38,3]],8655:[[7,5,1],[8,6,1],[10,7,1],[12,8,1],[14,9,1],[16,10,1],[19,12,1],[22,14,1],[27,16,1],[32,20,2],[37,23,2],[44,27,2],[53,33,3],[63,39,3]],8666:[[7,6,1],[8,7,1],[10,8,2],[12,10,2],[14,11,2],[16,14,3],[19,14,2],[22,18,3],[27,22,4],[32,25,4],[37,29,5],[44,35,6],[53,41,7],[63,49,8]],8667:[[7,6,1],[8,7,1],[10,8,2],[11,10,2],[13,11,2],[16,14,3],[18,14,2],[22,18,3],[26,21,4],[31,25,4],[37,29,5],[43,34,5],[52,41,7],[61,49,8]],8669:[[7,3,0],[8,4,0],[10,5,0],[12,5,0],[14,5,-1],[16,7,-1],[19,7,-1],[22,9,-1],[27,10,-2],[32,12,-2],[37,14,-3],[44,16,-4],[53,20,-4],[63,23,-5]],8672:[[9,3,0],[11,4,0],[13,5,0],[15,5,0],[18,6,0],[21,7,-1],[25,8,-1],[29,10,-1],[35,11,-1],[41,13,-2],[49,16,-2],[58,19,-2],[69,21,-3],[83,25,-4]],8674:[[9,4,0],[11,4,0],[13,5,0],[15,5,0],[18,7,0],[21,7,-1],[25,8,-1],[29,10,-1],[35,12,-1],[42,13,-2],[49,16,-2],[58,18,-3],[70,22,-3],[82,25,-4]]}});MathJax.Ajax.loadComplete(MathJax.OutputJax["HTML-CSS"].imgDir+"/AMS/Regular"+MathJax.OutputJax["HTML-CSS"].imgPacked+"/Arrows.js");

