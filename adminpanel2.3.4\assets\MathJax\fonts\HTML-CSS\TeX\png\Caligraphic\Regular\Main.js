/*
 *  /MathJax/fonts/HTML-CSS/TeX/png/Caligraphic/Regular/Main.js
 *  
 *  Copyright (c) 2010-2013 The MathJax Consortium
 *
 *  Part of the MathJax library.
 *  See http://www.mathjax.org for details.
 * 
 *  Licensed under the Apache License, Version 2.0;
 *  you may not use this file except in compliance with the License.
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 */

MathJax.OutputJax["HTML-CSS"].defineImageData({MathJax_Caligraphic:{32:[[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0]],48:[[4,4,0],[4,4,0],[5,5,0],[6,6,0],[7,7,0],[8,8,0],[9,9,0],[11,11,1],[13,13,1],[16,16,1],[18,19,1],[22,22,1],[26,26,1],[31,32,1]],49:[[3,3,0],[4,4,0],[5,5,0],[5,6,0],[6,7,0],[8,8,0],[9,9,0],[10,12,1],[12,14,1],[15,15,0],[17,18,0],[20,21,0],[24,25,0],[29,30,0]],50:[[4,3,0],[4,4,0],[5,5,0],[6,6,0],[7,6,0],[8,8,0],[9,9,0],[11,11,0],[13,13,0],[15,15,0],[18,18,0],[21,21,0],[25,25,0],[30,30,0]],51:[[4,5,2],[4,6,2],[5,7,2],[6,8,3],[7,10,3],[8,11,4],[9,13,4],[11,16,5],[13,19,7],[15,24,8],[18,26,8],[22,31,10],[26,37,12],[30,44,14]],52:[[4,5,2],[4,6,2],[5,7,2],[6,9,3],[7,10,3],[8,11,3],[10,13,4],[11,16,5],[14,19,6],[16,22,6],[19,26,8],[22,31,9],[27,36,11],[31,43,13]],53:[[4,5,2],[4,6,2],[5,7,2],[6,9,3],[7,10,3],[8,11,4],[9,13,4],[11,17,6],[13,19,7],[15,23,8],[18,26,8],[21,31,10],[25,37,12],[30,44,14]],54:[[4,5,0],[4,6,0],[5,7,0],[6,8,0],[7,10,0],[8,12,0],[9,13,0],[11,16,1],[13,19,1],[15,22,1],[18,28,1],[22,32,1],[26,38,1],[30,45,1]],55:[[4,6,2],[5,7,2],[5,8,3],[6,9,3],[7,10,3],[9,11,3],[10,14,4],[12,16,5],[14,20,7],[16,23,8],[19,27,9],[23,32,10],[27,38,12],[32,45,14]],56:[[4,5,0],[4,6,0],[5,7,0],[6,8,0],[7,10,0],[8,12,0],[9,13,0],[11,16,1],[13,19,1],[15,23,1],[18,27,1],[22,32,1],[26,38,1],[30,45,1]],57:[[4,5,2],[4,6,2],[5,7,2],[6,8,3],[7,10,3],[8,11,4],[9,13,4],[11,16,5],[13,19,7],[16,24,8],[18,26,8],[22,31,10],[26,37,12],[31,44,14]],65:[[6,5,0],[7,6,0],[9,8,1],[10,9,1],[12,11,1],[14,12,0],[16,15,1],[20,18,2],[23,22,2],[27,26,2],[33,31,2],[39,36,2],[46,43,2],[54,51,3]],66:[[5,5,0],[6,6,0],[7,7,0],[8,8,0],[10,10,0],[11,12,0],[13,14,0],[16,17,1],[19,21,1],[22,24,1],[26,29,1],[31,34,1],[37,40,1],[44,48,1]],67:[[4,5,0],[5,6,0],[6,7,0],[7,8,0],[8,10,0],[9,12,0],[11,14,0],[13,17,1],[15,21,1],[18,24,1],[21,29,1],[25,34,1],[30,40,1],[36,48,1]],68:[[6,5,0],[7,6,0],[8,7,0],[9,8,0],[11,9,0],[13,12,1],[15,15,1],[18,16,0],[22,19,0],[26,23,0],[30,27,0],[36,32,0],[43,37,0],[51,45,0]],69:[[4,5,0],[5,6,0],[6,7,0],[7,8,0],[8,10,0],[10,12,0],[11,14,0],[14,17,1],[16,21,1],[19,24,1],[23,29,1],[27,34,1],[32,40,1],[38,48,1]],70:[[6,5,0],[7,6,0],[9,7,0],[10,8,0],[12,10,0],[14,12,0],[17,15,0],[20,17,1],[23,20,1],[28,24,1],[33,28,1],[39,34,1],[46,40,2],[55,47,2]],71:[[5,6,1],[5,7,1],[6,8,1],[8,10,2],[9,11,1],[10,14,2],[12,17,3],[14,19,3],[17,23,3],[20,27,4],[24,32,4],[28,39,6],[34,46,7],[40,54,7]],72:[[6,5,0],[7,6,0],[8,7,0],[10,8,0],[12,10,0],[14,12,0],[16,14,0],[19,17,1],[23,21,1],[27,24,1],[32,29,2],[38,34,2],[45,41,2],[53,48,3]],73:[[6,5,0],[7,6,0],[8,7,0],[9,8,0],[10,9,0],[12,11,0],[14,14,0],[16,16,0],[19,19,0],[23,23,0],[28,27,0],[32,32,0],[38,38,0],[45,45,0]],74:[[6,6,1],[7,7,1],[9,8,1],[10,10,1],[12,11,2],[14,14,3],[17,17,3],[20,19,3],[24,22,3],[28,27,4],[33,32,5],[40,38,6],[47,44,7],[56,53,8]],75:[[6,5,0],[7,6,0],[8,7,0],[9,8,0],[11,10,0],[13,12,0],[15,14,0],[17,17,1],[21,21,1],[25,24,1],[29,29,1],[34,34,1],[41,40,1],[49,48,1]],76:[[5,5,0],[6,6,0],[7,7,0],[8,8,0],[10,10,0],[11,12,0],[13,14,0],[16,17,1],[19,21,1],[22,24,1],[26,29,1],[31,34,1],[37,40,1],[44,48,1]],77:[[8,5,0],[10,7,1],[12,8,1],[14,8,0],[16,10,0],[19,13,1],[23,15,1],[27,18,2],[32,22,2],[38,25,2],[45,30,2],[53,35,2],[63,41,2],[75,50,3]],78:[[8,6,0],[10,7,0],[11,9,1],[13,12,1],[15,13,1],[18,14,0],[21,17,1],[24,20,2],[29,24,2],[34,28,2],[40,33,2],[48,39,2],[57,46,2],[67,56,3]],79:[[6,5,0],[7,6,0],[8,7,0],[10,8,0],[11,10,0],[13,12,0],[16,14,0],[19,17,1],[22,21,1],[26,24,1],[31,29,1],[37,34,1],[43,40,1],[52,48,1]],80:[[6,6,1],[7,6,0],[8,8,1],[9,9,1],[11,11,1],[13,13,1],[15,16,2],[17,18,1],[21,21,2],[24,25,2],[29,29,3],[34,35,3],[41,41,3],[48,49,4]],81:[[6,6,1],[7,7,1],[8,8,1],[10,9,1],[11,11,1],[14,14,2],[16,17,3],[19,19,3],[22,24,4],[26,28,5],[31,33,5],[37,39,6],[44,46,7],[52,55,8]],82:[[6,5,0],[7,6,0],[9,7,0],[10,8,0],[12,10,0],[14,12,0],[17,15,0],[20,17,1],[24,19,1],[28,23,1],[33,28,1],[39,33,1],[47,39,1],[56,46,1]],83:[[5,5,0],[6,6,0],[7,7,0],[8,8,0],[9,10,0],[11,12,0],[13,14,0],[15,17,1],[18,21,1],[22,24,1],[26,29,1],[30,34,1],[36,40,1],[43,48,1]],84:[[6,6,1],[7,7,1],[9,8,1],[10,9,1],[12,11,1],[14,13,1],[17,15,1],[20,18,2],[23,22,2],[28,26,3],[33,31,3],[39,37,4],[46,44,4],[55,52,5]],85:[[6,5,0],[7,6,0],[8,7,0],[10,8,0],[11,10,0],[13,12,0],[15,14,0],[17,17,1],[20,20,1],[24,24,1],[28,28,1],[33,33,1],[40,39,1],[48,47,1]],86:[[5,6,1],[6,7,1],[7,8,1],[8,9,1],[10,11,1],[11,13,1],[13,15,1],[16,18,1],[19,21,2],[22,25,2],[26,30,3],[31,34,2],[37,41,3],[44,49,3]],87:[[8,6,1],[9,7,1],[11,8,1],[13,9,1],[15,11,1],[18,13,1],[21,15,1],[25,18,1],[29,21,2],[35,26,3],[41,30,3],[49,35,3],[58,42,4],[69,50,4]],88:[[6,5,0],[7,6,0],[8,7,0],[10,8,0],[12,9,0],[14,11,0],[16,14,0],[19,16,0],[23,19,0],[27,23,0],[32,27,0],[38,32,0],[45,38,0],[54,45,0]],89:[[5,6,1],[6,7,1],[8,8,1],[9,11,3],[10,12,2],[12,14,2],[14,16,2],[17,20,3],[20,23,4],[24,28,5],[29,33,6],[34,39,7],[40,46,8],[48,55,9]],90:[[6,5,0],[7,6,0],[8,7,0],[10,8,0],[11,10,0],[13,12,0],[15,14,0],[18,16,0],[22,19,0],[26,23,0],[31,27,0],[36,32,0],[43,38,0],[51,45,0]],160:[[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0]]}});MathJax.Ajax.loadComplete(MathJax.OutputJax["HTML-CSS"].imgDir+"/Caligraphic/Regular"+MathJax.OutputJax["HTML-CSS"].imgPacked+"/Main.js");

