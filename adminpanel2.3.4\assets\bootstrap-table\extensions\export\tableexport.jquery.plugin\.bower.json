{"name": "tableexport.jquery.plugin", "version": "1.26.0", "description": "html table export", "main": "tableExport.js", "authors": ["hhurz"], "license": "MIT", "keywords": ["html5", "javascript", "j<PERSON>y", "export", "table"], "homepage": "https://github.com/hhurz/tableExport.jquery.plugin", "dependencies": {"jquery": ">=1.9.1", "file-saver": ">=2.0.1", "html2canvas": "*", "jspdf": ">=2.0.0", "pdfmake": "^0.1.71"}, "moduleType": ["globals"], "ignore": [".git", ".idea", "package.json", "package-lock.json", "libs", "node_modules", "bower_components", "test", "tools"], "_release": "1.26.0", "_resolution": {"type": "version", "tag": "v1.26.0", "commit": "1aebdb0d86fae8323272078be22217b622b05f13"}, "_source": "https://github.com/hhurz/tableExport.jquery.plugin.git", "_target": "^1.26.0", "_originalSource": "tableExport.jquery.plugin", "_direct": true}