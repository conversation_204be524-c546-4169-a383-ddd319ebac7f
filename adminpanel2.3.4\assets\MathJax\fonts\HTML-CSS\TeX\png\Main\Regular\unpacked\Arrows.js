/*************************************************************
 *
 *  MathJax/fonts/HTML-CSS/TeX/png/Main/Regular/Arrows.js
 *  
 *  Defines the image size data needed for the HTML-CSS OutputJax
 *  to display mathematics using fallback images when the fonts
 *  are not available to the client browser.
 *
 *  ---------------------------------------------------------------------
 *
 *  Copyright (c) 2009-2013 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 *
 */

MathJax.OutputJax["HTML-CSS"].defineImageData({
  "MathJax_Main": {
    0x2190: [  // LEFTWARDS ARROW
      [7,5,1],[8,5,0],[10,6,1],[12,7,1],[14,9,1],[16,9,0],[19,11,1],[22,13,1],
      [27,15,1],[32,18,1],[37,22,1],[44,25,1],[53,30,1],[63,35,1]
    ],
    0x2191: [  // UPWARDS ARROW
      [4,7,2],[5,8,2],[5,9,2],[6,12,3],[7,13,3],[9,16,4],[10,18,4],[11,22,5],
      [13,26,6],[16,30,7],[19,36,8],[22,42,9],[27,50,11],[32,59,13]
    ],
    0x2192: [  // RIGHTWARDS ARROW
      [7,5,1],[8,5,0],[10,7,1],[12,7,1],[14,9,1],[16,9,0],[19,11,1],[22,13,1],
      [27,15,1],[32,18,1],[37,22,1],[44,25,1],[53,30,1],[63,35,1]
    ],
    0x2193: [  // DOWNWARDS ARROW
      [4,7,2],[5,8,2],[5,9,2],[6,12,3],[7,13,3],[9,16,4],[10,18,4],[12,22,5],
      [14,26,6],[16,30,7],[19,36,8],[23,42,9],[27,50,11],[32,59,13]
    ],
    0x2194: [  // LEFT RIGHT ARROW
      [7,5,1],[8,5,0],[10,7,1],[12,7,1],[14,9,1],[16,9,0],[19,11,1],[22,13,1],
      [27,15,1],[32,18,1],[37,22,1],[44,25,1],[53,30,1],[63,35,1]
    ],
    0x2195: [  // UP DOWN ARROW
      [4,8,2],[5,10,3],[5,11,3],[6,14,4],[7,15,4],[9,18,5],[10,22,6],[12,25,7],
      [14,30,8],[16,35,9],[19,42,11],[23,49,13],[27,59,16],[32,69,18]
    ],
    0x2196: [  // NORTH WEST ARROW
      [7,7,2],[8,8,2],[10,10,2],[12,12,3],[14,13,3],[16,16,4],[19,19,4],[22,22,5],
      [27,26,6],[32,31,7],[38,37,8],[45,44,10],[53,52,12],[63,61,13]
    ],
    0x2197: [  // NORTH EAST ARROW
      [7,7,2],[9,8,2],[10,10,2],[12,12,3],[14,13,3],[16,16,4],[19,19,4],[23,22,5],
      [27,26,6],[32,31,7],[38,37,8],[46,44,10],[54,52,11],[64,61,13]
    ],
    0x2198: [  // SOUTH EAST ARROW
      [7,7,2],[9,8,2],[10,10,3],[12,12,3],[14,13,3],[17,16,4],[19,19,5],[23,23,6],
      [27,26,6],[32,31,8],[38,37,9],[46,44,11],[54,52,13],[64,61,15]
    ],
    0x2199: [  // SOUTH WEST ARROW
      [7,7,2],[8,8,2],[10,10,3],[12,12,3],[14,13,3],[16,16,4],[19,19,5],[22,23,6],
      [26,26,6],[32,31,8],[37,37,9],[44,44,11],[53,52,13],[63,61,15]
    ],
    0x21A6: [  // RIGHTWARDS ARROW FROM BAR
      [7,5,1],[8,5,0],[10,7,1],[11,7,1],[14,9,1],[16,9,0],[19,11,1],[22,13,1],
      [27,15,1],[32,18,1],[37,22,1],[44,25,1],[53,30,1],[63,35,1]
    ],
    0x21A9: [  // LEFTWARDS ARROW WITH HOOK
      [8,5,1],[9,5,0],[11,7,1],[13,7,1],[15,9,1],[18,9,0],[21,11,1],[25,13,1],
      [30,15,1],[36,18,1],[42,21,1],[50,25,1],[60,30,1],[71,35,1]
    ],
    0x21AA: [  // RIGHTWARDS ARROW WITH HOOK
      [8,5,1],[9,5,0],[11,7,1],[13,7,1],[15,9,1],[18,9,0],[21,11,1],[25,13,1],
      [30,15,1],[36,18,1],[42,22,1],[50,25,1],[60,30,1],[71,35,1]
    ],
    0x21BC: [  // LEFTWARDS HARPOON WITH BARB UPWARDS
      [7,3,-1],[8,3,-2],[10,4,-2],[12,4,-2],[14,5,-3],[16,5,-4],[19,6,-4],[22,7,-5],
      [27,8,-6],[32,10,-7],[37,11,-9],[44,14,-10],[53,17,-12],[63,19,-15]
    ],
    0x21BD: [  // LEFTWARDS HARPOON WITH BARB DOWNWARDS
      [7,3,1],[8,3,0],[10,4,1],[12,4,1],[14,5,1],[16,5,0],[19,7,1],[22,8,1],
      [27,9,1],[32,10,1],[37,12,1],[44,14,1],[53,16,1],[63,19,1]
    ],
    0x21C0: [  // RIGHTWARDS HARPOON WITH BARB UPWARDS
      [7,3,-1],[8,3,-2],[10,4,-2],[12,4,-2],[14,5,-3],[16,5,-4],[19,6,-4],[22,7,-5],
      [27,8,-6],[32,10,-7],[37,11,-9],[44,14,-10],[53,17,-12],[63,19,-15]
    ],
    0x21C1: [  // RIGHTWARDS HARPOON WITH BARB DOWNWARDS
      [7,3,1],[8,3,0],[10,4,1],[12,4,1],[14,5,1],[16,5,0],[19,7,1],[22,8,1],
      [27,9,1],[32,10,1],[37,12,1],[44,14,1],[53,16,1],[63,19,1]
    ],
    0x21CC: [  // RIGHTWARDS HARPOON OVER LEFTWARDS HARPOON
      [7,6,1],[8,6,0],[10,8,1],[12,9,1],[14,11,1],[16,11,0],[19,14,1],[22,17,1],
      [27,20,1],[32,24,1],[37,28,1],[44,33,1],[53,39,1],[63,45,1]
    ],
    0x21D0: [  // LEFTWARDS DOUBLE ARROW
      [7,5,1],[8,6,1],[10,7,1],[12,8,1],[14,9,1],[16,10,1],[19,11,1],[22,14,1],
      [27,16,1],[32,20,2],[37,23,2],[44,27,2],[53,31,2],[63,38,3]
    ],
    0x21D1: [  // UPWARDS DOUBLE ARROW
      [4,7,2],[5,8,2],[6,9,2],[7,12,3],[8,13,3],[10,16,4],[12,18,4],[14,22,5],
      [17,26,6],[20,30,7],[23,36,8],[28,43,10],[32,50,11],[38,59,13]
    ],
    0x21D2: [  // RIGHTWARDS DOUBLE ARROW
      [7,5,1],[8,6,1],[10,7,1],[12,8,1],[14,9,1],[16,10,1],[19,12,1],[22,14,1],
      [27,16,1],[32,19,1],[37,22,1],[44,26,1],[53,32,2],[63,37,2]
    ],
    0x21D3: [  // DOWNWARDS DOUBLE ARROW
      [5,7,2],[5,8,2],[6,9,2],[7,12,3],[8,13,3],[10,16,4],[12,18,4],[14,22,5],
      [17,26,6],[20,30,7],[23,36,8],[28,43,10],[32,50,11],[39,59,13]
    ],
    0x21D4: [  // LEFT RIGHT DOUBLE ARROW
      [7,5,1],[9,6,1],[10,7,1],[12,8,1],[14,9,1],[17,10,1],[19,12,1],[23,14,1],
      [27,16,1],[32,19,1],[38,23,2],[45,27,2],[54,31,2],[64,37,2]
    ],
    0x21D5: [  // UP DOWN DOUBLE ARROW
      [4,8,2],[5,10,3],[6,11,3],[7,14,4],[8,15,4],[10,18,5],[12,22,6],[14,26,7],
      [17,30,8],[20,35,9],[23,42,11],[28,50,13],[32,58,15],[39,70,18]
    ]
  }
});

MathJax.Ajax.loadComplete(MathJax.OutputJax["HTML-CSS"].imgDir+"/Main/Regular"+
                          MathJax.OutputJax["HTML-CSS"].imgPacked+"/Arrows.js");
