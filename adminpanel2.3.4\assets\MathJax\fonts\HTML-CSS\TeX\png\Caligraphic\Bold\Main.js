/*
 *  /MathJax/fonts/HTML-CSS/TeX/png/Caligraphic/Bold/Main.js
 *  
 *  Copyright (c) 2010-2013 The MathJax Consortium
 *
 *  Part of the MathJax library.
 *  See http://www.mathjax.org for details.
 * 
 *  Licensed under the Apache License, Version 2.0;
 *  you may not use this file except in compliance with the License.
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 */

MathJax.OutputJax["HTML-CSS"].defineImageData({"MathJax_Caligraphic-bold":{32:[[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0]],48:[[4,4,0],[5,4,0],[6,5,0],[7,6,0],[8,7,0],[9,8,0],[11,9,0],[13,12,0],[15,14,0],[18,16,1],[21,19,1],[25,22,1],[30,26,1],[35,32,1]],49:[[4,3,0],[5,4,0],[5,5,0],[6,6,0],[7,7,0],[9,8,0],[10,9,0],[12,11,0],[14,13,0],[17,15,0],[20,18,0],[23,21,0],[28,25,0],[33,30,0]],50:[[4,3,0],[5,4,0],[6,5,0],[7,6,0],[8,6,0],[9,8,0],[11,9,0],[12,12,0],[15,13,0],[17,15,0],[21,18,0],[24,21,0],[29,26,0],[34,30,0]],51:[[4,5,2],[5,6,2],[6,7,2],[7,8,3],[8,9,3],[9,11,3],[11,14,5],[13,17,5],[15,19,6],[18,22,7],[21,27,8],[25,31,10],[30,37,11],[35,44,13]],52:[[4,6,2],[5,6,2],[6,7,2],[7,8,2],[8,10,3],[9,11,3],[11,14,5],[13,16,5],[15,18,5],[18,22,6],[22,26,7],[26,31,9],[30,37,10],[36,44,12]],53:[[4,5,2],[5,6,2],[6,7,2],[7,8,3],[8,10,3],[9,12,3],[11,14,5],[12,17,6],[15,19,6],[18,23,7],[21,27,8],[25,32,10],[29,38,12],[35,45,14]],54:[[4,5,0],[5,6,0],[6,7,0],[7,8,0],[8,9,0],[9,11,0],[11,13,0],[13,17,0],[15,20,0],[18,22,1],[21,27,1],[25,32,1],[30,37,1],[35,45,1]],55:[[4,6,2],[5,7,2],[6,8,3],[7,8,2],[8,10,3],[10,12,4],[11,14,4],[13,16,5],[16,20,7],[19,23,7],[22,27,8],[26,32,10],[31,38,12],[37,45,14]],56:[[4,5,0],[5,6,0],[6,7,0],[7,8,0],[8,9,0],[9,11,0],[11,13,0],[13,17,0],[15,19,0],[18,22,1],[21,27,1],[25,32,1],[30,38,1],[35,45,1]],57:[[4,5,2],[5,6,2],[6,7,2],[7,8,3],[8,9,3],[9,11,3],[11,14,5],[13,17,6],[15,19,6],[18,22,7],[21,27,8],[25,31,10],[30,37,12],[35,44,14]],65:[[7,6,0],[9,7,0],[10,9,1],[12,10,0],[14,12,1],[17,14,1],[20,17,1],[23,19,1],[28,23,1],[33,27,2],[39,32,2],[46,37,2],[55,44,3],[66,52,3]],66:[[6,5,0],[7,6,0],[8,7,0],[9,8,0],[11,10,0],[13,12,0],[15,14,0],[18,16,0],[21,19,0],[25,24,1],[29,28,1],[35,34,1],[41,40,1],[49,47,1]],67:[[5,5,0],[5,6,0],[6,7,0],[8,8,0],[9,10,0],[10,12,0],[12,14,0],[14,16,0],[17,19,0],[20,24,1],[24,28,1],[28,34,1],[34,40,1],[40,47,1]],68:[[7,5,0],[8,6,0],[9,7,0],[11,8,0],[13,10,0],[15,12,0],[18,14,0],[21,16,0],[25,19,0],[30,24,1],[35,27,1],[42,32,1],[49,39,0],[59,46,0]],69:[[5,5,0],[6,6,0],[7,7,0],[8,8,0],[9,10,0],[11,12,0],[13,14,0],[15,16,0],[18,19,0],[21,24,1],[25,28,1],[30,34,1],[35,40,1],[42,47,1]],70:[[7,6,0],[8,7,0],[10,8,0],[11,9,0],[13,11,0],[16,13,0],[19,15,0],[22,17,0],[26,20,0],[31,24,1],[37,29,1],[44,34,1],[52,40,2],[62,48,2]],71:[[5,6,1],[6,7,1],[7,8,1],[8,10,2],[10,12,2],[12,14,2],[14,16,2],[16,19,3],[19,23,4],[23,27,4],[27,32,5],[32,38,5],[38,45,6],[45,54,8]],72:[[7,5,0],[8,6,0],[10,7,0],[12,8,0],[13,10,0],[16,12,0],[19,14,0],[22,17,1],[26,20,1],[31,24,1],[37,28,2],[44,34,2],[52,40,2],[62,48,3]],73:[[7,5,0],[8,6,0],[9,7,0],[10,8,0],[12,10,0],[14,12,0],[16,14,0],[19,16,0],[22,19,0],[26,23,0],[32,27,0],[37,32,0],[44,38,0],[52,45,0]],74:[[7,6,1],[8,7,1],[10,8,1],[11,10,2],[13,12,2],[16,13,1],[19,16,2],[22,19,3],[26,22,3],[31,27,4],[37,31,5],[44,37,5],[52,44,6],[62,53,8]],75:[[6,5,0],[7,6,0],[9,7,0],[10,8,0],[12,10,0],[14,12,0],[17,14,0],[20,16,0],[23,19,0],[28,24,1],[33,28,1],[39,34,1],[46,40,1],[55,47,1]],76:[[6,5,0],[7,6,0],[8,7,0],[9,8,0],[11,10,0],[13,12,0],[15,14,0],[18,16,0],[21,19,0],[25,24,1],[30,28,1],[35,34,1],[42,40,1],[50,47,1]],77:[[10,7,1],[12,7,0],[14,8,0],[16,10,1],[19,12,1],[23,14,1],[27,16,1],[32,18,1],[38,21,1],[45,26,2],[53,30,2],[63,36,2],[75,43,3],[89,50,3]],78:[[9,7,1],[11,9,1],[12,10,1],[14,11,1],[17,13,1],[20,15,1],[23,18,1],[27,21,2],[32,25,2],[38,30,2],[45,34,2],[54,42,2],[64,50,3],[75,59,4]],79:[[7,5,0],[8,6,0],[9,7,0],[11,8,0],[13,10,0],[15,12,0],[18,14,0],[21,16,0],[25,19,0],[30,24,1],[35,28,1],[41,34,1],[49,40,1],[59,47,1]],80:[[6,6,1],[8,7,1],[9,8,1],[10,9,1],[12,11,1],[15,13,1],[17,15,1],[20,18,2],[24,21,2],[28,25,2],[34,30,3],[40,35,3],[47,42,4],[56,50,4]],81:[[7,6,1],[8,7,1],[9,8,1],[11,11,3],[13,12,2],[16,14,2],[18,17,3],[22,20,4],[26,24,5],[30,28,5],[36,33,6],[43,39,6],[51,47,8],[60,56,10]],82:[[7,5,0],[9,6,0],[10,7,0],[12,8,0],[14,10,0],[17,12,0],[20,14,0],[23,16,0],[28,19,0],[33,23,1],[39,27,1],[46,33,1],[55,39,1],[65,46,1]],83:[[5,5,0],[6,6,0],[8,7,0],[9,8,0],[10,10,0],[12,12,0],[15,14,0],[17,16,0],[20,19,0],[24,24,1],[29,28,1],[34,34,1],[40,40,1],[48,47,1]],84:[[7,6,1],[8,7,1],[10,8,1],[12,9,1],[14,11,1],[16,13,1],[19,15,1],[22,18,2],[27,23,2],[32,27,3],[38,32,3],[44,37,3],[53,44,4],[63,52,5]],85:[[7,5,0],[8,6,0],[9,7,0],[10,8,0],[12,10,0],[14,12,0],[16,14,0],[19,16,0],[23,19,0],[27,24,1],[31,28,1],[37,33,1],[44,39,1],[52,47,1]],86:[[6,6,1],[7,7,1],[8,8,1],[10,9,1],[11,11,1],[13,13,1],[16,15,1],[18,18,2],[22,21,2],[26,25,2],[31,30,3],[36,36,4],[43,42,4],[51,50,5]],87:[[9,6,1],[10,7,1],[12,8,1],[15,10,2],[17,11,1],[21,13,1],[24,15,1],[28,18,2],[34,22,3],[40,26,3],[48,30,3],[57,36,4],[67,43,4],[80,51,5]],88:[[7,5,0],[8,6,0],[9,7,0],[11,8,0],[13,10,0],[16,12,0],[18,14,0],[22,16,0],[26,19,0],[30,23,0],[36,27,0],[43,32,0],[51,39,1],[60,46,0]],89:[[6,6,1],[7,8,2],[8,9,2],[10,10,2],[12,12,2],[14,15,3],[16,17,3],[19,20,4],[23,24,5],[27,28,5],[32,33,6],[38,40,8],[45,47,9],[53,56,11]],90:[[6,5,0],[8,6,0],[9,7,0],[10,8,0],[12,10,0],[15,12,0],[17,14,0],[20,16,0],[24,19,0],[28,23,0],[34,27,0],[40,32,0],[47,38,0],[56,45,0]],160:[[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0]]}});MathJax.Ajax.loadComplete(MathJax.OutputJax["HTML-CSS"].imgDir+"/Caligraphic/Bold"+MathJax.OutputJax["HTML-CSS"].imgPacked+"/Main.js");

