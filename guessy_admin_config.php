<?php
/**
 * Guessy Admin Panel Configuration Script
 * This script configures the admin panel for Guessy application
 */

// Database configuration for Guessy
$guessy_db_config = '<?php
defined(\'BASEPATH\') OR exit(\'No direct script access allowed\');

$active_group = \'default\';
$query_builder = TRUE;

$db[\'default\'] = array(
    \'dsn\' => \'\',
    \'hostname\' => \'localhost\',
    \'username\' => \'root\',
    \'password\' => \'\',  // Usually empty for XAMPP
    \'database\' => \'guessy_db\',
    \'dbdriver\' => \'mysqli\',
    \'dbprefix\' => \'\',
    \'pconnect\' => FALSE,
    \'db_debug\' => (ENVIRONMENT !== \'production\'),
    \'cache_on\' => FALSE,
    \'cachedir\' => \'\',
    \'char_set\' => \'utf8mb4\',
    \'dbcollat\' => \'utf8mb4_unicode_ci\',
    \'swap_pre\' => \'\',
    \'encrypt\' => FALSE,
    \'compress\' => FALSE,
    \'stricton\' => FALSE,
    \'failover\' => array(),
    \'save_queries\' => TRUE
);
';

// Config.php configuration for Guessy
$guessy_config = '<?php
defined(\'BASEPATH\') or exit(\'No direct script access allowed\');

// Base URL for Guessy admin panel
$config[\'base_url\'] = \'http://localhost/guessy_admin/\';

$config[\'index_page\'] = \'\';
$config[\'uri_protocol\'] = \'REQUEST_URI\';
$config[\'url_suffix\'] = \'\';
$config[\'language\'] = \'english\';
$config[\'charset\'] = \'UTF-8\';
$config[\'enable_hooks\'] = FALSE;
$config[\'subclass_prefix\'] = \'MY_\';
$config[\'composer_autoload\'] = FALSE;
$config[\'permitted_uri_chars\'] = \'a-z 0-9~%.:_\-\';
$config[\'enable_query_strings\'] = FALSE;
$config[\'controller_trigger\'] = \'c\';
$config[\'function_trigger\'] = \'m\';
$config[\'directory_trigger\'] = \'d\';
$config[\'allow_get_array\'] = TRUE;
$config[\'log_threshold\'] = 0;
$config[\'log_path\'] = \'\';
$config[\'log_file_extension\'] = \'\';
$config[\'log_file_permissions\'] = 0644;
$config[\'log_date_format\'] = \'Y-m-d H:i:s\';
$config[\'error_views_path\'] = \'\';
$config[\'cache_path\'] = \'\';
$config[\'cache_query_string\'] = FALSE;
$config[\'encryption_key\'] = \'guessy_secret_key_2024_secure_admin\';
$config[\'sess_driver\'] = \'files\';
$config[\'sess_cookie_name\'] = \'guessy_session\';
$config[\'sess_expiration\'] = 7200;
$config[\'sess_save_path\'] = NULL;
$config[\'sess_match_ip\'] = FALSE;
$config[\'sess_time_to_update\'] = 300;
$config[\'sess_regenerate_destroy\'] = FALSE;
$config[\'cookie_prefix\'] = \'guessy_\';
$config[\'cookie_domain\'] = \'\';
$config[\'cookie_path\'] = \'/\';
$config[\'cookie_secure\'] = FALSE;
$config[\'cookie_httponly\'] = FALSE;
$config[\'standardize_newlines\'] = FALSE;
$config[\'global_xss_filtering\'] = FALSE;
$config[\'csrf_protection\'] = FALSE;
$config[\'csrf_token_name\'] = \'csrf_test_name\';
$config[\'csrf_cookie_name\'] = \'csrf_cookie_name\';
$config[\'csrf_expire\'] = 7200;
$config[\'csrf_regenerate\'] = TRUE;
$config[\'csrf_exclude_uris\'] = array();
$config[\'compress_output\'] = FALSE;
$config[\'time_reference\'] = \'local\';
$config[\'rewrite_short_tags\'] = FALSE;
$config[\'proxy_ips\'] = \'\';

// CORS headers for Guessy API access
header(\'Access-Control-Allow-Origin: *\');
header(\'Access-Control-Allow-Methods: GET, POST, OPTIONS\');
header(\'Access-Control-Allow-Headers: Content-Type, Authorization\');

// Guessy specific configurations
define(\'GUESSY_APP_NAME\', \'Guessy\');
define(\'GUESSY_VERSION\', \'2.3.4\');
define(\'GUESSY_PACKAGE\', \'com.guessy.quiz\');
';

// Function to setup Guessy configuration
function setupGuessyConfig() {
    global $guessy_db_config, $guessy_config;
    
    $config_dir = 'adminpanel2.3.4/application/config/';
    
    // Create backup of original files
    if (file_exists($config_dir . 'database.php')) {
        copy($config_dir . 'database.php', $config_dir . 'database.php.backup');
    }
    if (file_exists($config_dir . 'config.php')) {
        copy($config_dir . 'config.php', $config_dir . 'config.php.backup');
    }
    
    // Write new Guessy configuration files
    file_put_contents($config_dir . 'database.php', $guessy_db_config);
    file_put_contents($config_dir . 'config.php', $guessy_config);
    
    echo "✅ Guessy admin panel configuration files created successfully!\n";
    echo "📁 Database config: {$config_dir}database.php\n";
    echo "📁 Main config: {$config_dir}config.php\n";
    echo "💾 Original files backed up with .backup extension\n";
}

// Function to create Guessy .htaccess
function createGuessyHtaccess() {
    $htaccess_content = 'RewriteEngine On
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule ^(.*)$ index.php/$1 [L]

# Security headers for Guessy
Header always set X-Content-Type-Options nosniff
Header always set X-Frame-Options DENY
Header always set X-XSS-Protection "1; mode=block"

# CORS headers for Guessy API
Header always set Access-Control-Allow-Origin "*"
Header always set Access-Control-Allow-Methods "GET, POST, OPTIONS"
Header always set Access-Control-Allow-Headers "Content-Type, Authorization"

# Handle preflight requests
RewriteCond %{REQUEST_METHOD} OPTIONS
RewriteRule ^(.*)$ $1 [R=200,L]

# Guessy specific rules
<Files "*.php">
    Header set X-Powered-By "Guessy Admin Panel"
</Files>
';
    
    file_put_contents('adminpanel2.3.4/.htaccess', $htaccess_content);
    echo "✅ Guessy .htaccess file created for URL rewriting and CORS\n";
}

// Function to create Guessy environment file
function createGuessyEnv() {
    $env_content = '# Guessy Local Development Environment
# Copy this to .env and update values as needed

# Database Configuration
DB_HOST=localhost
DB_USERNAME=root
DB_PASSWORD=
DB_NAME=guessy_db

# Application Configuration
APP_ENV=development
APP_DEBUG=true
APP_NAME=Guessy
APP_URL=http://localhost/guessy_admin
APP_PACKAGE=com.guessy.quiz

# Firebase Configuration (get from Firebase Console)
FIREBASE_API_KEY=your_firebase_api_key
FIREBASE_AUTH_DOMAIN=guessy-quiz-app.firebaseapp.com
FIREBASE_PROJECT_ID=guessy-quiz-app
FIREBASE_STORAGE_BUCKET=guessy-quiz-app.appspot.com
FIREBASE_MESSAGING_SENDER_ID=your_sender_id
FIREBASE_APP_ID=your_app_id

# FCM Server Key (for push notifications)
FCM_SERVER_KEY=your_fcm_server_key

# Admin Credentials
ADMIN_USERNAME=guessy_admin
ADMIN_PASSWORD=guessy123

# Security
ENCRYPTION_KEY=guessy_secret_key_2024_secure_admin
SESSION_EXPIRE=7200

# File Upload Settings
MAX_FILE_SIZE=5MB
ALLOWED_EXTENSIONS=jpg,jpeg,png,gif,svg

# API Settings
API_RATE_LIMIT=1000
API_TIMEOUT=30

# Guessy Specific Settings
GUESSY_THEME_COLOR=#6C63FF
GUESSY_SECONDARY_COLOR=#4CAF50
GUESSY_ACCENT_COLOR=#FF9800
DEFAULT_COINS=100
DAILY_QUIZ_REWARD=20
BATTLE_WIN_REWARD=15
';
    
    file_put_contents('.env.guessy', $env_content);
    echo "✅ Guessy environment file created: .env.guessy\n";
    echo "📝 Copy this to .env and update with your actual values\n";
}

// Function to update admin panel branding
function updateAdminPanelBranding() {
    $views_dir = 'adminpanel2.3.4/application/views/';
    
    // Update header template if exists
    $header_file = $views_dir . 'header.php';
    if (file_exists($header_file)) {
        $header_content = file_get_contents($header_file);
        $header_content = str_replace('Elite Quiz', 'Guessy', $header_content);
        $header_content = str_replace('elite-quiz', 'guessy', $header_content);
        file_put_contents($header_file, $header_content);
        echo "✅ Updated admin panel header branding\n";
    }
    
    // Update login page if exists
    $login_file = $views_dir . 'login.php';
    if (file_exists($login_file)) {
        $login_content = file_get_contents($login_file);
        $login_content = str_replace('Elite Quiz', 'Guessy', $login_content);
        $login_content = str_replace('elite-quiz', 'guessy', $login_content);
        file_put_contents($login_file, $login_content);
        echo "✅ Updated admin panel login page branding\n";
    }
}

// Main setup function for Guessy
function runGuessySetup() {
    echo "🎯 Setting up Guessy Admin Panel for Local Development\n";
    echo "=" . str_repeat("=", 60) . "\n\n";
    
    // Check if admin panel directory exists
    if (!is_dir('adminpanel2.3.4')) {
        echo "❌ Error: adminpanel2.3.4 directory not found!\n";
        echo "Please make sure you're running this script from the project root.\n";
        return;
    }
    
    // Setup configuration files
    setupGuessyConfig();
    
    // Create .htaccess
    createGuessyHtaccess();
    
    // Create environment file
    createGuessyEnv();
    
    // Update branding
    updateAdminPanelBranding();
    
    echo "\n" . str_repeat("=", 60) . "\n";
    echo "🎉 Guessy admin panel setup completed successfully!\n\n";
    
    echo "📋 Next Steps:\n";
    echo "1. Start XAMPP and ensure Apache + MySQL are running\n";
    echo "2. Create database 'guessy_db' in phpMyAdmin\n";
    echo "3. Import database schema from adminpanel2.3.4/install/assets/quiz.php\n";
    echo "4. Run Guessy database setup: mysql -u root guessy_db < guessy_database_setup.sql\n";
    echo "5. Copy admin panel to XAMPP htdocs: cp -r adminpanel2.3.4 C:/xampp/htdocs/guessy_admin\n";
    echo "6. Access admin panel: http://localhost/guessy_admin\n";
    echo "7. Login with: guessy_admin / guessy123 (or admin / admin123)\n";
    echo "8. Configure Flutter app to use Guessy server\n\n";
    
    echo "🔧 Guessy Configuration Details:\n";
    echo "- Database: guessy_db\n";
    echo "- Admin URL: http://localhost/guessy_admin\n";
    echo "- API Base: http://localhost/guessy_admin/Api\n";
    echo "- Package: com.guessy.quiz\n";
    echo "- App Name: Guessy\n\n";
    
    echo "⚠️  Important Notes:\n";
    echo "- Original config files are backed up with .backup extension\n";
    echo "- Update Firebase configuration for Guessy project\n";
    echo "- Use ******** for Android emulator API calls\n";
    echo "- Use localhost for iOS simulator API calls\n";
    echo "- Use your computer's IP for physical device testing\n";
}

// Run the setup if script is executed directly
if (php_sapi_name() === 'cli') {
    runGuessySetup();
} else {
    echo "<pre>";
    runGuessySetup();
    echo "</pre>";
}
?>
