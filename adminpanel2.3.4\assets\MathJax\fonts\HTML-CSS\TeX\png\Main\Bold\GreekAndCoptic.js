/*
 *  /MathJax/fonts/HTML-CSS/TeX/png/Main/Bold/GreekAndCoptic.js
 *  
 *  Copyright (c) 2010-2013 The MathJax Consortium
 *
 *  Part of the MathJax library.
 *  See http://www.mathjax.org for details.
 * 
 *  Licensed under the Apache License, Version 2.0;
 *  you may not use this file except in compliance with the License.
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 */

MathJax.OutputJax["HTML-CSS"].defineImageData({"MathJax_Main-bold":{915:[[5,5,0],[6,6,0],[7,8,0],[8,8,0],[9,9,0],[11,12,0],[13,14,0],[15,17,0],[18,20,0],[22,23,0],[26,27,0],[30,32,0],[36,38,0],[43,46,0]],916:[[7,5,0],[8,6,0],[9,8,0],[11,8,0],[13,9,0],[15,12,0],[18,14,0],[21,17,0],[25,20,0],[30,23,0],[36,28,0],[42,32,0],[50,38,0],[60,46,0]],920:[[6,5,0],[7,6,0],[9,8,0],[10,8,0],[12,9,0],[14,12,0],[17,14,0],[20,17,0],[23,20,0],[28,23,0],[33,28,0],[39,32,0],[46,38,0],[55,46,0]],923:[[6,5,0],[7,6,0],[8,8,0],[9,8,0],[11,9,0],[13,12,0],[15,14,0],[18,17,0],[22,20,0],[26,23,0],[30,28,0],[36,32,0],[43,38,0],[51,46,0]],926:[[5,5,0],[6,6,0],[8,8,0],[9,8,0],[10,9,0],[12,12,0],[14,14,0],[17,17,0],[20,20,0],[24,23,0],[28,27,0],[34,31,0],[40,37,0],[48,45,0]],928:[[6,5,0],[8,6,0],[9,8,0],[11,8,0],[12,9,0],[15,12,0],[17,14,0],[20,17,0],[24,20,0],[29,23,0],[34,27,0],[40,32,0],[48,38,0],[57,46,0]],931:[[6,5,0],[7,6,0],[8,8,0],[9,8,0],[11,9,0],[13,12,0],[15,14,0],[18,17,0],[22,20,0],[26,23,0],[30,28,0],[36,32,0],[43,38,0],[51,46,0]],933:[[6,5,0],[7,6,0],[9,8,0],[10,8,0],[12,9,0],[14,12,0],[17,14,0],[20,17,0],[23,20,0],[28,23,0],[33,28,0],[39,32,0],[46,38,0],[55,46,0]],934:[[6,5,0],[7,6,0],[8,8,0],[9,8,0],[11,9,0],[13,12,0],[15,14,0],[18,17,0],[22,20,0],[26,23,0],[30,28,0],[36,32,0],[43,38,0],[51,46,0]],936:[[6,5,0],[7,6,0],[9,8,0],[10,8,0],[12,9,0],[14,12,0],[17,14,0],[20,17,0],[23,20,0],[28,23,0],[33,28,0],[39,32,0],[46,38,0],[55,46,0]],937:[[6,5,0],[7,6,0],[8,8,0],[10,8,0],[11,9,0],[13,12,0],[16,14,0],[18,17,0],[22,20,0],[26,23,0],[31,28,0],[36,32,0],[43,38,0],[51,46,0]]}});MathJax.Ajax.loadComplete(MathJax.OutputJax["HTML-CSS"].imgDir+"/Main/Bold"+MathJax.OutputJax["HTML-CSS"].imgPacked+"/GreekAndCoptic.js");

