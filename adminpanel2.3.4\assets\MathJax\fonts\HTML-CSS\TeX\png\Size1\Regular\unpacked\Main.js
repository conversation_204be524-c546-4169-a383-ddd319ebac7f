/*************************************************************
 *
 *  MathJax/fonts/HTML-CSS/TeX/png/Size1/Regular/Main.js
 *  
 *  Defines the image size data needed for the HTML-CSS OutputJax
 *  to display mathematics using fallback images when the fonts
 *  are not available to the client browser.
 *
 *  ---------------------------------------------------------------------
 *
 *  Copyright (c) 2009-2013 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 *
 */

MathJax.OutputJax["HTML-CSS"].defineImageData({
  "MathJax_Size1": {
    0x20: [  // SPACE
      [1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],
      [1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0]
    ],
    0x28: [  // LEFT PARENTHESIS
      [3,9,2],[4,10,3],[5,12,3],[5,15,4],[6,17,5],[7,20,6],[9,24,7],[10,28,8],
      [12,34,10],[14,40,11],[17,47,14],[20,56,16],[24,67,19],[28,79,23]
    ],
    0x29: [  // RIGHT PARENTHESIS
      [3,9,2],[3,10,3],[3,12,3],[4,15,4],[5,17,5],[6,20,6],[6,24,7],[8,28,8],
      [9,34,10],[11,40,11],[12,47,14],[15,56,16],[17,67,19],[21,79,23]
    ],
    0x2F: [  // SOLIDUS
      [4,9,2],[5,10,3],[6,12,3],[7,14,4],[8,17,5],[9,20,6],[11,24,7],[13,28,8],
      [15,33,10],[18,40,11],[21,47,14],[25,56,16],[29,67,19],[35,79,23]
    ],
    0x5B: [  // LEFT SQUARE BRACKET
      [3,9,3],[4,11,3],[4,12,4],[5,15,4],[6,17,5],[7,20,6],[8,24,7],[10,28,8],
      [11,34,10],[13,41,12],[16,47,14],[19,56,16],[22,67,19],[26,79,23]
    ],
    0x5C: [  // REVERSE SOLIDUS
      [4,9,2],[5,10,3],[6,12,3],[7,14,4],[8,17,5],[9,20,6],[11,24,7],[13,28,8],
      [15,33,10],[18,40,11],[21,47,14],[25,56,16],[29,67,19],[35,79,23]
    ],
    0x5D: [  // RIGHT SQUARE BRACKET
      [2,9,3],[2,11,3],[3,12,4],[3,15,4],[3,17,5],[4,20,6],[5,24,7],[5,28,8],
      [6,34,10],[8,41,12],[9,47,14],[10,56,16],[12,67,19],[15,79,23]
    ],
    0x7B: [  // LEFT CURLY BRACKET
      [4,9,2],[4,10,3],[5,12,3],[6,15,4],[7,17,5],[8,20,6],[10,24,7],[12,28,8],
      [14,34,10],[16,40,12],[19,47,14],[23,56,16],[27,67,20],[32,79,23]
    ],
    0x7D: [  // RIGHT CURLY BRACKET
      [4,9,2],[4,10,3],[5,12,3],[6,15,4],[7,17,5],[8,20,6],[10,24,7],[12,28,8],
      [14,34,10],[16,40,11],[19,47,13],[23,56,16],[27,67,19],[32,79,23]
    ],
    0xA0: [  // NO-BREAK SPACE
      [1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],
      [1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0]
    ],
    0x2C6: [  // MODIFIER LETTER CIRCUMFLEX ACCENT
      [5,3,-3],[6,3,-4],[7,3,-5],[8,3,-6],[9,4,-7],[11,4,-9],[12,4,-10],[15,5,-13],
      [17,7,-14],[20,7,-17],[24,8,-21],[28,10,-25],[33,12,-30],[39,14,-36]
    ],
    0x2DC: [  // SMALL TILDE
      [4,1,-4],[5,2,-5],[6,2,-6],[7,2,-7],[8,2,-8],[10,3,-9],[11,4,-11],[13,4,-13],
      [16,4,-17],[19,5,-20],[22,5,-23],[26,6,-28],[31,7,-33],[37,9,-39]
    ],
    0x302: [  // COMBINING CIRCUMFLEX ACCENT
      [5,3,-3],[6,3,-4],[7,3,-5],[8,3,-6],[9,4,-7],[11,4,-9],[12,4,-10],[15,5,-13],
      [17,7,-14],[20,7,-17],[24,8,-21],[28,10,-25],[33,12,-30],[39,14,-36]
    ],
    0x303: [  // COMBINING TILDE
      [4,1,-4],[5,2,-5],[6,2,-6],[7,2,-7],[8,2,-8],[10,3,-9],[11,4,-11],[13,4,-13],
      [16,4,-17],[19,5,-20],[22,5,-23],[26,6,-28],[31,7,-33],[37,9,-39]
    ],
    0x2016: [  // DOUBLE VERTICAL LINE
      [4,5,0],[5,5,0],[6,6,0],[7,8,0],[8,9,0],[9,10,0],[11,12,0],[13,14,0],
      [15,17,0],[18,20,0],[21,24,0],[25,28,0],[29,34,0],[35,40,0]
    ],
    0x2191: [  // UPWARDS ARROW
      [4,5,0],[5,5,0],[6,6,0],[7,8,0],[8,9,0],[10,10,0],[11,12,0],[13,14,0],
      [16,17,0],[19,20,0],[22,24,0],[26,28,0],[31,34,0],[37,40,0]
    ],
    0x2193: [  // DOWNWARDS ARROW
      [4,5,0],[5,5,0],[6,6,0],[7,8,0],[8,9,0],[10,10,0],[11,12,0],[13,14,0],
      [16,17,0],[19,20,0],[22,24,0],[26,28,0],[31,34,0],[37,40,0]
    ],
    0x21D1: [  // UPWARDS DOUBLE ARROW
      [5,5,0],[7,5,0],[7,6,0],[9,8,0],[10,9,0],[13,10,0],[14,12,0],[17,14,0],
      [20,17,0],[24,20,0],[29,24,0],[34,28,0],[40,34,0],[48,40,0]
    ],
    0x21D3: [  // DOWNWARDS DOUBLE ARROW
      [5,5,0],[7,5,0],[7,6,0],[9,8,0],[10,9,0],[13,10,0],[14,12,0],[17,14,0],
      [20,17,0],[24,20,0],[29,24,0],[34,28,0],[40,34,0],[48,40,0]
    ],
    0x220F: [  // N-ARY PRODUCT
      [7,8,2],[8,9,2],[9,11,3],[11,12,3],[13,15,4],[15,17,4],[18,20,5],[21,24,6],
      [25,28,7],[30,34,9],[35,40,10],[42,47,12],[50,56,14],[59,67,17]
    ],
    0x2210: [  // N-ARY COPRODUCT
      [7,8,2],[8,9,2],[9,11,3],[11,12,3],[13,15,4],[15,17,4],[18,20,5],[21,24,6],
      [25,28,7],[30,34,9],[35,40,10],[42,47,12],[50,56,14],[59,67,17]
    ],
    0x2211: [  // N-ARY SUMMATION
      [7,8,2],[9,9,2],[10,11,3],[12,12,3],[14,15,4],[17,18,5],[20,20,5],[24,24,6],
      [28,28,7],[33,34,9],[40,40,10],[47,47,12],[56,56,14],[66,67,17]
    ],
    0x221A: [  // SQUARE ROOT
      [8,9,3],[9,11,3],[11,13,4],[12,15,5],[15,17,5],[17,21,6],[20,24,7],[24,29,9],
      [29,34,10],[34,40,12],[40,48,14],[48,57,17],[57,67,20],[68,80,24]
    ],
    0x2223: [  // DIVIDES
      [2,6,1],[2,7,1],[2,8,1],[2,9,1],[3,10,1],[3,12,1],[4,14,1],[5,16,1],
      [6,19,1],[7,22,1],[8,26,1],[9,31,1],[11,36,1],[13,43,1]
    ],
    0x2225: [  // PARALLEL TO
      [3,6,1],[4,7,1],[4,8,1],[5,9,1],[6,10,1],[7,12,1],[8,14,1],[10,16,1],
      [12,19,1],[14,22,1],[17,26,1],[20,31,1],[23,36,1],[27,43,1]
    ],
    0x222B: [  // INTEGRAL
      [5,8,2],[5,10,3],[6,11,3],[7,14,4],[9,16,4],[11,19,5],[12,22,6],[15,26,7],
      [17,32,9],[21,38,11],[24,44,12],[29,53,15],[34,62,17],[41,74,21]
    ],
    0x222C: [  // DOUBLE INTEGRAL
      [7,8,2],[8,10,3],[10,11,3],[11,14,4],[14,16,4],[16,19,5],[19,22,6],[23,26,7],
      [27,32,9],[32,38,11],[38,44,12],[45,53,15],[53,62,17],[64,74,21]
    ],
    0x222D: [  // TRIPLE INTEGRAL
      [9,8,2],[11,10,3],[13,11,3],[15,14,4],[19,16,4],[22,19,5],[26,22,6],[31,26,7],
      [37,32,9],[43,38,11],[52,44,12],[61,53,15],[73,62,17],[86,74,21]
    ],
    0x222E: [  // CONTOUR INTEGRAL
      [5,8,2],[5,10,3],[6,11,3],[7,14,4],[9,16,4],[11,19,5],[12,22,6],[15,26,7],
      [17,32,9],[21,38,11],[24,44,12],[29,53,15],[34,62,17],[41,74,21]
    ],
    0x22C0: [  // N-ARY LOGICAL AND
      [6,8,2],[7,10,3],[8,11,3],[10,12,3],[11,15,4],[13,18,5],[16,20,5],[19,24,6],
      [22,28,7],[26,34,9],[31,40,10],[37,47,12],[43,56,14],[52,67,17]
    ],
    0x22C1: [  // N-ARY LOGICAL OR
      [6,8,2],[7,10,3],[8,11,3],[10,13,4],[11,15,4],[13,18,5],[16,21,6],[19,25,7],
      [22,29,8],[26,34,9],[31,41,11],[37,48,13],[43,57,15],[52,68,18]
    ],
    0x22C2: [  // N-ARY INTERSECTION
      [6,7,2],[7,10,3],[8,11,3],[10,12,3],[11,15,4],[13,18,5],[16,20,5],[18,25,7],
      [22,29,8],[26,34,9],[31,40,10],[36,47,12],[43,57,15],[51,67,17]
    ],
    0x22C3: [  // N-ARY UNION
      [6,8,2],[7,10,3],[8,11,3],[10,12,3],[11,15,4],[13,18,5],[16,20,5],[18,24,6],
      [22,28,7],[26,34,9],[31,40,10],[36,47,12],[43,56,14],[51,67,17]
    ],
    0x2308: [  // LEFT CEILING
      [4,9,3],[4,10,3],[5,13,4],[6,15,5],[7,17,5],[8,20,6],[9,24,7],[11,28,8],
      [13,34,10],[15,40,12],[18,48,14],[21,56,16],[25,67,20],[30,79,23]
    ],
    0x2309: [  // RIGHT CEILING
      [2,9,3],[3,10,3],[3,13,4],[3,15,5],[4,17,5],[5,20,6],[6,24,7],[7,28,8],
      [8,34,10],[9,40,12],[11,48,14],[13,56,16],[15,67,20],[18,79,23]
    ],
    0x230A: [  // LEFT FLOOR
      [4,9,3],[4,11,3],[5,13,4],[6,15,4],[7,17,5],[8,21,6],[9,24,7],[11,28,8],
      [13,34,10],[15,41,12],[18,48,14],[21,57,17],[25,68,20],[30,79,23]
    ],
    0x230B: [  // RIGHT FLOOR
      [2,9,3],[3,11,3],[3,13,4],[3,15,4],[4,17,5],[5,21,6],[6,24,7],[7,28,8],
      [8,34,10],[9,41,12],[11,48,14],[13,57,17],[15,68,20],[18,79,23]
    ],
    0x23D0: [  // VERTICAL LINE EXTENSION (used to extend arrows)
      [3,5,0],[3,5,0],[4,6,0],[4,8,0],[5,9,0],[6,10,0],[7,12,0],[9,14,0],
      [10,17,0],[12,20,0],[14,24,0],[17,28,0],[20,34,0],[24,40,0]
    ],
    0x27E8: [  // MATHEMATICAL LEFT ANGLE BRACKET
      [3,9,3],[4,11,3],[4,13,4],[5,15,5],[6,17,5],[7,21,6],[8,24,7],[10,29,9],
      [11,34,10],[13,40,12],[16,48,14],[19,57,17],[22,67,20],[26,80,24]
    ],
    0x27E9: [  // MATHEMATICAL RIGHT ANGLE BRACKET
      [3,9,3],[4,11,3],[4,13,4],[5,15,5],[6,17,5],[7,21,6],[8,24,7],[9,29,9],
      [11,34,10],[13,40,12],[15,48,14],[18,57,17],[21,67,20],[25,80,24]
    ],
    0x2A00: [  // N-ARY CIRCLED DOT OPERATOR
      [8,8,2],[9,9,2],[11,11,3],[13,12,3],[15,15,4],[18,18,5],[21,20,5],[25,24,6],
      [30,28,7],[35,34,9],[42,40,10],[50,47,12],[59,56,14],[70,67,17]
    ],
    0x2A01: [  // N-ARY CIRCLED PLUS OPERATOR
      [8,8,2],[9,8,2],[11,11,3],[13,12,3],[15,15,4],[18,18,5],[21,20,5],[25,24,6],
      [30,28,7],[35,34,9],[42,40,10],[49,47,12],[59,56,14],[70,67,17]
    ],
    0x2A02: [  // N-ARY CIRCLED TIMES OPERATOR
      [8,8,2],[9,9,2],[11,11,3],[13,12,3],[15,15,4],[18,18,5],[21,20,5],[25,24,6],
      [30,28,7],[35,34,9],[42,40,10],[50,47,12],[59,56,14],[70,67,17]
    ],
    0x2A04: [  // N-ARY UNION OPERATOR WITH PLUS
      [6,8,2],[7,10,3],[8,11,3],[10,12,3],[11,15,4],[13,18,5],[16,20,5],[18,24,6],
      [22,28,7],[26,34,9],[31,40,10],[36,47,12],[43,56,14],[51,67,17]
    ],
    0x2A06: [  // N-ARY SQUARE UNION OPERATOR
      [6,8,2],[7,9,2],[8,11,3],[10,12,3],[11,15,4],[13,18,5],[16,20,5],[19,24,6],
      [22,28,7],[26,34,9],[31,40,10],[37,47,12],[43,56,14],[52,67,17]
    ]
  }
});

MathJax.Ajax.loadComplete(MathJax.OutputJax["HTML-CSS"].imgDir+"/Size1/Regular"+
                          MathJax.OutputJax["HTML-CSS"].imgPacked+"/Main.js");
