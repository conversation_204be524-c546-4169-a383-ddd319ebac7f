/*
 *  /MathJax/fonts/HTML-CSS/TeX/png/Typewriter/Regular/CombDiacritMarks.js
 *  
 *  Copyright (c) 2010-2013 The MathJax Consortium
 *
 *  Part of the MathJax library.
 *  See http://www.mathjax.org for details.
 * 
 *  Licensed under the Apache License, Version 2.0;
 *  you may not use this file except in compliance with the License.
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 */

MathJax.OutputJax["HTML-CSS"].defineImageData({MathJax_Typewriter:{768:[[2,1,-3],[3,1,-5],[4,1,-5],[3,2,-5],[4,2,-6],[4,2,-8],[5,3,-10],[6,3,-11],[7,4,-13],[8,5,-16],[9,5,-19],[10,6,-22],[13,7,-27],[15,9,-32]],769:[[3,1,-3],[3,1,-5],[3,1,-5],[3,2,-5],[4,2,-6],[5,2,-8],[5,3,-10],[6,3,-11],[7,4,-13],[8,5,-16],[9,5,-19],[11,6,-22],[13,7,-27],[15,9,-32]],770:[[3,1,-3],[4,2,-4],[5,2,-4],[5,2,-5],[5,2,-6],[7,3,-7],[8,3,-10],[9,4,-10],[10,4,-13],[12,5,-16],[14,6,-18],[17,7,-21],[19,9,-25],[23,10,-31]],771:[[4,1,-3],[4,1,-5],[5,1,-5],[5,2,-5],[6,3,-5],[7,3,-7],[8,3,-10],[9,3,-11],[11,4,-13],[13,5,-16],[15,6,-18],[17,7,-21],[21,8,-26],[24,10,-31]],772:[[4,1,-3],[4,1,-4],[5,1,-4],[6,1,-6],[6,2,-6],[7,3,-7],[8,2,-10],[10,2,-11],[11,3,-14],[13,3,-17],[16,3,-19],[18,4,-23],[21,5,-27],[26,6,-33]],774:[[4,1,-3],[4,1,-5],[5,1,-5],[6,1,-6],[6,2,-6],[7,3,-8],[8,2,-11],[10,3,-11],[11,3,-14],[13,4,-17],[15,5,-19],[18,5,-23],[21,6,-28],[25,7,-34]],776:[[3,1,-3],[4,1,-5],[4,1,-5],[4,2,-5],[5,2,-6],[7,2,-8],[7,2,-11],[8,3,-11],[10,3,-14],[11,4,-17],[13,4,-20],[16,5,-23],[19,6,-28],[22,7,-34]],778:[[2,1,-3],[2,1,-5],[3,1,-5],[3,2,-5],[3,2,-6],[3,2,-8],[4,3,-10],[4,3,-11],[5,3,-14],[7,5,-16],[7,5,-19],[8,6,-22],[10,7,-27],[12,8,-33]],780:[[3,1,-3],[4,1,-4],[5,1,-4],[5,2,-5],[5,2,-6],[7,2,-7],[8,3,-9],[8,3,-10],[10,4,-12],[12,5,-15],[14,5,-17],[16,6,-21],[19,7,-25],[23,9,-30]]}});MathJax.Ajax.loadComplete(MathJax.OutputJax["HTML-CSS"].imgDir+"/Typewriter/Regular"+MathJax.OutputJax["HTML-CSS"].imgPacked+"/CombDiacritMarks.js");

