/*************************************************************
 *
 *  MathJax/fonts/HTML-CSS/TeX/png/Main/Bold/Main.js
 *  
 *  Defines the image size data needed for the HTML-CSS OutputJax
 *  to display mathematics using fallback images when the fonts
 *  are not available to the client browser.
 *
 *  ---------------------------------------------------------------------
 *
 *  Copyright (c) 2009-2013 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 *
 */

MathJax.OutputJax["HTML-CSS"].defineImageData({
  "MathJax_Main-bold": {
    0x20: [  // SPACE
      [1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],
      [1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0]
    ],
    0x21: [  // EXCLAMATION MARK
      [2,5,0],[3,6,0],[3,8,0],[4,8,0],[4,9,0],[5,12,0],[6,14,0],[7,17,0],
      [8,20,0],[9,23,0],[11,28,0],[13,32,0],[15,39,0],[18,47,0]
    ],
    0x22: [  // QUOTATION MARK
      [4,3,-2],[5,4,-2],[5,4,-4],[6,4,-4],[7,5,-4],[9,7,-5],[10,8,-6],[12,9,-8],
      [14,11,-9],[17,13,-10],[20,15,-13],[23,17,-15],[28,20,-18],[33,25,-21]
    ],
    0x23: [  // NUMBER SIGN
      [7,6,1],[8,8,2],[9,10,2],[11,10,2],[13,12,3],[15,15,3],[18,18,4],[21,22,5],
      [25,26,6],[30,29,6],[35,36,8],[42,41,9],[50,49,11],[59,59,13]
    ],
    0x24: [  // DOLLAR SIGN
      [4,6,1],[5,8,1],[5,9,1],[6,9,1],[8,11,1],[9,15,1],[10,17,2],[12,20,1],
      [15,24,2],[17,27,2],[20,33,3],[24,38,3],[29,45,3],[34,54,4]
    ],
    0x25: [  // PERCENT SIGN
      [7,6,1],[8,8,1],[9,9,1],[11,9,1],[13,11,1],[15,14,1],[18,16,1],[21,21,2],
      [25,24,2],[30,27,2],[35,32,2],[42,38,3],[50,45,3],[59,54,4]
    ],
    0x26: [  // AMPERSAND
      [6,5,0],[7,6,0],[9,8,0],[10,8,0],[12,9,0],[14,12,0],[17,14,0],[20,17,0],
      [24,20,0],[28,23,0],[33,28,0],[39,32,0],[47,38,0],[55,46,0]
    ],
    0x27: [  // APOSTROPHE
      [2,3,-2],[3,4,-2],[3,4,-4],[4,4,-4],[4,5,-4],[5,7,-5],[6,8,-6],[7,9,-8],
      [8,11,-9],[9,13,-10],[11,15,-13],[13,17,-15],[15,20,-18],[18,25,-21]
    ],
    0x28: [  // LEFT PARENTHESIS
      [3,7,2],[4,9,2],[4,12,3],[5,12,3],[6,14,3],[7,18,4],[8,20,5],[9,25,6],
      [11,29,7],[13,34,8],[15,40,10],[18,47,12],[22,56,14],[25,67,17]
    ],
    0x29: [  // RIGHT PARENTHESIS
      [3,7,2],[3,9,2],[4,12,3],[5,12,3],[5,14,3],[6,18,4],[7,20,5],[8,25,6],
      [10,29,7],[12,34,8],[14,40,10],[16,47,12],[19,56,14],[23,67,17]
    ],
    0x2A: [  // ASTERISK
      [4,3,-2],[5,4,-3],[5,5,-3],[6,5,-3],[7,6,-4],[9,8,-5],[10,9,-6],[12,11,-7],
      [14,13,-9],[17,15,-10],[20,18,-12],[24,21,-14],[28,25,-17],[33,30,-20]
    ],
    0x2B: [  // PLUS SIGN
      [6,6,1],[7,7,1],[9,9,1],[10,9,1],[12,11,2],[14,14,2],[17,16,3],[20,19,3],
      [23,22,4],[28,26,4],[33,31,5],[39,36,6],[46,43,7],[55,51,9]
    ],
    0x2C: [  // COMMA
      [2,3,1],[3,4,2],[3,4,2],[4,4,2],[4,6,3],[5,7,3],[6,8,4],[6,10,5],
      [8,11,6],[9,12,6],[11,15,8],[12,17,9],[15,21,11],[17,25,13]
    ],
    0x2D: [  // HYPHEN-MINUS
      [3,1,-1],[3,1,-1],[4,3,-1],[4,3,-1],[5,3,-2],[6,2,-3],[7,3,-3],[8,3,-4],
      [9,4,-5],[11,4,-6],[13,5,-7],[15,6,-8],[18,7,-9],[21,8,-11]
    ],
    0x2E: [  // FULL STOP
      [2,2,0],[3,2,0],[3,2,0],[3,2,0],[4,3,0],[5,4,0],[5,4,0],[6,5,0],
      [7,5,0],[9,6,0],[10,7,0],[12,8,0],[14,10,0],[17,12,0]
    ],
    0x2F: [  // SOLIDUS
      [4,7,2],[5,9,2],[5,11,3],[6,11,3],[8,14,3],[9,18,4],[10,20,5],[12,25,6],
      [15,29,7],[17,33,8],[20,40,10],[24,47,12],[29,56,14],[34,67,17]
    ],
    0x30: [  // DIGIT ZERO
      [4,5,0],[5,6,0],[6,8,0],[7,8,0],[8,9,0],[9,12,0],[11,13,0],[13,17,0],
      [15,20,0],[18,22,0],[21,26,0],[25,31,0],[30,37,0],[35,44,0]
    ],
    0x31: [  // DIGIT ONE
      [4,5,0],[5,6,0],[5,7,0],[6,8,0],[7,9,0],[9,12,0],[10,13,0],[12,16,0],
      [14,19,0],[17,22,0],[20,26,0],[23,31,0],[28,36,0],[33,44,0]
    ],
    0x32: [  // DIGIT TWO
      [4,5,0],[5,6,0],[6,7,0],[7,7,0],[8,9,0],[9,12,0],[11,14,0],[12,16,0],
      [15,19,0],[18,22,0],[21,26,0],[25,30,0],[29,36,0],[35,44,0]
    ],
    0x33: [  // DIGIT THREE
      [4,5,0],[5,6,0],[6,8,0],[7,8,0],[8,9,0],[9,12,0],[11,13,0],[13,17,0],
      [15,19,0],[18,22,0],[21,27,0],[25,31,0],[30,37,0],[35,44,0]
    ],
    0x34: [  // DIGIT FOUR
      [4,5,0],[5,7,1],[6,8,1],[7,9,1],[8,10,1],[9,13,1],[11,14,1],[13,17,1],
      [15,20,1],[18,23,1],[22,27,1],[26,32,1],[30,37,1],[36,45,1]
    ],
    0x35: [  // DIGIT FIVE
      [4,5,0],[5,6,0],[6,8,0],[7,8,0],[8,9,0],[9,12,0],[11,14,0],[12,16,0],
      [15,19,0],[18,23,0],[21,27,0],[25,31,0],[29,37,0],[35,45,0]
    ],
    0x36: [  // DIGIT SIX
      [4,5,0],[5,6,0],[6,8,0],[7,8,0],[8,9,0],[9,12,0],[11,13,0],[13,17,0],
      [15,19,0],[18,22,0],[21,27,0],[25,31,0],[30,37,0],[35,44,0]
    ],
    0x37: [  // DIGIT SEVEN
      [4,5,0],[5,7,0],[6,8,0],[7,8,0],[8,9,0],[10,12,0],[11,14,0],[13,17,0],
      [16,20,0],[19,23,0],[22,27,0],[26,32,0],[31,38,0],[37,45,0]
    ],
    0x38: [  // DIGIT EIGHT
      [4,5,0],[5,6,0],[6,8,0],[7,8,0],[8,9,0],[9,12,0],[11,13,0],[13,17,0],
      [15,19,0],[18,22,0],[21,27,0],[25,31,0],[30,37,0],[35,44,0]
    ],
    0x39: [  // DIGIT NINE
      [4,5,0],[5,6,0],[6,8,0],[7,8,0],[8,9,0],[9,12,0],[11,13,0],[13,17,0],
      [15,20,0],[18,22,0],[21,27,0],[25,31,0],[30,37,0],[35,44,0]
    ],
    0x3A: [  // COLON
      [2,3,0],[3,4,0],[3,5,0],[3,5,0],[4,6,0],[5,8,0],[5,9,0],[6,11,0],
      [7,13,0],[9,15,0],[10,18,0],[12,21,0],[14,25,0],[17,30,0]
    ],
    0x3B: [  // SEMICOLON
      [2,4,1],[3,6,2],[3,7,2],[3,7,2],[4,9,3],[5,11,3],[5,13,4],[6,16,5],
      [7,19,6],[9,21,6],[10,26,8],[12,30,9],[14,36,11],[17,43,13]
    ],
    0x3C: [  // LESS-THAN SIGN
      [6,5,1],[7,6,1],[8,8,1],[10,8,1],[12,9,1],[14,12,1],[16,14,2],[19,17,2],
      [23,20,2],[27,23,3],[32,27,3],[38,32,4],[45,38,5],[53,45,6]
    ],
    0x3D: [  // EQUALS SIGN
      [6,2,0],[7,3,-1],[9,3,-1],[10,3,-1],[12,4,-1],[14,6,-1],[17,6,-1],[20,8,-2],
      [23,8,-3],[28,9,-4],[33,12,-4],[39,13,-5],[46,16,-6],[55,19,-7]
    ],
    0x3E: [  // GREATER-THAN SIGN
      [6,5,1],[7,6,1],[8,8,1],[10,8,1],[11,9,1],[14,12,1],[16,14,2],[19,17,2],
      [22,20,2],[27,23,3],[32,27,3],[37,32,4],[45,38,5],[53,45,6]
    ],
    0x3F: [  // QUESTION MARK
      [4,5,0],[4,6,0],[5,8,0],[6,8,0],[7,9,0],[8,12,0],[10,14,0],[11,17,0],
      [14,20,0],[16,23,0],[19,28,0],[23,32,0],[27,38,0],[32,46,0]
    ],
    0x40: [  // COMMERCIAL AT
      [6,5,0],[7,6,0],[9,8,0],[10,8,0],[12,9,0],[14,12,0],[17,14,0],[20,17,0],
      [23,20,0],[28,23,0],[33,28,0],[39,32,0],[46,38,0],[55,46,0]
    ],
    0x41: [  // LATIN CAPITAL LETTER A
      [6,5,0],[7,6,0],[9,8,0],[10,8,0],[12,9,0],[14,12,0],[17,14,0],[20,17,0],
      [23,20,0],[28,23,0],[33,28,0],[39,32,0],[46,38,0],[55,46,0]
    ],
    0x42: [  // LATIN CAPITAL LETTER B
      [6,5,0],[7,6,0],[8,8,0],[9,8,0],[11,9,0],[13,12,0],[15,14,0],[18,17,0],
      [21,20,0],[25,23,0],[30,28,0],[36,32,0],[42,38,0],[50,46,0]
    ],
    0x43: [  // LATIN CAPITAL LETTER C
      [6,5,0],[7,6,0],[8,8,0],[9,8,0],[11,9,0],[13,12,0],[15,14,0],[18,17,0],
      [22,20,0],[26,23,0],[30,28,0],[36,32,0],[43,38,0],[51,46,0]
    ],
    0x44: [  // LATIN CAPITAL LETTER D
      [6,5,0],[7,6,0],[9,8,0],[10,8,0],[12,9,0],[14,12,0],[16,14,0],[19,17,0],
      [23,20,0],[27,23,0],[32,28,0],[38,32,0],[46,38,0],[54,45,0]
    ],
    0x45: [  // LATIN CAPITAL LETTER E
      [5,5,0],[6,6,0],[8,8,0],[9,8,0],[10,9,0],[12,12,0],[15,14,0],[17,17,0],
      [20,20,0],[24,23,0],[29,28,0],[34,32,0],[40,38,0],[48,46,0]
    ],
    0x46: [  // LATIN CAPITAL LETTER F
      [5,5,0],[6,6,0],[7,8,0],[8,8,0],[10,9,0],[12,12,0],[14,14,0],[16,17,0],
      [19,20,0],[23,23,0],[27,28,0],[32,32,0],[38,38,0],[45,46,0]
    ],
    0x47: [  // LATIN CAPITAL LETTER G
      [6,5,0],[7,6,0],[9,8,0],[10,8,0],[12,9,0],[14,12,0],[17,14,0],[20,17,0],
      [24,20,0],[28,23,0],[34,28,0],[40,33,1],[47,39,1],[56,47,1]
    ],
    0x48: [  // LATIN CAPITAL LETTER H
      [6,5,0],[8,6,0],[9,8,0],[11,8,0],[12,9,0],[15,12,0],[17,14,0],[20,17,0],
      [24,20,0],[29,23,0],[34,28,0],[40,32,0],[48,38,0],[57,46,0]
    ],
    0x49: [  // LATIN CAPITAL LETTER I
      [3,5,0],[4,6,0],[5,8,0],[5,8,0],[6,9,0],[7,12,0],[8,14,0],[10,17,0],
      [12,20,0],[14,23,0],[17,28,0],[20,32,0],[23,38,0],[27,46,0]
    ],
    0x4A: [  // LATIN CAPITAL LETTER J
      [4,5,0],[5,6,0],[6,8,0],[7,8,0],[8,9,0],[9,12,0],[11,14,0],[13,17,0],
      [15,20,0],[18,23,0],[21,28,0],[25,32,0],[30,38,0],[35,46,0]
    ],
    0x4B: [  // LATIN CAPITAL LETTER K
      [6,5,0],[8,6,0],[9,8,0],[11,8,0],[12,9,0],[15,12,0],[17,14,0],[20,17,0],
      [24,20,0],[29,23,0],[34,28,0],[40,32,0],[48,38,0],[57,46,0]
    ],
    0x4C: [  // LATIN CAPITAL LETTER L
      [5,5,0],[6,6,0],[7,8,0],[8,8,0],[9,9,0],[11,12,0],[13,14,0],[16,17,0],
      [18,20,0],[22,23,0],[26,28,0],[31,32,0],[36,38,0],[43,46,0]
    ],
    0x4D: [  // LATIN CAPITAL LETTER M
      [8,6,0],[9,7,0],[11,9,0],[13,9,0],[15,10,0],[18,13,0],[21,15,0],[25,18,0],
      [29,21,0],[35,24,0],[42,29,0],[49,33,0],[58,39,0],[70,47,0]
    ],
    0x4E: [  // LATIN CAPITAL LETTER N
      [6,5,0],[8,6,0],[9,8,0],[11,8,0],[12,9,0],[15,12,0],[17,14,0],[20,17,0],
      [24,20,0],[29,23,0],[34,28,0],[40,32,0],[48,38,0],[57,46,0]
    ],
    0x4F: [  // LATIN CAPITAL LETTER O
      [6,5,0],[7,6,0],[8,8,0],[10,8,0],[12,9,0],[14,12,0],[16,14,0],[19,17,0],
      [23,20,0],[27,23,0],[32,28,0],[38,32,0],[45,38,0],[53,46,0]
    ],
    0x50: [  // LATIN CAPITAL LETTER P
      [5,5,0],[6,6,0],[8,8,0],[9,8,0],[10,9,0],[12,12,0],[15,14,0],[17,17,0],
      [20,20,0],[24,23,0],[29,28,0],[34,32,0],[40,38,0],[48,46,0]
    ],
    0x51: [  // LATIN CAPITAL LETTER Q
      [6,6,1],[7,8,2],[8,10,2],[10,10,2],[12,12,3],[14,15,3],[16,18,4],[19,22,5],
      [23,26,6],[27,29,6],[32,36,8],[38,41,9],[45,49,11],[53,59,13]
    ],
    0x52: [  // LATIN CAPITAL LETTER R
      [6,5,0],[8,6,0],[9,8,0],[11,8,0],[12,9,0],[15,12,0],[17,14,0],[20,17,0],
      [24,20,0],[29,23,0],[34,28,0],[40,32,0],[48,38,0],[57,46,0]
    ],
    0x53: [  // LATIN CAPITAL LETTER S
      [4,5,0],[5,6,0],[6,8,0],[7,8,0],[8,9,0],[10,12,0],[12,14,0],[14,17,0],
      [16,20,0],[19,23,0],[23,28,0],[27,32,0],[32,38,0],[38,46,0]
    ],
    0x54: [  // LATIN CAPITAL LETTER T
      [6,5,0],[7,6,0],[8,8,0],[9,8,0],[11,9,0],[13,12,0],[15,14,0],[18,17,0],
      [21,19,0],[25,23,0],[30,27,0],[36,31,0],[42,38,0],[50,45,0]
    ],
    0x55: [  // LATIN CAPITAL LETTER U
      [6,5,0],[8,6,0],[9,8,0],[10,8,0],[12,9,0],[15,12,0],[17,14,0],[20,17,0],
      [24,20,0],[28,23,0],[34,28,0],[40,32,0],[47,39,1],[56,47,1]
    ],
    0x56: [  // LATIN CAPITAL LETTER V
      [6,6,1],[7,7,1],[9,9,1],[10,9,1],[12,10,1],[14,13,1],[17,15,1],[20,18,1],
      [24,21,1],[28,24,1],[33,29,1],[40,33,1],[47,40,1],[56,47,1]
    ],
    0x57: [  // LATIN CAPITAL LETTER W
      [8,6,0],[10,7,0],[12,9,0],[14,9,0],[17,10,0],[20,13,0],[23,15,0],[27,18,0],
      [33,21,0],[39,24,0],[46,29,0],[54,33,0],[65,39,0],[77,47,0]
    ],
    0x58: [  // LATIN CAPITAL LETTER X
      [6,5,0],[7,6,0],[9,8,0],[10,8,0],[12,9,0],[14,12,0],[17,14,0],[20,17,0],
      [24,20,0],[28,23,0],[33,28,0],[39,32,0],[47,38,0],[55,46,0]
    ],
    0x59: [  // LATIN CAPITAL LETTER Y
      [6,5,0],[8,6,0],[9,8,0],[10,8,0],[12,9,0],[15,12,0],[17,14,0],[20,17,0],
      [24,20,0],[28,23,0],[34,28,0],[40,32,0],[47,38,0],[56,46,0]
    ],
    0x5A: [  // LATIN CAPITAL LETTER Z
      [5,5,0],[6,6,0],[7,8,0],[8,8,0],[9,9,0],[11,12,0],[13,14,0],[15,17,0],
      [18,20,0],[22,23,0],[26,28,0],[30,32,0],[36,38,0],[43,46,0]
    ],
    0x5B: [  // LEFT SQUARE BRACKET
      [3,7,2],[3,9,2],[3,11,3],[4,11,3],[5,13,3],[5,19,5],[6,22,6],[7,26,7],
      [9,29,7],[10,34,8],[12,40,10],[14,47,12],[17,56,14],[20,67,17]
    ],
    0x5C: [  // REVERSE SOLIDUS
      [4,7,2],[5,9,2],[5,11,3],[6,11,3],[8,14,3],[9,18,4],[10,20,5],[12,25,6],
      [15,29,7],[17,33,8],[20,40,10],[24,47,12],[29,56,14],[34,67,17]
    ],
    0x5D: [  // RIGHT SQUARE BRACKET
      [2,7,2],[2,9,2],[2,11,3],[3,11,3],[3,13,3],[4,19,5],[4,22,6],[5,26,7],
      [6,29,7],[7,34,8],[8,40,10],[9,47,12],[11,56,14],[13,67,17]
    ],
    0x5E: [  // CIRCUMFLEX ACCENT
      [4,2,-3],[4,2,-5],[5,2,-6],[6,2,-6],[7,3,-7],[8,4,-9],[9,4,-10],[11,5,-13],
      [13,5,-15],[15,6,-17],[18,7,-21],[21,9,-24],[25,10,-29],[30,12,-35]
    ],
    0x5F: [  // LOW LINE
      [4,2,1],[5,2,1],[6,2,1],[7,2,1],[8,2,1],[10,2,1],[12,3,2],[14,3,2],
      [16,3,2],[19,3,2],[23,3,2],[27,4,3],[32,4,3],[38,5,4]
    ],
    0x60: [  // GRAVE ACCENT
      [3,2,-3],[3,2,-4],[4,3,-5],[4,3,-5],[5,3,-6],[6,4,-8],[7,5,-9],[8,5,-12],
      [10,6,-14],[12,7,-16],[14,9,-19],[16,10,-23],[19,12,-27],[23,14,-33]
    ],
    0x61: [  // LATIN SMALL LETTER A
      [4,3,0],[5,4,0],[6,5,0],[7,5,0],[8,6,0],[10,8,0],[11,9,0],[13,11,0],
      [16,13,0],[19,15,0],[22,18,0],[26,21,0],[31,25,0],[37,30,0]
    ],
    0x62: [  // LATIN SMALL LETTER B
      [5,5,0],[5,6,0],[6,8,0],[8,8,0],[9,9,0],[10,12,0],[12,14,0],[14,17,0],
      [17,20,0],[20,23,0],[24,28,0],[28,32,0],[34,38,0],[40,46,0]
    ],
    0x63: [  // LATIN SMALL LETTER C
      [4,3,0],[4,4,0],[5,5,0],[6,5,0],[7,6,0],[8,8,0],[10,9,0],[12,11,0],
      [14,13,0],[16,15,0],[19,18,0],[23,21,0],[27,25,0],[32,30,0]
    ],
    0x64: [  // LATIN SMALL LETTER D
      [5,5,0],[6,6,0],[6,8,0],[8,8,0],[9,9,0],[11,12,0],[12,14,0],[15,17,0],
      [17,20,0],[20,23,0],[24,28,0],[29,32,0],[34,38,0],[40,46,0]
    ],
    0x65: [  // LATIN SMALL LETTER E
      [4,3,0],[5,4,0],[5,5,0],[6,5,0],[7,6,0],[9,8,0],[10,9,0],[12,11,0],
      [14,13,0],[17,15,0],[20,18,0],[23,21,0],[28,25,0],[33,30,0]
    ],
    0x66: [  // LATIN SMALL LETTER F
      [4,5,0],[4,6,0],[5,8,0],[6,8,0],[7,9,0],[8,12,0],[9,14,0],[11,17,0],
      [13,20,0],[15,23,0],[18,28,0],[21,32,0],[25,38,0],[30,46,0]
    ],
    0x67: [  // LATIN SMALL LETTER G
      [4,4,1],[5,6,2],[6,7,2],[7,7,2],[8,9,3],[10,11,3],[11,13,4],[13,16,5],
      [16,19,6],[19,21,6],[22,26,8],[26,30,9],[31,36,11],[37,43,13]
    ],
    0x68: [  // LATIN SMALL LETTER H
      [5,5,0],[6,6,0],[7,8,0],[8,8,0],[9,9,0],[11,12,0],[13,14,0],[15,17,0],
      [18,20,0],[21,23,0],[25,28,0],[29,32,0],[35,38,0],[41,46,0]
    ],
    0x69: [  // LATIN SMALL LETTER I
      [3,5,0],[3,6,0],[3,8,0],[4,8,0],[5,9,0],[5,12,0],[6,14,0],[7,17,0],
      [9,20,0],[10,23,0],[12,28,0],[14,32,0],[17,38,0],[20,46,0]
    ],
    0x6A: [  // LATIN SMALL LETTER J
      [3,6,1],[4,8,2],[4,10,2],[5,10,2],[5,12,3],[7,15,3],[8,18,4],[9,22,5],
      [10,26,6],[13,29,6],[14,36,8],[17,41,9],[20,49,11],[24,59,13]
    ],
    0x6B: [  // LATIN SMALL LETTER K
      [5,5,0],[5,6,0],[6,8,0],[7,8,0],[9,9,0],[10,12,0],[12,14,0],[14,17,0],
      [17,20,0],[20,23,0],[23,28,0],[28,32,0],[33,38,0],[39,46,0]
    ],
    0x6C: [  // LATIN SMALL LETTER L
      [3,5,0],[3,6,0],[3,8,0],[4,8,0],[5,9,0],[5,12,0],[6,14,0],[7,17,0],
      [9,20,0],[10,23,0],[12,28,0],[14,32,0],[17,38,0],[20,46,0]
    ],
    0x6D: [  // LATIN SMALL LETTER M
      [7,3,0],[8,4,0],[10,5,0],[12,5,0],[14,6,0],[16,8,0],[19,9,0],[22,11,0],
      [27,13,0],[31,15,0],[37,18,0],[44,21,0],[53,25,0],[62,30,0]
    ],
    0x6E: [  // LATIN SMALL LETTER N
      [5,3,0],[6,5,0],[7,5,0],[8,5,0],[9,6,0],[11,8,0],[13,9,0],[15,11,0],
      [18,13,0],[21,15,0],[25,18,0],[29,21,0],[35,25,0],[41,30,0]
    ],
    0x6F: [  // LATIN SMALL LETTER O
      [4,3,0],[5,4,0],[6,5,0],[7,5,0],[8,6,0],[9,8,0],[11,9,0],[13,11,0],
      [15,13,0],[18,15,0],[22,18,0],[26,21,0],[30,25,0],[36,30,0]
    ],
    0x70: [  // LATIN SMALL LETTER P
      [5,4,1],[5,6,2],[6,7,2],[7,7,2],[9,9,3],[10,11,3],[12,13,4],[14,16,5],
      [17,19,6],[20,21,6],[24,26,8],[28,30,9],[33,36,11],[40,43,13]
    ],
    0x71: [  // LATIN SMALL LETTER Q
      [5,4,1],[6,6,2],[6,7,2],[8,7,2],[9,9,3],[11,11,3],[12,13,4],[15,16,5],
      [17,19,6],[21,21,6],[24,26,8],[29,30,9],[34,36,11],[41,43,13]
    ],
    0x72: [  // LATIN SMALL LETTER R
      [4,3,0],[4,4,0],[5,5,0],[6,5,0],[7,6,0],[8,8,0],[9,9,0],[11,11,0],
      [13,13,0],[15,15,0],[18,18,0],[21,21,0],[25,25,0],[29,30,0]
    ],
    0x73: [  // LATIN SMALL LETTER S
      [3,3,0],[4,4,0],[5,5,0],[5,5,0],[6,6,0],[7,8,0],[9,9,0],[10,11,0],
      [12,13,0],[14,15,0],[17,18,0],[20,21,0],[23,25,0],[28,30,0]
    ],
    0x74: [  // LATIN SMALL LETTER T
      [3,5,0],[4,6,0],[4,7,0],[5,7,0],[6,9,0],[7,12,0],[8,13,0],[9,16,0],
      [11,19,0],[13,22,0],[15,26,0],[18,30,0],[22,36,0],[26,43,0]
    ],
    0x75: [  // LATIN SMALL LETTER U
      [5,3,0],[6,4,0],[7,5,0],[8,5,0],[9,6,0],[11,8,0],[13,9,0],[15,11,0],
      [18,13,0],[21,15,0],[25,18,0],[29,21,0],[35,25,0],[41,30,0]
    ],
    0x76: [  // LATIN SMALL LETTER V
      [4,3,0],[5,4,0],[6,5,0],[7,5,0],[8,6,0],[10,8,0],[12,9,0],[14,11,0],
      [16,13,0],[19,15,0],[23,18,0],[27,21,0],[32,25,0],[38,30,0]
    ],
    0x77: [  // LATIN SMALL LETTER W
      [6,3,0],[7,4,0],[8,5,0],[10,5,0],[12,6,0],[14,8,0],[16,9,0],[19,11,0],
      [23,13,0],[27,15,0],[32,18,0],[38,21,0],[45,25,0],[53,30,0]
    ],
    0x78: [  // LATIN SMALL LETTER X
      [5,3,0],[5,4,0],[6,5,0],[7,5,0],[9,6,0],[10,8,0],[12,9,0],[14,11,0],
      [17,13,0],[20,15,0],[23,18,0],[28,21,0],[33,25,0],[39,30,0]
    ],
    0x79: [  // LATIN SMALL LETTER Y
      [4,4,1],[5,6,2],[6,7,2],[7,7,2],[9,9,3],[10,11,3],[12,13,4],[14,16,5],
      [17,19,6],[20,21,6],[23,26,8],[27,30,9],[33,36,11],[39,43,13]
    ],
    0x7A: [  // LATIN SMALL LETTER Z
      [4,3,0],[4,4,0],[5,5,0],[6,5,0],[7,6,0],[8,8,0],[9,9,0],[11,11,0],
      [13,13,0],[16,15,0],[19,18,0],[22,21,0],[26,25,0],[31,30,0]
    ],
    0x7B: [  // LEFT CURLY BRACKET
      [4,7,2],[5,9,2],[5,11,3],[6,11,3],[7,14,3],[9,18,4],[10,21,6],[12,25,7],
      [14,30,7],[17,33,8],[20,40,10],[24,47,12],[28,55,14],[34,66,17]
    ],
    0x7C: [  // VERTICAL LINE
      [2,7,2],[2,9,2],[2,12,3],[3,12,3],[3,14,3],[4,18,4],[4,20,5],[5,25,6],
      [6,29,7],[7,34,8],[8,40,10],[9,47,12],[11,56,14],[13,67,17]
    ],
    0x7D: [  // RIGHT CURLY BRACKET
      [4,7,2],[5,9,2],[5,11,3],[6,11,3],[7,14,3],[9,18,4],[10,21,6],[12,26,7],
      [14,29,7],[17,33,8],[20,40,10],[24,47,11],[28,56,14],[34,66,16]
    ],
    0x7E: [  // TILDE
      [4,1,-1],[4,1,-2],[5,3,-1],[6,3,-1],[7,4,-2],[8,3,-4],[10,3,-4],[12,4,-5],
      [14,5,-6],[16,6,-7],[19,6,-8],[23,7,-9],[27,9,-11],[32,10,-13]
    ],
    0x393: [  // GREEK CAPITAL LETTER GAMMA
      [5,5,0],[6,6,0],[7,8,0],[8,8,0],[9,9,0],[11,12,0],[13,14,0],[15,17,0],
      [18,20,0],[22,23,0],[26,27,0],[30,32,0],[36,38,0],[43,46,0]
    ],
    0x394: [  // GREEK CAPITAL LETTER DELTA
      [7,5,0],[8,6,0],[9,8,0],[11,8,0],[13,9,0],[15,12,0],[18,14,0],[21,17,0],
      [25,20,0],[30,23,0],[36,28,0],[42,32,0],[50,38,0],[60,46,0]
    ],
    0x398: [  // GREEK CAPITAL LETTER THETA
      [6,5,0],[7,6,0],[9,8,0],[10,8,0],[12,9,0],[14,12,0],[17,14,0],[20,17,0],
      [23,20,0],[28,23,0],[33,28,0],[39,32,0],[46,38,0],[55,46,0]
    ],
    0x39B: [  // GREEK CAPITAL LETTER LAMDA
      [6,5,0],[7,6,0],[8,8,0],[9,8,0],[11,9,0],[13,12,0],[15,14,0],[18,17,0],
      [22,20,0],[26,23,0],[30,28,0],[36,32,0],[43,38,0],[51,46,0]
    ],
    0x39E: [  // GREEK CAPITAL LETTER XI
      [5,5,0],[6,6,0],[8,8,0],[9,8,0],[10,9,0],[12,12,0],[14,14,0],[17,17,0],
      [20,20,0],[24,23,0],[28,27,0],[34,31,0],[40,37,0],[48,45,0]
    ],
    0x3A0: [  // GREEK CAPITAL LETTER PI
      [6,5,0],[8,6,0],[9,8,0],[11,8,0],[12,9,0],[15,12,0],[17,14,0],[20,17,0],
      [24,20,0],[29,23,0],[34,27,0],[40,32,0],[48,38,0],[57,46,0]
    ],
    0x3A3: [  // GREEK CAPITAL LETTER SIGMA
      [6,5,0],[7,6,0],[8,8,0],[9,8,0],[11,9,0],[13,12,0],[15,14,0],[18,17,0],
      [22,20,0],[26,23,0],[30,28,0],[36,32,0],[43,38,0],[51,46,0]
    ],
    0x3A5: [  // GREEK CAPITAL LETTER UPSILON
      [6,5,0],[7,6,0],[9,8,0],[10,8,0],[12,9,0],[14,12,0],[17,14,0],[20,17,0],
      [23,20,0],[28,23,0],[33,28,0],[39,32,0],[46,38,0],[55,46,0]
    ],
    0x3A6: [  // GREEK CAPITAL LETTER PHI
      [6,5,0],[7,6,0],[8,8,0],[9,8,0],[11,9,0],[13,12,0],[15,14,0],[18,17,0],
      [22,20,0],[26,23,0],[30,28,0],[36,32,0],[43,38,0],[51,46,0]
    ],
    0x3A8: [  // GREEK CAPITAL LETTER PSI
      [6,5,0],[7,6,0],[9,8,0],[10,8,0],[12,9,0],[14,12,0],[17,14,0],[20,17,0],
      [23,20,0],[28,23,0],[33,28,0],[39,32,0],[46,38,0],[55,46,0]
    ],
    0x3A9: [  // GREEK CAPITAL LETTER OMEGA
      [6,5,0],[7,6,0],[8,8,0],[10,8,0],[11,9,0],[13,12,0],[16,14,0],[18,17,0],
      [22,20,0],[26,23,0],[31,28,0],[36,32,0],[43,38,0],[51,46,0]
    ]
  }
});

MathJax.Ajax.loadComplete(MathJax.OutputJax["HTML-CSS"].imgDir+"/Main/Bold"+
                          MathJax.OutputJax["HTML-CSS"].imgPacked+"/Main.js");
