// Guessy Flutter Package Name Update Script
// This script updates the Flutter app package name from Elite Quiz to Guessy

import 'dart:io';

void main() async {
  print('🎯 Updating Flutter App Package Name to Guessy');
  print('=' * 50);
  
  await updateAndroidPackage();
  await updateIOSBundle();
  await updateFlutterConfig();
  await updateManifestFiles();
  
  print('\n' + '=' * 50);
  print('🎉 Flutter app package update completed!');
  print('\n📋 Next Steps:');
  print('1. Run: flutter clean');
  print('2. Run: flutter pub get');
  print('3. Update Firebase project with new package name');
  print('4. Generate new google-services.json with new package');
  print('5. Test the app with new package name');
  
  print('\n🔧 Updated Package Details:');
  print('- Old Package: com.wrteam.flutterquiz');
  print('- New Package: com.guessy.quiz');
  print('- App Name: Guessy');
  print('- Bundle ID: com.guessy.quiz');
}

Future<void> updateAndroidPackage() async {
  print('\n📱 Updating Android package configuration...');
  
  // Update build.gradle (app level)
  final buildGradleFile = File('elite_quiz_app-2.3.4/android/app/build.gradle');
  if (await buildGradleFile.exists()) {
    String content = await buildGradleFile.readAsString();
    content = content.replaceAll(
      'applicationId "com.wrteam.flutterquiz"',
      'applicationId "com.guessy.quiz"'
    );
    await buildGradleFile.writeAsString(content);
    print('✅ Updated android/app/build.gradle');
  }
  
  // Update AndroidManifest.xml files
  final manifestFiles = [
    'elite_quiz_app-2.3.4/android/app/src/main/AndroidManifest.xml',
    'elite_quiz_app-2.3.4/android/app/src/debug/AndroidManifest.xml',
    'elite_quiz_app-2.3.4/android/app/src/profile/AndroidManifest.xml',
  ];
  
  for (String manifestPath in manifestFiles) {
    final manifestFile = File(manifestPath);
    if (await manifestFile.exists()) {
      String content = await manifestFile.readAsString();
      content = content.replaceAll(
        'package="com.wrteam.flutterquiz"',
        'package="com.guessy.quiz"'
      );
      content = content.replaceAll(
        'com.wrteam.flutterquiz',
        'com.guessy.quiz'
      );
      await manifestFile.writeAsString(content);
      print('✅ Updated ${manifestPath.split('/').last}');
    }
  }
  
  // Update MainActivity.kt
  final mainActivityFile = File('elite_quiz_app-2.3.4/android/app/src/main/kotlin/com/wrteam/flutterquiz/MainActivity.kt');
  if (await mainActivityFile.exists()) {
    String content = await mainActivityFile.readAsString();
    content = content.replaceAll(
      'package com.wrteam.flutterquiz',
      'package com.guessy.quiz'
    );
    await mainActivityFile.writeAsString(content);
    
    // Move the file to new package structure
    final newDir = Directory('elite_quiz_app-2.3.4/android/app/src/main/kotlin/com/guessy/quiz');
    await newDir.create(recursive: true);
    await mainActivityFile.copy('${newDir.path}/MainActivity.kt');
    print('✅ Updated and moved MainActivity.kt');
  }
}

Future<void> updateIOSBundle() async {
  print('\n🍎 Updating iOS bundle configuration...');
  
  // Update Info.plist
  final infoPlistFile = File('elite_quiz_app-2.3.4/ios/Runner/Info.plist');
  if (await infoPlistFile.exists()) {
    String content = await infoPlistFile.readAsString();
    content = content.replaceAll(
      'com.wrteam.flutterquiz',
      'com.guessy.quiz'
    );
    content = content.replaceAll(
      'Elite Quiz',
      'Guessy'
    );
    await infoPlistFile.writeAsString(content);
    print('✅ Updated ios/Runner/Info.plist');
  }
  
  // Update project.pbxproj
  final projectFile = File('elite_quiz_app-2.3.4/ios/Runner.xcodeproj/project.pbxproj');
  if (await projectFile.exists()) {
    String content = await projectFile.readAsString();
    content = content.replaceAll(
      'com.wrteam.flutterquiz',
      'com.guessy.quiz'
    );
    content = content.replaceAll(
      'PRODUCT_BUNDLE_IDENTIFIER = com.wrteam.flutterquiz',
      'PRODUCT_BUNDLE_IDENTIFIER = com.guessy.quiz'
    );
    await projectFile.writeAsString(content);
    print('✅ Updated ios/Runner.xcodeproj/project.pbxproj');
  }
}

Future<void> updateFlutterConfig() async {
  print('\n⚙️ Updating Flutter configuration files...');
  
  // Update pubspec.yaml (already done)
  print('✅ pubspec.yaml already updated');
  
  // Update main.dart if needed
  final mainFile = File('elite_quiz_app-2.3.4/lib/main.dart');
  if (await mainFile.exists()) {
    String content = await mainFile.readAsString();
    if (content.contains('Elite Quiz')) {
      content = content.replaceAll('Elite Quiz', 'Guessy');
      await mainFile.writeAsString(content);
      print('✅ Updated lib/main.dart');
    }
  }
  
  // Update app.dart if needed
  final appFile = File('elite_quiz_app-2.3.4/lib/app/app.dart');
  if (await appFile.exists()) {
    String content = await appFile.readAsString();
    if (content.contains('Elite Quiz')) {
      content = content.replaceAll('Elite Quiz', 'Guessy');
      await appFile.writeAsString(content);
      print('✅ Updated lib/app/app.dart');
    }
  }
}

Future<void> updateManifestFiles() async {
  print('\n📄 Updating manifest and configuration files...');
  
  // Create new google-services.json template
  const googleServicesTemplate = '''{
  "project_info": {
    "project_number": "YOUR_PROJECT_NUMBER",
    "project_id": "guessy-quiz-app",
    "storage_bucket": "guessy-quiz-app.appspot.com"
  },
  "client": [
    {
      "client_info": {
        "mobilesdk_app_id": "YOUR_MOBILE_SDK_APP_ID",
        "android_client_info": {
          "package_name": "com.guessy.quiz"
        }
      },
      "oauth_client": [
        {
          "client_id": "YOUR_CLIENT_ID",
          "client_type": 3
        }
      ],
      "api_key": [
        {
          "current_key": "YOUR_API_KEY"
        }
      ],
      "services": {
        "appinvite_service": {
          "other_platform_oauth_client": [
            {
              "client_id": "YOUR_CLIENT_ID",
              "client_type": 3
            }
          ]
        }
      }
    }
  ],
  "configuration_version": "1"
}''';
  
  final googleServicesFile = File('elite_quiz_app-2.3.4/android/app/google-services.json.template');
  await googleServicesFile.writeAsString(googleServicesTemplate);
  print('✅ Created google-services.json template');
  
  // Create iOS GoogleService-Info.plist template
  const iosConfigTemplate = '''<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
    <key>API_KEY</key>
    <string>YOUR_API_KEY</string>
    <key>GCM_SENDER_ID</key>
    <string>YOUR_SENDER_ID</string>
    <key>PLIST_VERSION</key>
    <string>1</string>
    <key>BUNDLE_ID</key>
    <string>com.guessy.quiz</string>
    <key>PROJECT_ID</key>
    <string>guessy-quiz-app</string>
    <key>STORAGE_BUCKET</key>
    <string>guessy-quiz-app.appspot.com</string>
    <key>IS_ADS_ENABLED</key>
    <false/>
    <key>IS_ANALYTICS_ENABLED</key>
    <false/>
    <key>IS_APPINVITE_ENABLED</key>
    <true/>
    <key>IS_GCM_ENABLED</key>
    <true/>
    <key>IS_SIGNIN_ENABLED</key>
    <true/>
    <key>GOOGLE_APP_ID</key>
    <string>YOUR_GOOGLE_APP_ID</string>
</dict>
</plist>''';
  
  final iosConfigFile = File('elite_quiz_app-2.3.4/ios/Runner/GoogleService-Info.plist.template');
  await iosConfigFile.writeAsString(iosConfigTemplate);
  print('✅ Created GoogleService-Info.plist template');
}
