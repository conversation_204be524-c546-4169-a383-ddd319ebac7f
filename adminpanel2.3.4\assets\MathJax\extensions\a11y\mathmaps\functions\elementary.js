[{"locale": "en"}, {"category": "Elementary", "mappings": {"default": {"default": "logarithm", "alternative": "logarithm function", "short": "log"}}, "key": "log", "names": ["log"]}, {"category": "Elementary", "mappings": {"default": {"default": "natural logarithm", "alternative": "natural logarithm function", "short": "natural log"}, "mathspeak": {"default": "ln"}}, "key": "ln", "names": ["ln"]}, {"category": "Elementary", "mappings": {"default": {"default": "logarithm base 10", "short": "log base 10"}}, "key": "lg", "names": ["lg"]}, {"category": "Elementary", "mappings": {"default": {"default": "exponential", "alternative": "exponential function", "short": "exp"}}, "key": "exp", "names": ["exp", "expt"]}, {"category": "Elementary", "mappings": {"default": {"default": "greatest common divisor", "short": "gcd"}}, "key": "gcd", "names": ["gcd"]}, {"category": "Elementary", "mappings": {"default": {"default": "least common multiple", "short": "lcm"}}, "key": "lcm", "names": ["lcm"]}, {"category": "Complex", "mappings": {"default": {"default": "argument", "short": "arg"}}, "key": "arg", "names": ["arg"]}, {"category": "Complex", "mappings": {"default": {"default": "imaginary part", "short": "imaginary"}, "mathspeak": {"default": "im"}}, "key": "im", "names": ["im"]}, {"category": "Complex", "mappings": {"default": {"default": "real part", "short": "real"}, "mathspeak": {"default": "re"}}, "key": "re", "names": ["re"]}, {"category": "Limits", "mappings": {"default": {"default": "infimum", "short": "inf"}}, "key": "inf", "names": ["inf"]}, {"category": "Limits", "mappings": {"default": {"default": "limit", "short": "lim"}, "mathspeak": {"default": "limit"}}, "key": "lim", "names": ["lim"]}, {"category": "Limits", "mappings": {"default": {"default": "infimum default", "alternative": "inferior limit", "short": "liminf"}}, "key": "liminf", "names": ["lim inf"]}, {"category": "Limits", "mappings": {"default": {"default": "supremum limit", "alternative": "superior limit", "short": "limsup"}}, "key": "limsup", "names": ["lim sup"]}, {"category": "Limits", "mappings": {"default": {"default": "maximum", "short": "max"}}, "key": "max", "names": ["max"]}, {"category": "Limits", "mappings": {"default": {"default": "minimum", "short": "min"}}, "key": "min", "names": ["min"]}, {"category": "Limits", "mappings": {"default": {"default": "supremum", "short": "sup"}}, "key": "sup", "names": ["sup"]}, {"category": "Limits", "mappings": {"default": {"default": "injective limit", "alternative": "direct limit", "short": "colimit"}}, "key": "<PERSON><PERSON><PERSON>", "names": ["<PERSON><PERSON><PERSON>", "inj lim"]}, {"category": "Limits", "mappings": {"default": {"default": "projective limit", "alternative": "inverse limit", "short": "limit"}}, "key": "proj<PERSON>", "names": ["proj<PERSON>", "proj lim"]}, {"category": "Elementary", "mappings": {"default": {"default": "modulo", "short": "mod"}}, "key": "mod", "names": ["mod"]}, {"category": "Probability", "mappings": {"default": {"default": "probability"}}, "key": "Pr", "names": ["Pr"]}]