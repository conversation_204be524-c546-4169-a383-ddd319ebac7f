/*************************************************************
 *
 *  MathJax/fonts/HTML-CSS/TeX/png/Math/Italic/Main.js
 *  
 *  Defines the image size data needed for the HTML-CSS OutputJax
 *  to display mathematics using fallback images when the fonts
 *  are not available to the client browser.
 *
 *  ---------------------------------------------------------------------
 *
 *  Copyright (c) 2009-2013 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 *
 */

MathJax.OutputJax["HTML-CSS"].defineImageData({
  "MathJax_Math-italic": {
    0x20: [  // SPACE
      [1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],
      [1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0]
    ],
    0x2F: [  // SOLIDUS
      [5,6,1],[6,8,2],[7,8,2],[8,10,2],[9,12,3],[11,14,3],[13,18,4],[15,21,5],
      [18,25,6],[21,31,7],[25,36,8],[30,45,10],[36,53,12],[42,61,14]
    ],
    0x41: [  // LATIN CAPITAL LETTER A
      [6,5,0],[7,6,0],[8,6,0],[9,8,0],[11,9,0],[13,11,0],[15,14,0],[17,16,0],
      [21,19,0],[24,24,0],[29,28,0],[34,34,1],[41,41,1],[48,47,1]
    ],
    0x42: [  // LATIN CAPITAL LETTER B
      [6,5,0],[7,6,0],[8,6,0],[9,8,0],[11,9,0],[13,11,0],[15,14,0],[18,16,0],
      [21,19,0],[25,23,0],[30,27,0],[36,33,0],[42,39,0],[50,45,0]
    ],
    0x43: [  // LATIN CAPITAL LETTER C
      [6,5,0],[7,6,0],[8,6,0],[9,8,0],[11,9,0],[13,11,0],[15,14,0],[18,16,0],
      [21,20,1],[25,25,1],[30,29,1],[36,35,1],[42,41,1],[50,48,1]
    ],
    0x44: [  // LATIN CAPITAL LETTER D
      [6,5,0],[7,6,0],[8,6,0],[10,8,0],[12,10,0],[14,11,0],[16,14,0],[19,16,0],
      [23,19,0],[27,23,0],[32,27,0],[38,33,0],[45,39,0],[53,45,0]
    ],
    0x45: [  // LATIN CAPITAL LETTER E
      [6,5,0],[7,6,0],[8,6,0],[9,8,0],[11,9,0],[13,11,0],[15,14,0],[18,16,0],
      [22,19,0],[26,23,0],[30,27,0],[36,33,0],[43,39,0],[51,46,0]
    ],
    0x46: [  // LATIN CAPITAL LETTER F
      [6,5,0],[7,6,0],[8,6,0],[9,8,0],[11,9,0],[13,11,0],[15,14,0],[18,16,0],
      [21,19,0],[25,23,0],[30,27,0],[35,33,0],[42,39,0],[50,45,-1]
    ],
    0x47: [  // LATIN CAPITAL LETTER G
      [6,5,0],[7,6,0],[8,6,0],[9,8,0],[11,9,0],[13,11,0],[15,14,0],[18,16,0],
      [21,20,1],[25,25,1],[30,29,1],[36,35,1],[42,41,1],[50,48,1]
    ],
    0x48: [  // LATIN CAPITAL LETTER H
      [7,5,0],[8,7,0],[9,7,0],[11,9,0],[13,10,0],[15,12,0],[18,15,0],[21,17,0],
      [25,20,0],[30,24,0],[35,28,0],[42,34,0],[50,40,0],[59,46,0]
    ],
    0x49: [  // LATIN CAPITAL LETTER I
      [4,5,0],[5,6,0],[5,6,0],[6,8,0],[7,9,0],[9,11,0],[10,14,0],[12,16,0],
      [14,19,0],[17,23,0],[20,27,0],[24,33,0],[28,39,0],[34,45,0]
    ],
    0x4A: [  // LATIN CAPITAL LETTER J
      [5,5,0],[6,7,0],[7,6,0],[8,9,0],[9,10,0],[11,12,0],[13,15,0],[15,17,0],
      [18,21,1],[21,25,1],[25,29,1],[30,35,1],[35,41,1],[42,47,1]
    ],
    0x4B: [  // LATIN CAPITAL LETTER K
      [7,5,0],[8,6,0],[9,6,0],[11,8,0],[13,10,0],[15,11,0],[18,14,0],[21,16,0],
      [25,19,0],[30,23,0],[35,27,0],[42,33,0],[50,39,0],[59,45,0]
    ],
    0x4C: [  // LATIN CAPITAL LETTER L
      [5,5,0],[6,6,0],[7,6,0],[8,8,0],[9,9,0],[11,11,0],[13,14,0],[15,16,0],
      [18,19,0],[22,23,0],[26,27,0],[30,33,0],[36,39,0],[43,45,-1]
    ],
    0x4D: [  // LATIN CAPITAL LETTER M
      [8,5,0],[9,6,0],[11,6,0],[13,8,0],[15,10,0],[18,11,0],[21,14,0],[25,16,0],
      [29,19,0],[35,24,0],[42,27,0],[49,33,0],[58,39,0],[70,45,-1]
    ],
    0x4E: [  // LATIN CAPITAL LETTER N
      [7,5,0],[8,7,0],[9,6,0],[11,9,0],[13,10,0],[15,12,0],[18,15,0],[21,17,0],
      [25,20,0],[30,24,0],[35,28,0],[42,34,0],[50,39,0],[59,46,0]
    ],
    0x4F: [  // LATIN CAPITAL LETTER O
      [6,5,0],[7,6,0],[8,6,0],[9,8,0],[11,9,0],[13,11,0],[15,14,0],[18,16,0],
      [21,20,1],[25,25,1],[29,29,1],[35,35,2],[41,41,1],[49,48,1]
    ],
    0x50: [  // LATIN CAPITAL LETTER P
      [6,5,0],[7,6,0],[8,6,0],[9,8,0],[11,9,0],[13,11,0],[15,14,0],[18,16,0],
      [21,19,0],[25,23,0],[30,27,0],[35,33,0],[42,39,0],[50,45,0]
    ],
    0x51: [  // LATIN CAPITAL LETTER Q
      [6,6,1],[7,8,2],[8,8,2],[9,10,2],[11,12,3],[13,14,3],[15,18,4],[18,21,5],
      [21,25,6],[25,31,7],[29,36,8],[35,44,10],[41,51,11],[49,59,13]
    ],
    0x52: [  // LATIN CAPITAL LETTER R
      [6,5,0],[7,6,0],[8,6,0],[9,8,0],[11,9,0],[13,11,0],[15,14,0],[18,16,0],
      [21,20,1],[25,24,1],[30,28,1],[35,34,1],[42,40,1],[50,46,1]
    ],
    0x53: [  // LATIN CAPITAL LETTER S
      [5,5,0],[6,6,0],[7,6,0],[8,8,0],[9,9,0],[11,11,0],[13,14,0],[15,16,0],
      [18,20,1],[22,25,1],[26,29,1],[31,35,2],[36,41,1],[43,48,2]
    ],
    0x54: [  // LATIN CAPITAL LETTER T
      [5,5,0],[6,6,0],[7,6,0],[9,8,0],[10,9,0],[12,11,0],[14,14,0],[17,16,0],
      [20,19,0],[24,23,0],[28,26,0],[33,32,0],[40,38,0],[47,44,0]
    ],
    0x55: [  // LATIN CAPITAL LETTER U
      [6,5,0],[7,6,0],[8,7,0],[10,9,0],[11,10,0],[13,12,0],[15,15,0],[18,17,0],
      [22,21,1],[26,25,1],[31,29,1],[36,35,1],[43,41,1],[51,48,1]
    ],
    0x56: [  // LATIN CAPITAL LETTER V
      [6,6,1],[7,7,1],[8,7,1],[10,9,1],[11,10,1],[13,12,1],[15,15,1],[18,17,1],
      [22,20,1],[26,24,1],[31,28,1],[36,35,2],[43,41,2],[51,47,1]
    ],
    0x57: [  // LATIN CAPITAL LETTER W
      [8,5,0],[9,6,0],[11,6,0],[13,8,0],[15,9,0],[18,11,0],[21,14,0],[25,16,0],
      [29,20,1],[35,24,1],[41,28,1],[49,34,1],[58,41,2],[69,48,2]
    ],
    0x58: [  // LATIN CAPITAL LETTER X
      [6,5,0],[8,7,1],[9,7,1],[10,9,1],[12,10,1],[15,12,1],[17,15,1],[20,17,1],
      [24,20,1],[28,24,1],[34,28,1],[40,34,1],[48,40,1],[56,46,1]
    ],
    0x59: [  // LATIN CAPITAL LETTER Y
      [6,5,0],[7,6,0],[8,6,0],[9,8,0],[11,9,0],[13,11,0],[15,14,0],[18,16,0],
      [22,19,0],[26,23,0],[30,27,0],[36,33,0],[43,39,0],[51,45,-1]
    ],
    0x5A: [  // LATIN CAPITAL LETTER Z
      [5,5,0],[6,6,0],[8,6,0],[9,8,0],[10,9,0],[12,11,0],[15,14,0],[17,16,0],
      [20,19,0],[24,23,0],[29,27,0],[34,33,0],[40,39,0],[48,45,0]
    ],
    0x61: [  // LATIN SMALL LETTER A
      [4,3,0],[5,4,0],[5,4,0],[6,5,0],[7,6,0],[9,7,0],[10,9,0],[12,10,0],
      [14,12,0],[17,15,0],[20,17,0],[24,22,1],[28,26,1],[34,30,1]
    ],
    0x62: [  // LATIN SMALL LETTER B
      [3,5,0],[4,6,0],[5,6,0],[5,8,0],[6,9,0],[7,11,0],[9,14,0],[10,16,0],
      [12,19,0],[14,24,0],[17,27,0],[20,34,1],[24,40,1],[28,47,1]
    ],
    0x63: [  // LATIN SMALL LETTER C
      [3,3,0],[4,4,0],[5,4,0],[6,5,0],[6,6,0],[8,7,0],[9,9,0],[10,10,0],
      [12,12,0],[15,15,0],[17,17,0],[21,22,1],[24,26,1],[29,30,1]
    ],
    0x64: [  // LATIN SMALL LETTER D
      [4,5,0],[5,6,0],[6,6,0],[7,8,0],[8,9,0],[9,11,0],[11,14,0],[13,16,0],
      [15,19,0],[18,24,0],[21,27,0],[25,34,1],[30,40,1],[35,47,1]
    ],
    0x65: [  // LATIN SMALL LETTER E
      [3,3,0],[4,4,0],[5,4,0],[6,5,0],[6,6,0],[8,7,0],[9,9,0],[10,10,0],
      [12,12,0],[15,15,0],[17,17,0],[20,22,1],[24,26,1],[29,30,1]
    ],
    0x66: [  // LATIN SMALL LETTER F
      [4,6,1],[5,8,2],[6,8,2],[7,10,2],[8,12,3],[10,14,3],[11,18,4],[13,21,5],
      [16,25,6],[19,31,7],[22,36,8],[26,43,10],[31,51,12],[37,60,13]
    ],
    0x67: [  // LATIN SMALL LETTER G
      [4,4,1],[4,6,2],[5,6,2],[6,7,2],[7,9,3],[8,10,3],[10,13,4],[12,15,5],
      [14,18,6],[16,22,7],[19,25,8],[23,31,10],[27,37,12],[32,42,13]
    ],
    0x68: [  // LATIN SMALL LETTER H
      [4,5,0],[5,6,0],[6,6,0],[7,8,0],[8,9,0],[10,11,0],[11,14,0],[13,16,0],
      [16,19,0],[19,24,0],[22,27,0],[26,34,1],[31,40,1],[37,47,1]
    ],
    0x69: [  // LATIN SMALL LETTER I
      [3,5,0],[3,6,0],[3,6,0],[4,8,0],[5,10,0],[5,11,0],[6,14,0],[7,16,0],
      [9,18,0],[10,23,0],[12,26,0],[14,32,1],[17,38,1],[20,44,1]
    ],
    0x6A: [  // LATIN SMALL LETTER J
      [4,6,1],[5,8,2],[5,8,2],[6,10,2],[7,13,3],[8,14,3],[9,18,4],[11,20,5],
      [13,24,6],[15,30,7],[17,33,8],[20,41,10],[24,49,12],[28,57,13]
    ],
    0x6B: [  // LATIN SMALL LETTER K
      [4,5,0],[5,6,0],[5,6,0],[6,8,0],[7,9,0],[9,11,0],[10,14,0],[12,16,0],
      [14,19,0],[17,24,0],[20,27,0],[24,34,1],[28,40,1],[33,47,1]
    ],
    0x6C: [  // LATIN SMALL LETTER L
      [2,6,0],[3,7,0],[3,7,0],[4,9,0],[4,10,0],[5,12,0],[6,15,0],[7,17,0],
      [8,20,0],[9,24,0],[11,28,0],[13,35,1],[15,40,1],[18,47,1]
    ],
    0x6D: [  // LATIN SMALL LETTER M
      [6,3,0],[8,4,0],[9,4,0],[10,5,0],[12,6,0],[15,7,0],[17,9,0],[20,10,0],
      [24,12,0],[28,15,0],[34,17,0],[40,22,1],[48,26,1],[56,30,1]
    ],
    0x6E: [  // LATIN SMALL LETTER N
      [4,3,0],[5,4,0],[6,4,0],[7,5,0],[8,6,0],[10,7,0],[12,9,0],[14,10,0],
      [16,12,0],[19,15,0],[23,17,0],[27,22,1],[32,26,1],[38,30,1]
    ],
    0x6F: [  // LATIN SMALL LETTER O
      [4,3,0],[4,4,0],[5,4,0],[6,5,0],[7,6,0],[8,7,0],[10,9,0],[12,10,0],
      [14,12,0],[16,15,0],[19,17,0],[23,22,1],[27,26,1],[32,30,1]
    ],
    0x70: [  // LATIN SMALL LETTER P
      [5,4,1],[6,6,2],[6,6,2],[7,7,2],[8,9,3],[10,10,3],[11,13,4],[13,15,5],
      [16,18,6],[19,22,7],[22,25,8],[26,31,10],[31,37,12],[36,43,14]
    ],
    0x71: [  // LATIN SMALL LETTER Q
      [4,4,1],[4,6,2],[5,6,2],[6,7,2],[7,9,3],[8,10,3],[9,13,4],[11,15,5],
      [13,18,6],[16,22,7],[18,24,7],[22,31,10],[26,37,12],[31,42,13]
    ],
    0x72: [  // LATIN SMALL LETTER R
      [3,3,0],[4,4,0],[5,4,0],[6,5,0],[6,6,0],[8,7,0],[9,9,0],[10,10,0],
      [12,12,0],[15,15,0],[17,17,0],[20,22,1],[24,26,1],[29,30,1]
    ],
    0x73: [  // LATIN SMALL LETTER S
      [3,3,0],[4,4,0],[5,4,0],[5,5,0],[6,6,0],[7,7,0],[9,9,0],[10,10,0],
      [12,12,0],[14,15,0],[17,17,0],[20,22,1],[24,26,1],[28,30,1]
    ],
    0x74: [  // LATIN SMALL LETTER T
      [3,5,0],[3,6,0],[4,6,0],[4,7,0],[5,9,0],[6,10,0],[7,13,0],[8,15,0],
      [10,18,0],[11,22,0],[13,25,0],[16,30,1],[19,36,1],[22,42,1]
    ],
    0x75: [  // LATIN SMALL LETTER U
      [4,3,0],[5,4,0],[6,4,0],[7,5,0],[8,6,0],[10,7,0],[11,9,0],[13,10,0],
      [16,12,0],[19,15,0],[22,17,0],[26,22,1],[31,26,1],[37,30,1]
    ],
    0x76: [  // LATIN SMALL LETTER V
      [4,3,0],[4,4,0],[5,4,0],[6,5,0],[7,6,0],[8,7,0],[10,9,0],[11,10,0],
      [13,12,0],[16,15,0],[19,17,0],[22,22,1],[26,26,1],[31,30,1]
    ],
    0x77: [  // LATIN SMALL LETTER W
      [5,3,0],[6,4,0],[7,4,0],[9,5,0],[10,6,0],[12,7,0],[14,9,0],[16,10,0],
      [20,12,0],[23,15,0],[27,17,0],[32,22,1],[39,26,1],[46,30,1]
    ],
    0x78: [  // LATIN SMALL LETTER X
      [4,3,0],[5,4,0],[6,4,0],[7,5,0],[8,6,0],[9,7,0],[11,9,0],[13,10,0],
      [15,12,0],[18,15,0],[21,17,0],[25,22,1],[29,26,1],[35,30,1]
    ],
    0x79: [  // LATIN SMALL LETTER Y
      [4,4,1],[5,6,2],[5,6,2],[6,7,2],[7,9,3],[9,10,3],[10,13,4],[12,15,5],
      [14,18,6],[17,22,7],[20,25,8],[23,31,10],[28,37,12],[33,42,13]
    ],
    0x7A: [  // LATIN SMALL LETTER Z
      [4,3,0],[4,4,0],[5,4,0],[6,5,0],[7,6,0],[8,7,0],[10,9,0],[11,10,0],
      [13,12,0],[16,15,0],[19,17,0],[22,22,1],[26,26,1],[31,30,1]
    ],
    0xA0: [  // NO-BREAK SPACE
      [1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],
      [1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0]
    ],
    0x393: [  // GREEK CAPITAL LETTER GAMMA
      [5,5,0],[6,6,0],[8,6,0],[9,8,0],[10,9,0],[12,11,0],[14,14,0],[17,16,0],
      [20,19,0],[24,23,0],[29,27,0],[34,33,0],[40,39,0],[48,45,0]
    ],
    0x394: [  // GREEK CAPITAL LETTER DELTA
      [6,5,0],[7,6,0],[8,6,0],[10,8,0],[11,9,0],[14,11,0],[16,14,0],[19,16,0],
      [22,19,0],[26,24,0],[31,28,0],[37,34,0],[44,41,0],[52,47,0]
    ],
    0x398: [  // GREEK CAPITAL LETTER THETA
      [6,5,0],[7,6,0],[8,6,0],[9,8,0],[11,9,0],[13,11,0],[15,14,0],[18,16,0],
      [21,20,1],[25,25,1],[29,29,1],[35,35,2],[41,41,1],[49,48,1]
    ],
    0x39B: [  // GREEK CAPITAL LETTER LAMDA
      [5,5,0],[6,7,0],[7,7,0],[8,8,0],[10,10,0],[12,12,0],[14,15,0],[16,16,0],
      [19,20,0],[23,24,0],[27,28,0],[32,34,1],[38,41,1],[45,47,0]
    ],
    0x39E: [  // GREEK CAPITAL LETTER XI
      [6,5,0],[7,6,0],[8,6,0],[10,8,0],[11,9,0],[13,11,0],[16,14,0],[19,16,0],
      [22,19,0],[26,23,0],[31,27,0],[37,32,0],[43,38,0],[52,45,0]
    ],
    0x3A0: [  // GREEK CAPITAL LETTER PI
      [7,5,0],[8,6,0],[9,6,0],[11,8,0],[13,9,0],[15,11,0],[18,14,0],[21,16,0],
      [25,19,0],[30,23,0],[35,27,0],[42,33,0],[50,39,0],[59,45,0]
    ],
    0x3A3: [  // GREEK CAPITAL LETTER SIGMA
      [6,5,0],[7,6,0],[8,6,0],[10,8,0],[12,9,0],[14,11,0],[16,14,0],[19,16,0],
      [23,19,0],[27,23,0],[32,27,0],[38,33,0],[45,39,0],[54,46,0]
    ],
    0x3A5: [  // GREEK CAPITAL LETTER UPSILON
      [5,5,0],[6,6,0],[7,6,0],[9,8,0],[10,9,0],[12,11,0],[14,14,0],[17,16,0],
      [20,19,0],[23,24,0],[28,28,0],[33,34,0],[39,40,0],[46,46,0]
    ],
    0x3A6: [  // GREEK CAPITAL LETTER PHI
      [5,5,0],[6,6,0],[7,6,0],[8,8,0],[9,9,0],[11,11,0],[13,14,0],[15,16,0],
      [18,19,0],[22,23,0],[26,27,0],[30,32,-1],[36,39,0],[43,45,-1]
    ],
    0x3A8: [  // GREEK CAPITAL LETTER PSI
      [5,5,0],[6,6,0],[7,6,0],[9,8,0],[10,9,0],[12,11,0],[14,14,0],[17,16,0],
      [20,19,0],[23,23,0],[28,27,0],[33,32,-1],[39,39,0],[46,45,-1]
    ],
    0x3A9: [  // GREEK CAPITAL LETTER OMEGA
      [6,5,0],[7,6,0],[8,6,0],[10,8,0],[11,9,0],[14,11,0],[16,14,0],[19,16,0],
      [22,19,0],[26,24,0],[31,28,0],[37,34,0],[44,40,0],[52,47,0]
    ],
    0x3B1: [  // GREEK SMALL LETTER ALPHA
      [5,3,0],[5,4,0],[6,4,0],[8,5,0],[9,6,0],[10,7,0],[12,9,0],[14,10,0],
      [17,12,0],[20,15,0],[24,17,0],[28,22,1],[34,26,1],[40,30,1]
    ],
    0x3B2: [  // GREEK SMALL LETTER BETA
      [4,6,1],[5,8,2],[6,8,2],[7,10,2],[8,12,3],[10,14,3],[12,18,4],[14,21,5],
      [16,25,6],[19,31,7],[23,36,8],[27,43,10],[32,52,12],[38,59,13]
    ],
    0x3B3: [  // GREEK SMALL LETTER GAMMA
      [4,4,1],[5,6,2],[6,6,2],[7,7,2],[8,9,3],[10,10,3],[11,13,4],[13,15,5],
      [16,18,6],[18,22,7],[22,25,8],[26,32,11],[31,38,13],[36,44,15]
    ],
    0x3B4: [  // GREEK SMALL LETTER DELTA
      [4,5,0],[4,6,0],[5,6,0],[6,8,0],[7,9,0],[8,11,0],[9,14,0],[11,16,0],
      [13,19,0],[15,24,0],[18,28,0],[21,35,1],[25,41,1],[30,48,1]
    ],
    0x3B5: [  // GREEK SMALL LETTER EPSILON
      [3,3,0],[4,4,0],[5,4,0],[6,5,0],[6,6,0],[8,7,0],[9,9,0],[10,10,0],
      [12,13,1],[15,16,1],[17,18,1],[20,22,1],[24,27,2],[29,31,1]
    ],
    0x3B6: [  // GREEK SMALL LETTER ZETA
      [4,6,1],[4,8,2],[5,8,2],[6,10,2],[7,12,3],[8,14,3],[10,18,4],[11,21,5],
      [13,25,6],[16,31,7],[19,36,8],[22,43,10],[26,52,12],[31,60,13]
    ],
    0x3B7: [  // GREEK SMALL LETTER ETA
      [4,4,1],[5,6,2],[5,6,2],[6,7,2],[7,9,3],[9,10,3],[10,13,4],[12,15,5],
      [14,18,6],[17,22,7],[20,25,8],[24,32,11],[28,38,13],[33,44,15]
    ],
    0x3B8: [  // GREEK SMALL LETTER THETA
      [4,5,0],[4,6,0],[5,6,0],[6,8,0],[7,9,0],[8,11,0],[9,14,0],[11,16,0],
      [13,19,0],[16,24,0],[19,28,0],[22,34,1],[26,41,1],[31,47,1]
    ],
    0x3B9: [  // GREEK SMALL LETTER IOTA
      [3,3,0],[3,4,0],[4,4,0],[4,5,0],[5,6,0],[6,7,0],[7,9,0],[8,10,0],
      [10,12,0],[11,15,0],[13,17,0],[16,22,1],[19,26,1],[22,30,1]
    ],
    0x3BA: [  // GREEK SMALL LETTER KAPPA
      [4,3,0],[5,4,0],[6,4,0],[7,5,0],[8,6,0],[10,7,0],[11,9,0],[13,10,0],
      [16,12,0],[19,15,0],[22,17,0],[26,22,1],[31,26,1],[37,30,1]
    ],
    0x3BB: [  // GREEK SMALL LETTER LAMDA
      [4,5,0],[5,6,0],[6,6,0],[7,8,0],[8,9,0],[10,11,0],[11,14,0],[13,16,0],
      [16,19,0],[19,24,0],[22,27,0],[26,34,1],[31,40,1],[37,47,1]
    ],
    0x3BC: [  // GREEK SMALL LETTER MU
      [4,4,1],[5,6,2],[6,6,2],[7,7,2],[8,9,3],[10,10,3],[12,13,4],[14,15,5],
      [16,18,6],[20,22,7],[23,25,8],[27,32,11],[33,38,13],[39,44,15]
    ],
    0x3BD: [  // GREEK SMALL LETTER NU
      [4,3,0],[5,4,0],[6,4,0],[7,5,0],[8,6,0],[9,7,0],[11,9,0],[13,10,0],
      [15,12,0],[18,15,0],[21,17,0],[25,22,1],[30,25,0],[35,29,0]
    ],
    0x3BE: [  // GREEK SMALL LETTER XI
      [4,6,1],[4,8,2],[5,8,2],[6,10,2],[7,12,3],[8,14,3],[9,18,4],[11,21,5],
      [13,25,6],[15,31,7],[18,36,8],[21,43,10],[25,52,12],[30,60,13]
    ],
    0x3BF: [  // GREEK SMALL LETTER OMICRON
      [4,3,0],[4,4,0],[5,4,0],[6,5,0],[7,6,0],[8,7,0],[10,9,0],[12,10,0],
      [14,12,0],[16,15,0],[19,17,0],[23,22,1],[27,26,1],[32,30,1]
    ],
    0x3C0: [  // GREEK SMALL LETTER PI
      [4,3,0],[5,4,0],[6,4,0],[7,5,0],[8,6,0],[10,7,0],[12,9,0],[14,10,0],
      [16,12,0],[19,15,0],[23,17,0],[27,22,1],[32,25,1],[38,29,1]
    ],
    0x3C1: [  // GREEK SMALL LETTER RHO
      [4,4,1],[5,6,2],[5,6,2],[6,7,2],[7,9,3],[9,10,3],[10,13,4],[12,15,5],
      [14,18,6],[17,22,7],[20,25,8],[24,32,11],[28,38,13],[34,44,15]
    ],
    0x3C2: [  // GREEK SMALL LETTER FINAL SIGMA
      [3,4,1],[4,5,1],[5,5,1],[5,7,2],[6,8,2],[7,9,2],[8,11,2],[10,13,3],
      [12,15,3],[14,19,4],[17,22,5],[20,26,5],[23,32,7],[27,36,7]
    ],
    0x3C3: [  // GREEK SMALL LETTER SIGMA
      [4,3,0],[5,4,0],[6,4,0],[7,5,0],[8,6,0],[10,7,0],[12,9,0],[14,10,0],
      [16,12,0],[19,15,0],[23,17,0],[27,22,1],[32,25,1],[38,29,1]
    ],
    0x3C4: [  // GREEK SMALL LETTER TAU
      [4,3,0],[5,4,0],[6,4,0],[7,5,0],[8,6,0],[9,7,0],[10,9,0],[12,10,0],
      [15,12,0],[17,15,0],[21,17,0],[24,21,1],[29,26,1],[34,29,1]
    ],
    0x3C5: [  // GREEK SMALL LETTER UPSILON
      [4,3,0],[5,4,0],[6,4,0],[7,5,0],[8,6,0],[9,7,0],[11,9,0],[13,10,0],
      [15,12,0],[18,15,0],[21,17,0],[25,22,1],[29,26,1],[35,30,1]
    ],
    0x3C6: [  // GREEK SMALL LETTER PHI
      [5,4,1],[6,6,2],[7,6,2],[8,7,2],[9,9,3],[11,10,3],[13,13,4],[15,15,5],
      [18,18,6],[21,22,7],[25,25,8],[29,32,11],[35,38,13],[41,44,15]
    ],
    0x3C7: [  // GREEK SMALL LETTER CHI
      [5,4,1],[5,6,2],[6,6,2],[8,7,2],[9,9,3],[10,10,3],[12,13,4],[14,15,5],
      [17,18,6],[20,22,7],[24,25,8],[28,31,10],[34,37,12],[40,42,13]
    ],
    0x3C8: [  // GREEK SMALL LETTER PSI
      [5,6,1],[6,8,2],[7,8,2],[8,10,2],[9,12,3],[11,14,3],[13,18,4],[15,21,5],
      [18,25,6],[21,31,7],[25,35,8],[30,43,10],[35,51,12],[42,59,13]
    ],
    0x3C9: [  // GREEK SMALL LETTER OMEGA
      [5,3,0],[6,4,0],[6,4,0],[8,5,0],[9,6,0],[11,7,0],[12,9,0],[15,10,0],
      [17,12,0],[20,15,0],[24,17,0],[29,22,1],[34,26,1],[40,30,1]
    ],
    0x3D1: [  // GREEK THETA SYMBOL
      [4,5,0],[5,6,0],[6,6,0],[7,8,0],[8,9,0],[10,11,0],[11,14,0],[14,16,0],
      [16,19,0],[19,24,0],[23,28,0],[27,34,1],[32,41,1],[38,47,1]
    ],
    0x3D5: [  // GREEK PHI SYMBOL
      [4,6,1],[5,8,2],[6,8,2],[7,10,2],[8,12,3],[10,14,3],[12,18,4],[14,21,5],
      [16,25,6],[20,31,7],[23,35,8],[27,43,10],[32,51,12],[39,59,13]
    ],
    0x3D6: [  // GREEK PI SYMBOL
      [6,3,0],[7,4,0],[9,4,0],[10,5,0],[12,6,0],[14,7,0],[17,9,0],[20,10,0],
      [23,12,0],[28,15,0],[33,17,0],[39,22,1],[46,25,1],[55,29,1]
    ],
    0x3F1: [  // GREEK RHO SYMBOL
      [4,4,1],[5,6,2],[5,6,2],[6,7,2],[8,9,3],[9,10,3],[10,13,4],[12,15,5],
      [15,18,6],[17,22,7],[20,25,8],[24,31,10],[29,37,12],[34,42,13]
    ],
    0x3F5: [  // GREEK LUNATE EPSILON SYMBOL
      [3,3,0],[4,4,0],[4,4,0],[5,5,0],[6,6,0],[7,7,0],[8,9,0],[9,10,0],
      [11,12,0],[13,15,0],[15,17,0],[18,21,1],[21,25,1],[25,30,1]
    ]
  }
});

MathJax.Ajax.loadComplete(MathJax.OutputJax["HTML-CSS"].imgDir+"/Math/Italic"+
                          MathJax.OutputJax["HTML-CSS"].imgPacked+"/Main.js");
