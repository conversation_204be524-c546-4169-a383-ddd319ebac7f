[{"locale": "en"}, {"category": "Nd", "mappings": {"default": {"default": "digit zero", "short": "zero"}, "mathspeak": {"default": "0"}}, "key": "0030"}, {"category": "Nd", "mappings": {"default": {"default": "digit one", "short": "one"}, "mathspeak": {"default": "1"}}, "key": "0031"}, {"category": "Nd", "mappings": {"default": {"default": "digit two", "short": "two"}, "mathspeak": {"default": "2"}}, "key": "0032"}, {"category": "Nd", "mappings": {"default": {"default": "digit three", "short": "three"}, "mathspeak": {"default": "3"}}, "key": "0033"}, {"category": "Nd", "mappings": {"default": {"default": "digit four", "short": "four"}, "mathspeak": {"default": "4"}}, "key": "0034"}, {"category": "Nd", "mappings": {"default": {"default": "digit five", "short": "five"}, "mathspeak": {"default": "5"}}, "key": "0035"}, {"category": "Nd", "mappings": {"default": {"default": "digit six", "short": "six"}, "mathspeak": {"default": "6"}}, "key": "0036"}, {"category": "Nd", "mappings": {"default": {"default": "digit seven", "short": "seven"}, "mathspeak": {"default": "7"}}, "key": "0037"}, {"category": "Nd", "mappings": {"default": {"default": "digit eight", "short": "eight"}, "mathspeak": {"default": "8"}}, "key": "0038"}, {"category": "Nd", "mappings": {"default": {"default": "digit nine", "short": "nine"}, "mathspeak": {"default": "9"}}, "key": "0039"}, {"category": "No", "mappings": {"default": {"default": "superscript digit two", "alternative": "superscript two", "short": "square"}, "mathspeak": {"default": "9"}}, "key": "00B2"}, {"category": "No", "mappings": {"default": {"default": "superscript digit three", "alternative": "superscript three", "short": "cube"}}, "key": "00B3"}, {"category": "No", "mappings": {"default": {"default": "superscript digit one", "alternative": "superscript one", "short": "super one"}}, "key": "00B9"}, {"category": "No", "mappings": {"default": {"default": "vulgar fraction one quarter", "alternative": "fraction one quarter", "short": "one quarter"}}, "key": "00BC"}, {"category": "No", "mappings": {"default": {"default": "vulgar fraction one half", "alternative": "fraction one half", "short": "one half"}}, "key": "00BD"}, {"category": "No", "mappings": {"default": {"default": "vulgar fraction three quarters", "alternative": "fraction three quarters", "short": "three quarters"}}, "key": "00BE"}, {"category": "No", "mappings": {"default": {"default": "superscript zero", "short": "super zero"}}, "key": "2070"}, {"category": "No", "mappings": {"default": {"default": "superscript digit four", "alternative": "superscript four", "short": "super four"}}, "key": "2074"}, {"category": "No", "mappings": {"default": {"default": "superscript digit five", "alternative": "superscript five", "short": "super five"}}, "key": "2075"}, {"category": "No", "mappings": {"default": {"default": "superscript digit six", "alternative": "superscript six", "short": "super six"}}, "key": "2076"}, {"category": "No", "mappings": {"default": {"default": "superscript digit seven", "alternative": "superscript seven", "short": "super seven"}}, "key": "2077"}, {"category": "No", "mappings": {"default": {"default": "superscript digit eight", "alternative": "superscript eight", "short": "super eight"}}, "key": "2078"}, {"category": "No", "mappings": {"default": {"default": "superscript digit nine", "alternative": "superscript nine", "short": "super nine"}}, "key": "2079"}, {"category": "No", "mappings": {"default": {"default": "subscript digit zero", "alternative": "subscript zero", "short": "sub zero"}}, "key": "2080"}, {"category": "No", "mappings": {"default": {"default": "subscript digit one", "alternative": "subscript one", "short": "sub one"}}, "key": "2081"}, {"category": "No", "mappings": {"default": {"default": "subscript digit two", "alternative": "subscript two", "short": "sub two"}}, "key": "2082"}, {"category": "No", "mappings": {"default": {"default": "subscript digit three", "alternative": "subscript three", "short": "sub three"}}, "key": "2083"}, {"category": "No", "mappings": {"default": {"default": "subscript digit four", "alternative": "subscript four", "short": "sub four"}}, "key": "2084"}, {"category": "No", "mappings": {"default": {"default": "subscript digit five", "alternative": "subscript five", "short": "sub five"}}, "key": "2085"}, {"category": "No", "mappings": {"default": {"default": "subscript digit six", "alternative": "subscript six", "short": "sub six"}}, "key": "2086"}, {"category": "No", "mappings": {"default": {"default": "subscript digit seven", "alternative": "subscript seven", "short": "sub seven"}}, "key": "2087"}, {"category": "No", "mappings": {"default": {"default": "subscript digit eight", "alternative": "subscript eight", "short": "sub eight"}}, "key": "2088"}, {"category": "No", "mappings": {"default": {"default": "subscript digit nine", "alternative": "subscript nine", "short": "sub nine"}}, "key": "2089"}, {"category": "No", "mappings": {"default": {"default": "vulgar fraction one seventh", "short": "one seventh"}}, "key": "2150"}, {"category": "No", "mappings": {"default": {"default": "vulgar fraction one ninth", "short": "one ninth"}}, "key": "2151"}, {"category": "No", "mappings": {"default": {"default": "vulgar fraction one tenth", "short": "one tenth"}}, "key": "2152"}, {"category": "No", "mappings": {"default": {"default": "vulgar fraction one third", "alternative": "fraction one third", "short": "one third"}}, "key": "2153"}, {"category": "No", "mappings": {"default": {"default": "vulgar fraction two thirds", "alternative": "fraction two thirds", "short": "two thirds"}}, "key": "2154"}, {"category": "No", "mappings": {"default": {"default": "vulgar fraction one fifth", "alternative": "fraction one fifth", "short": "one fifth"}}, "key": "2155"}, {"category": "No", "mappings": {"default": {"default": "vulgar fraction two fifths", "alternative": "fraction two fifths", "short": "two fifths"}}, "key": "2156"}, {"category": "No", "mappings": {"default": {"default": "vulgar fraction three fifths", "alternative": "fraction three fifths", "short": "three fifths"}}, "key": "2157"}, {"category": "No", "mappings": {"default": {"default": "vulgar fraction four fifths", "alternative": "fraction four fifths", "short": "four fifths"}}, "key": "2158"}, {"category": "No", "mappings": {"default": {"default": "vulgar fraction one sixth", "alternative": "fraction one sixth", "short": "one sixth"}}, "key": "2159"}, {"category": "No", "mappings": {"default": {"default": "vulgar fraction five sixths", "alternative": "fraction five sixths", "short": "five sixths"}}, "key": "215A"}, {"category": "No", "mappings": {"default": {"default": "vulgar fraction one eighth", "alternative": "fraction one eighth", "short": "one eighth"}}, "key": "215B"}, {"category": "No", "mappings": {"default": {"default": "vulgar fraction three eighths", "alternative": "fraction three eighths", "short": "three eighths"}}, "key": "215C"}, {"category": "No", "mappings": {"default": {"default": "vulgar fraction five eighths", "alternative": "fraction five eighths", "short": "five eighths"}}, "key": "215D"}, {"category": "No", "mappings": {"default": {"default": "vulgar fraction seven eighths", "alternative": "fraction seven eighths", "short": "seven eighths"}}, "key": "215E"}, {"category": "No", "mappings": {"default": {"default": "fraction numerator one", "short": "numerator one"}}, "key": "215F"}, {"category": "No", "mappings": {"default": {"default": "vulgar fraction zero thirds", "short": "zero thirds"}}, "key": "2189"}, {"category": "No", "mappings": {"default": {"default": "circled digit one", "short": "circled one"}, "mathspeak": {"default": "circled 1"}}, "key": "2460"}, {"category": "No", "mappings": {"default": {"default": "circled digit two", "short": "circled two"}, "mathspeak": {"default": "circled 2"}}, "key": "2461"}, {"category": "No", "mappings": {"default": {"default": "circled digit three", "short": "circled three"}, "mathspeak": {"default": "circled 3"}}, "key": "2462"}, {"category": "No", "mappings": {"default": {"default": "circled digit four", "short": "circled four"}, "mathspeak": {"default": "circled 4"}}, "key": "2463"}, {"category": "No", "mappings": {"default": {"default": "circled digit five", "short": "circled five"}, "mathspeak": {"default": "circled 5"}}, "key": "2464"}, {"category": "No", "mappings": {"default": {"default": "circled digit six", "short": "circled six"}, "mathspeak": {"default": "circled 6"}}, "key": "2465"}, {"category": "No", "mappings": {"default": {"default": "circled digit seven", "short": "circled seven"}, "mathspeak": {"default": "circled 7"}}, "key": "2466"}, {"category": "No", "mappings": {"default": {"default": "circled digit eight", "short": "circled eight"}, "mathspeak": {"default": "circled 8"}}, "key": "2467"}, {"category": "No", "mappings": {"default": {"default": "circled digit nine", "short": "circled nine"}, "mathspeak": {"default": "circled 9"}}, "key": "2468"}, {"category": "No", "mappings": {"default": {"default": "circled number ten", "short": "circled ten"}, "mathspeak": {"default": "circled 10"}}, "key": "2469"}, {"category": "No", "mappings": {"default": {"default": "circled number eleven", "short": "circled eleven"}, "mathspeak": {"default": "circled 11"}}, "key": "246A"}, {"category": "No", "mappings": {"default": {"default": "circled number twelve", "short": "circled twelve"}, "mathspeak": {"default": "circled 12"}}, "key": "246B"}, {"category": "No", "mappings": {"default": {"default": "circled number thirteen", "short": "circled thirteen"}, "mathspeak": {"default": "circled 13"}}, "key": "246C"}, {"category": "No", "mappings": {"default": {"default": "circled number fourteen", "short": "circled fourteen"}, "mathspeak": {"default": "circled 14"}}, "key": "246D"}, {"category": "No", "mappings": {"default": {"default": "circled number fifteen", "short": "circled fifteen"}, "mathspeak": {"default": "circled 15"}}, "key": "246E"}, {"category": "No", "mappings": {"default": {"default": "circled number sixteen", "short": "circled sixteen"}, "mathspeak": {"default": "circled 16"}}, "key": "246F"}, {"category": "No", "mappings": {"default": {"default": "circled number seventeen", "short": "circled seventeen"}, "mathspeak": {"default": "circled 17"}}, "key": "2470"}, {"category": "No", "mappings": {"default": {"default": "circled number eighteen", "short": "circled eighteen"}, "mathspeak": {"default": "circled 18"}}, "key": "2471"}, {"category": "No", "mappings": {"default": {"default": "circled number nineteen", "short": "circled nineteen"}, "mathspeak": {"default": "circled 19"}}, "key": "2472"}, {"category": "No", "mappings": {"default": {"default": "circled number twenty", "short": "circled twenty"}, "mathspeak": {"default": "circled 20"}}, "key": "2473"}, {"category": "No", "mappings": {"default": {"default": "parenthesized digit one", "short": "parenthesized one"}, "mathspeak": {"default": "parenthesized 1"}}, "key": "2474"}, {"category": "No", "mappings": {"default": {"default": "parenthesized digit two", "short": "parenthesized two"}, "mathspeak": {"default": "parenthesized 2"}}, "key": "2475"}, {"category": "No", "mappings": {"default": {"default": "parenthesized digit three", "short": "parenthesized three"}, "mathspeak": {"default": "parenthesized 3"}}, "key": "2476"}, {"category": "No", "mappings": {"default": {"default": "parenthesized digit four", "short": "parenthesized four"}, "mathspeak": {"default": "parenthesized 4"}}, "key": "2477"}, {"category": "No", "mappings": {"default": {"default": "parenthesized digit five", "short": "parenthesized five"}, "mathspeak": {"default": "parenthesized 5"}}, "key": "2478"}, {"category": "No", "mappings": {"default": {"default": "parenthesized digit six", "short": "parenthesized six"}, "mathspeak": {"default": "parenthesized 6"}}, "key": "2479"}, {"category": "No", "mappings": {"default": {"default": "parenthesized digit seven", "short": "parenthesized seven"}, "mathspeak": {"default": "parenthesized 7"}}, "key": "247A"}, {"category": "No", "mappings": {"default": {"default": "parenthesized digit eight", "short": "parenthesized eight"}, "mathspeak": {"default": "parenthesized 8"}}, "key": "247B"}, {"category": "No", "mappings": {"default": {"default": "parenthesized digit nine", "short": "parenthesized nine"}, "mathspeak": {"default": "parenthesized 9"}}, "key": "247C"}, {"category": "No", "mappings": {"default": {"default": "parenthesized number ten", "short": "parenthesized ten"}, "mathspeak": {"default": "parenthesized 10"}}, "key": "247D"}, {"category": "No", "mappings": {"default": {"default": "parenthesized number eleven", "short": "parenthesized eleven"}, "mathspeak": {"default": "parenthesized 11"}}, "key": "247E"}, {"category": "No", "mappings": {"default": {"default": "parenthesized number twelve", "short": "parenthesized twelve"}, "mathspeak": {"default": "parenthesized 12"}}, "key": "247F"}, {"category": "No", "mappings": {"default": {"default": "parenthesized number thirteen", "short": "parenthesized thirteen"}, "mathspeak": {"default": "parenthesized 13"}}, "key": "2480"}, {"category": "No", "mappings": {"default": {"default": "parenthesized number fourteen", "short": "parenthesized fourteen"}, "mathspeak": {"default": "parenthesized 14"}}, "key": "2481"}, {"category": "No", "mappings": {"default": {"default": "parenthesized number fifteen", "short": "parenthesized fifteen"}, "mathspeak": {"default": "parenthesized 15"}}, "key": "2482"}, {"category": "No", "mappings": {"default": {"default": "parenthesized number sixteen", "short": "parenthesized sixteen"}, "mathspeak": {"default": "parenthesized 16"}}, "key": "2483"}, {"category": "No", "mappings": {"default": {"default": "parenthesized number seventeen", "short": "parenthesized seventeen"}, "mathspeak": {"default": "parenthesized 17"}}, "key": "2484"}, {"category": "No", "mappings": {"default": {"default": "parenthesized number eighteen", "short": "parenthesized eighteen"}, "mathspeak": {"default": "parenthesized 18"}}, "key": "2485"}, {"category": "No", "mappings": {"default": {"default": "parenthesized number nineteen", "short": "parenthesized nineteen"}, "mathspeak": {"default": "parenthesized 19"}}, "key": "2486"}, {"category": "No", "mappings": {"default": {"default": "parenthesized number twenty", "short": "parenthesized twenty"}, "mathspeak": {"default": "parenthesized 20"}}, "key": "2487"}, {"category": "No", "mappings": {"default": {"default": "digit one full stop", "alternative": "digit one period", "short": "one period"}, "mathspeak": {"default": "1 period"}}, "key": "2488"}, {"category": "No", "mappings": {"default": {"default": "digit two full stop", "alternative": "digit two period", "short": "two period"}, "mathspeak": {"default": "2 period"}}, "key": "2489"}, {"category": "No", "mappings": {"default": {"default": "digit three full stop", "alternative": "digit three period", "short": "three period"}, "mathspeak": {"default": "3 period"}}, "key": "248A"}, {"category": "No", "mappings": {"default": {"default": "digit four full stop", "alternative": "digit four period", "short": "four period"}, "mathspeak": {"default": "4 period"}}, "key": "248B"}, {"category": "No", "mappings": {"default": {"default": "digit five full stop", "alternative": "digit five period", "short": "five period"}, "mathspeak": {"default": "5 period"}}, "key": "248C"}, {"category": "No", "mappings": {"default": {"default": "digit six full stop", "alternative": "digit six period", "short": "six period"}, "mathspeak": {"default": "6 period"}}, "key": "248D"}, {"category": "No", "mappings": {"default": {"default": "digit seven full stop", "alternative": "digit seven period", "short": "seven period"}, "mathspeak": {"default": "7 period"}}, "key": "248E"}, {"category": "No", "mappings": {"default": {"default": "digit eight full stop", "alternative": "digit eight period", "short": "eight period"}, "mathspeak": {"default": "8 period"}}, "key": "248F"}, {"category": "No", "mappings": {"default": {"default": "digit nine full stop", "alternative": "digit nine period", "short": "nine period"}, "mathspeak": {"default": "9 period"}}, "key": "2490"}, {"category": "No", "mappings": {"default": {"default": "number ten full stop", "alternative": "number ten period", "short": "ten period"}, "mathspeak": {"default": "10 period"}}, "key": "2491"}, {"category": "No", "mappings": {"default": {"default": "number eleven full stop", "alternative": "number eleven period", "short": "eleven period"}, "mathspeak": {"default": "11 period"}}, "key": "2492"}, {"category": "No", "mappings": {"default": {"default": "number twelve full stop", "alternative": "number twelve period", "short": "twelve period"}, "mathspeak": {"default": "12 period"}}, "key": "2493"}, {"category": "No", "mappings": {"default": {"default": "number thirteen full stop", "alternative": "number thirteen period", "short": "thirteen period"}, "mathspeak": {"default": "13 period"}}, "key": "2494"}, {"category": "No", "mappings": {"default": {"default": "number fourteen full stop", "alternative": "number fourteen period", "short": "fourteen period"}, "mathspeak": {"default": "14 period"}}, "key": "2495"}, {"category": "No", "mappings": {"default": {"default": "number fifteen full stop", "alternative": "number fifteen period", "short": "fifteen period"}, "mathspeak": {"default": "15 period"}}, "key": "2496"}, {"category": "No", "mappings": {"default": {"default": "number sixteen full stop", "alternative": "number sixteen period", "short": "sixteen period"}, "mathspeak": {"default": "16 period"}}, "key": "2497"}, {"category": "No", "mappings": {"default": {"default": "number seventeen full stop", "alternative": "number seventeen period", "short": "seventeen period"}, "mathspeak": {"default": "17 period"}}, "key": "2498"}, {"category": "No", "mappings": {"default": {"default": "number eighteen full stop", "alternative": "number eighteen period", "short": "eighteen period"}, "mathspeak": {"default": "18 period"}}, "key": "2499"}, {"category": "No", "mappings": {"default": {"default": "number nineteen full stop", "alternative": "number nineteen period", "short": "nineteen period"}, "mathspeak": {"default": "19 period"}}, "key": "249A"}, {"category": "No", "mappings": {"default": {"default": "number twenty full stop", "alternative": "number twenty period", "short": "twenty period"}, "mathspeak": {"default": "20 period"}}, "key": "249B"}, {"category": "No", "mappings": {"default": {"default": "circled digit zero", "short": "circled zero"}, "mathspeak": {"default": "circled 0"}}, "key": "24EA"}, {"category": "No", "mappings": {"default": {"default": "negative circled number eleven", "short": "negative circled eleven"}, "mathspeak": {"default": "negative circled 11"}}, "key": "24EB"}, {"category": "No", "mappings": {"default": {"default": "negative circled number twelve", "short": "negative circled twelve"}, "mathspeak": {"default": "negative circled 12"}}, "key": "24EC"}, {"category": "No", "mappings": {"default": {"default": "negative circled number thirteen", "short": "negative circled thirteen"}, "mathspeak": {"default": "negative circled 13"}}, "key": "24ED"}, {"category": "No", "mappings": {"default": {"default": "negative circled number fourteen", "short": "negative circled fourteen"}, "mathspeak": {"default": "negative circled 14"}}, "key": "24EE"}, {"category": "No", "mappings": {"default": {"default": "negative circled number fifteen", "short": "negative circled fifteen"}, "mathspeak": {"default": "negative circled 15"}}, "key": "24EF"}, {"category": "No", "mappings": {"default": {"default": "negative circled number sixteen", "short": "negative circled sixteen"}, "mathspeak": {"default": "negative circled 16"}}, "key": "24F0"}, {"category": "No", "mappings": {"default": {"default": "negative circled number seventeen", "short": "negative circled seventeen"}, "mathspeak": {"default": "negative circled 17"}}, "key": "24F1"}, {"category": "No", "mappings": {"default": {"default": "negative circled number eighteen", "short": "negative circled eighteen"}, "mathspeak": {"default": "negative circled 18"}}, "key": "24F2"}, {"category": "No", "mappings": {"default": {"default": "negative circled number nineteen", "short": "negative circled nineteen"}, "mathspeak": {"default": "negative circled 19"}}, "key": "24F3"}, {"category": "No", "mappings": {"default": {"default": "negative circled number twenty", "short": "negative circled twenty"}, "mathspeak": {"default": "negative circled 20"}}, "key": "24F4"}, {"category": "No", "mappings": {"default": {"default": "double circled digit one", "short": "double circled one"}, "mathspeak": {"default": "double circled 1"}}, "key": "24F5"}, {"category": "No", "mappings": {"default": {"default": "double circled digit two", "short": "double circled two"}, "mathspeak": {"default": "double circled 2"}}, "key": "24F6"}, {"category": "No", "mappings": {"default": {"default": "double circled digit three", "short": "double circled three"}, "mathspeak": {"default": "double circled 3"}}, "key": "24F7"}, {"category": "No", "mappings": {"default": {"default": "double circled digit four", "short": "double circled four"}, "mathspeak": {"default": "double circled 4"}}, "key": "24F8"}, {"category": "No", "mappings": {"default": {"default": "double circled digit five", "short": "double circled five"}, "mathspeak": {"default": "double circled 5"}}, "key": "24F9"}, {"category": "No", "mappings": {"default": {"default": "double circled digit six", "short": "double circled six"}, "mathspeak": {"default": "double circled 6"}}, "key": "24FA"}, {"category": "No", "mappings": {"default": {"default": "double circled digit seven", "short": "double circled seven"}, "mathspeak": {"default": "double circled 7"}}, "key": "24FB"}, {"category": "No", "mappings": {"default": {"default": "double circled digit eight", "short": "double circled eight"}, "mathspeak": {"default": "double circled 8"}}, "key": "24FC"}, {"category": "No", "mappings": {"default": {"default": "double circled digit nine", "short": "double circled nine"}, "mathspeak": {"default": "double circled 9"}}, "key": "24FD"}, {"category": "No", "mappings": {"default": {"default": "double circled number ten", "short": "double circled ten"}, "mathspeak": {"default": "double circled 10"}}, "key": "24FE"}, {"category": "No", "mappings": {"default": {"default": "negative circled digit zero", "short": "negative circled zero"}, "mathspeak": {"default": "negative circled 0"}}, "key": "24FF"}, {"category": "No", "mappings": {"default": {"default": "dingbat negative circled digit one", "alternative": "inverse circled digit one", "short": "inverse circled one"}, "mathspeak": {"default": "inverse circled 1"}}, "key": "2776"}, {"category": "No", "mappings": {"default": {"default": "dingbat negative circled digit two", "alternative": "inverse circled digit two", "short": "inverse circled two"}, "mathspeak": {"default": "inverse circled 2"}}, "key": "2777"}, {"category": "No", "mappings": {"default": {"default": "dingbat negative circled digit three", "alternative": "inverse circled digit three", "short": "inverse circled three"}, "mathspeak": {"default": "inverse circled 3"}}, "key": "2778"}, {"category": "No", "mappings": {"default": {"default": "dingbat negative circled digit four", "alternative": "inverse circled digit four", "short": "inverse circled four"}, "mathspeak": {"default": "inverse circled 4"}}, "key": "2779"}, {"category": "No", "mappings": {"default": {"default": "dingbat negative circled digit five", "alternative": "inverse circled digit five", "short": "inverse circled five"}, "mathspeak": {"default": "inverse circled 5"}}, "key": "277A"}, {"category": "No", "mappings": {"default": {"default": "dingbat negative circled digit six", "alternative": "inverse circled digit six", "short": "inverse circled six"}, "mathspeak": {"default": "inverse circled 6"}}, "key": "277B"}, {"category": "No", "mappings": {"default": {"default": "dingbat negative circled digit seven", "alternative": "inverse circled digit seven", "short": "inverse circled seven"}, "mathspeak": {"default": "inverse circled 7"}}, "key": "277C"}, {"category": "No", "mappings": {"default": {"default": "dingbat negative circled digit eight", "alternative": "inverse circled digit eight", "short": "inverse circled eight"}, "mathspeak": {"default": "inverse circled 8"}}, "key": "277D"}, {"category": "No", "mappings": {"default": {"default": "dingbat negative circled digit nine", "alternative": "inverse circled digit nine", "short": "inverse circled nine"}, "mathspeak": {"default": "inverse circled 9"}}, "key": "277E"}, {"category": "No", "mappings": {"default": {"default": "dingbat negative circled number ten", "alternative": "inverse circled number ten", "short": "inverse circled ten"}, "mathspeak": {"default": "inverse circled 10"}}, "key": "277F"}, {"category": "No", "mappings": {"default": {"default": "dingbat circled sans serif digit one", "alternative": "circled sans serif digit one", "short": "circled one"}}, "key": "2780"}, {"category": "No", "mappings": {"default": {"default": "dingbat circled sans serif digit two", "alternative": "circled sans serif digit two", "short": "circled two"}}, "key": "2781"}, {"category": "No", "mappings": {"default": {"default": "ding<PERSON> circled sans serif digit three", "alternative": "circled sans serif digit three", "short": "circled three"}}, "key": "2782"}, {"category": "No", "mappings": {"default": {"default": "ding<PERSON> circled sans serif digit four", "alternative": "circled sans serif digit four", "short": "circled four"}}, "key": "2783"}, {"category": "No", "mappings": {"default": {"default": "ding<PERSON> circled sans serif digit five", "alternative": "circled sans serif digit five", "short": "circled five"}}, "key": "2784"}, {"category": "No", "mappings": {"default": {"default": "ding<PERSON> circled sans serif digit six", "alternative": "circled sans serif digit six", "short": "circled six"}}, "key": "2785"}, {"category": "No", "mappings": {"default": {"default": "ding<PERSON> circled sans serif digit seven", "alternative": "circled sans serif digit seven", "short": "circled seven"}}, "key": "2786"}, {"category": "No", "mappings": {"default": {"default": "ding<PERSON> circled sans serif digit eight", "alternative": "circled sans serif digit eight", "short": "circled eight"}}, "key": "2787"}, {"category": "No", "mappings": {"default": {"default": "ding<PERSON> circled sans serif digit nine", "alternative": "circled sans serif digit nine", "short": "circled nine"}}, "key": "2788"}, {"category": "No", "mappings": {"default": {"default": "dingbat circled sans serif number ten", "alternative": "circled sans serif number ten", "short": "circled ten"}}, "key": "2789"}, {"category": "No", "mappings": {"default": {"default": "dingbat negative circled sans serif digit one", "alternative": "inverse circled sans serif digit one", "short": "inverse circled one"}, "mathspeak": {"default": "inverse circled 1"}}, "key": "278A"}, {"category": "No", "mappings": {"default": {"default": "dingbat negative circled sans serif digit two", "alternative": "inverse circled sans serif digit two", "short": "inverse circled two"}, "mathspeak": {"default": "inverse circled 2"}}, "key": "278B"}, {"category": "No", "mappings": {"default": {"default": "dingbat negative circled sans serif digit three", "alternative": "inverse circled sans serif digit three", "short": "inverse circled three"}, "mathspeak": {"default": "inverse circled 3"}}, "key": "278C"}, {"category": "No", "mappings": {"default": {"default": "dingbat negative circled sans serif digit four", "alternative": "inverse circled sans serif digit four", "short": "inverse circled four"}, "mathspeak": {"default": "inverse circled 4"}}, "key": "278D"}, {"category": "No", "mappings": {"default": {"default": "dingbat negative circled sans serif digit five", "alternative": "inverse circled sans serif digit five", "short": "inverse circled five"}, "mathspeak": {"default": "inverse circled 5"}}, "key": "278E"}, {"category": "No", "mappings": {"default": {"default": "dingbat negative circled sans serif digit six", "alternative": "inverse circled sans serif digit six", "short": "inverse circled six"}, "mathspeak": {"default": "inverse circled 6"}}, "key": "278F"}, {"category": "No", "mappings": {"default": {"default": "dingbat negative circled sans serif digit seven", "alternative": "inverse circled sans serif digit seven", "short": "inverse circled seven"}, "mathspeak": {"default": "inverse circled 7"}}, "key": "2790"}, {"category": "No", "mappings": {"default": {"default": "dingbat negative circled sans serif digit eight", "alternative": "inverse circled sans serif digit eight", "short": "inverse circled eight"}, "mathspeak": {"default": "inverse circled 8"}}, "key": "2791"}, {"category": "No", "mappings": {"default": {"default": "dingbat negative circled sans serif digit nine", "alternative": "inverse circled sans serif digit nine", "short": "inverse circled nine"}, "mathspeak": {"default": "inverse circled 9"}}, "key": "2792"}, {"category": "No", "mappings": {"default": {"default": "dingbat negative circled sans serif number ten", "alternative": "inverse circled sans serif number ten", "short": "inverse circled ten"}, "mathspeak": {"default": "inverse circled 10"}}, "key": "2793"}, {"category": "No", "mappings": {"default": {"default": "circled number ten on black square", "short": "circled ten on black square"}}, "key": "3248"}, {"category": "No", "mappings": {"default": {"default": "circled number twenty on black square", "short": "circled twenty on black square"}}, "key": "3249"}, {"category": "No", "mappings": {"default": {"default": "circled number thirty on black square", "short": "circled thirty on black square"}}, "key": "324A"}, {"category": "No", "mappings": {"default": {"default": "circled number forty on black square", "short": "circled forty on black square"}}, "key": "324B"}, {"category": "No", "mappings": {"default": {"default": "circled number fifty on black square", "short": "circled fifty on black square"}}, "key": "324C"}, {"category": "No", "mappings": {"default": {"default": "circled number sixty on black square", "short": "circled sixty on black square"}}, "key": "324D"}, {"category": "No", "mappings": {"default": {"default": "circled number seventy on black square", "short": "circled seventy on black square"}}, "key": "324E"}, {"category": "No", "mappings": {"default": {"default": "circled number eighty on black square", "short": "circled eighty on black square"}}, "key": "324F"}, {"category": "No", "mappings": {"default": {"default": "circled number twenty one", "short": "circled twenty one"}}, "key": "3251"}, {"category": "No", "mappings": {"default": {"default": "circled number twenty two", "short": "circled twenty two"}}, "key": "3252"}, {"category": "No", "mappings": {"default": {"default": "circled number twenty three", "short": "circled twenty three"}}, "key": "3253"}, {"category": "No", "mappings": {"default": {"default": "circled number twenty four", "short": "circled twenty four"}}, "key": "3254"}, {"category": "No", "mappings": {"default": {"default": "circled number twenty five", "short": "circled twenty five"}}, "key": "3255"}, {"category": "No", "mappings": {"default": {"default": "circled number twenty six", "short": "circled twenty six"}}, "key": "3256"}, {"category": "No", "mappings": {"default": {"default": "circled number twenty seven", "short": "circled twenty seven"}}, "key": "3257"}, {"category": "No", "mappings": {"default": {"default": "circled number twenty eight", "short": "circled twenty eight"}}, "key": "3258"}, {"category": "No", "mappings": {"default": {"default": "circled number twenty nine", "short": "circled twenty nine"}}, "key": "3259"}, {"category": "No", "mappings": {"default": {"default": "circled number thirty", "short": "circled thirty"}}, "key": "325A"}, {"category": "No", "mappings": {"default": {"default": "circled number thirty one", "short": "circled thirty one"}}, "key": "325B"}, {"category": "No", "mappings": {"default": {"default": "circled number thirty two", "short": "circled thirty two"}}, "key": "325C"}, {"category": "No", "mappings": {"default": {"default": "circled number thirty three", "short": "circled thirty three"}}, "key": "325D"}, {"category": "No", "mappings": {"default": {"default": "circled number thirty four", "short": "circled thirty four"}}, "key": "325E"}, {"category": "No", "mappings": {"default": {"default": "circled number thirty five", "short": "circled thirty five"}}, "key": "325F"}, {"category": "No", "mappings": {"default": {"default": "circled number thirty six", "short": "circled thirty six"}}, "key": "32B1"}, {"category": "No", "mappings": {"default": {"default": "circled number thirty seven", "short": "circled thirty seven"}}, "key": "32B2"}, {"category": "No", "mappings": {"default": {"default": "circled number thirty eight", "short": "circled thirty eight"}}, "key": "32B3"}, {"category": "No", "mappings": {"default": {"default": "circled number thirty nine", "short": "circled thirty nine"}}, "key": "32B4"}, {"category": "No", "mappings": {"default": {"default": "circled number forty", "short": "circled forty"}}, "key": "32B5"}, {"category": "No", "mappings": {"default": {"default": "circled number forty one", "short": "circled forty one"}}, "key": "32B6"}, {"category": "No", "mappings": {"default": {"default": "circled number forty two", "short": "circled forty two"}}, "key": "32B7"}, {"category": "No", "mappings": {"default": {"default": "circled number forty three", "short": "circled forty three"}}, "key": "32B8"}, {"category": "No", "mappings": {"default": {"default": "circled number forty four", "short": "circled forty four"}}, "key": "32B9"}, {"category": "No", "mappings": {"default": {"default": "circled number forty five", "short": "circled forty five"}}, "key": "32BA"}, {"category": "No", "mappings": {"default": {"default": "circled number forty six", "short": "circled forty six"}}, "key": "32BB"}, {"category": "No", "mappings": {"default": {"default": "circled number forty seven", "short": "circled forty seven"}}, "key": "32BC"}, {"category": "No", "mappings": {"default": {"default": "circled number forty eight", "short": "circled forty eight"}}, "key": "32BD"}, {"category": "No", "mappings": {"default": {"default": "circled number forty nine", "short": "circled forty nine"}}, "key": "32BE"}, {"category": "No", "mappings": {"default": {"default": "circled number fifty", "short": "circled fifty"}}, "key": "32BF"}, {"category": "Nd", "mappings": {"default": {"default": "fullwidth digit zero", "short": "zero"}, "mathspeak": {"default": "0"}}, "key": "FF10"}, {"category": "Nd", "mappings": {"default": {"default": "fullwidth digit one", "short": "one"}, "mathspeak": {"default": "1"}}, "key": "FF11"}, {"category": "Nd", "mappings": {"default": {"default": "fullwidth digit two", "short": "two"}, "mathspeak": {"default": "2"}}, "key": "FF12"}, {"category": "Nd", "mappings": {"default": {"default": "fullwidth digit three", "short": "three"}, "mathspeak": {"default": "3"}}, "key": "FF13"}, {"category": "Nd", "mappings": {"default": {"default": "fullwidth digit four", "short": "four"}, "mathspeak": {"default": "4"}}, "key": "FF14"}, {"category": "Nd", "mappings": {"default": {"default": "fullwidth digit five", "short": "five"}, "mathspeak": {"default": "5"}}, "key": "FF15"}, {"category": "Nd", "mappings": {"default": {"default": "fullwidth digit six", "short": "six"}, "mathspeak": {"default": "6"}}, "key": "FF16"}, {"category": "Nd", "mappings": {"default": {"default": "fullwidth digit seven", "short": "seven"}, "mathspeak": {"default": "7"}}, "key": "FF17"}, {"category": "Nd", "mappings": {"default": {"default": "fullwidth digit eight", "short": "eight"}, "mathspeak": {"default": "8"}}, "key": "FF18"}, {"category": "Nd", "mappings": {"default": {"default": "fullwidth digit nine", "short": "nine"}, "mathspeak": {"default": "9"}}, "key": "FF19"}, {"category": "Nd", "mappings": {"default": {"default": "mathematical bold digit zero", "alternative": "bold digit zero", "short": "bold zero"}, "mathspeak": {"default": "bold 0"}}, "key": "1D7CE"}, {"category": "Nd", "mappings": {"default": {"default": "mathematical bold digit one", "alternative": "bold digit one", "short": "bold one"}, "mathspeak": {"default": "bold 1"}}, "key": "1D7CF"}, {"category": "Nd", "mappings": {"default": {"default": "mathematical bold digit two", "alternative": "bold digit two", "short": "bold two"}, "mathspeak": {"default": "bold 2"}}, "key": "1D7D0"}, {"category": "Nd", "mappings": {"default": {"default": "mathematical bold digit three", "alternative": "bold digit three", "short": "bold three"}, "mathspeak": {"default": "bold 3"}}, "key": "1D7D1"}, {"category": "Nd", "mappings": {"default": {"default": "mathematical bold digit four", "alternative": "bold digit four", "short": "bold four"}, "mathspeak": {"default": "bold 4"}}, "key": "1D7D2"}, {"category": "Nd", "mappings": {"default": {"default": "mathematical bold digit five", "alternative": "bold digit five", "short": "bold five"}, "mathspeak": {"default": "bold 5"}}, "key": "1D7D3"}, {"category": "Nd", "mappings": {"default": {"default": "mathematical bold digit six", "alternative": "bold digit six", "short": "bold six"}, "mathspeak": {"default": "bold 6"}}, "key": "1D7D4"}, {"category": "Nd", "mappings": {"default": {"default": "mathematical bold digit seven", "alternative": "bold digit seven", "short": "bold seven"}, "mathspeak": {"default": "bold 7"}}, "key": "1D7D5"}, {"category": "Nd", "mappings": {"default": {"default": "mathematical bold digit eight", "alternative": "bold digit eight", "short": "bold eight"}, "mathspeak": {"default": "bold 8"}}, "key": "1D7D6"}, {"category": "Nd", "mappings": {"default": {"default": "mathematical bold digit nine", "alternative": "bold digit nine", "short": "bold nine"}, "mathspeak": {"default": "bold 9"}}, "key": "1D7D7"}, {"category": "Nd", "mappings": {"default": {"default": "mathematical double struck digit zero", "alternative": "double struck zero", "short": "blackboard zero"}, "mathspeak": {"default": "blackboard 0"}}, "key": "1D7D8"}, {"category": "Nd", "mappings": {"default": {"default": "mathematical double struck digit one", "alternative": "double struck one", "short": "blackboard one"}, "mathspeak": {"default": "blackboard 1"}}, "key": "1D7D9"}, {"category": "Nd", "mappings": {"default": {"default": "mathematical double struck digit two", "alternative": "double struck two", "short": "blackboard two"}, "mathspeak": {"default": "blackboard 2"}}, "key": "1D7DA"}, {"category": "Nd", "mappings": {"default": {"default": "mathematical double struck digit three", "alternative": "double struck three", "short": "blackboard three"}, "mathspeak": {"default": "blackboard 3"}}, "key": "1D7DB"}, {"category": "Nd", "mappings": {"default": {"default": "mathematical double struck digit four", "alternative": "double struck four", "short": "blackboard four"}, "mathspeak": {"default": "blackboard 4"}}, "key": "1D7DC"}, {"category": "Nd", "mappings": {"default": {"default": "mathematical double struck digit five", "alternative": "double struck five", "short": "blackboard five"}, "mathspeak": {"default": "blackboard 5"}}, "key": "1D7DD"}, {"category": "Nd", "mappings": {"default": {"default": "mathematical double struck digit six", "alternative": "double struck six", "short": "blackboard six"}, "mathspeak": {"default": "blackboard 6"}}, "key": "1D7DE"}, {"category": "Nd", "mappings": {"default": {"default": "mathematical double struck digit seven", "alternative": "double struck seven", "short": "blackboard seven"}, "mathspeak": {"default": "blackboard 7"}}, "key": "1D7DF"}, {"category": "Nd", "mappings": {"default": {"default": "mathematical double struck digit eight", "alternative": "double struck eight", "short": "blackboard eight"}, "mathspeak": {"default": "blackboard 8"}}, "key": "1D7E0"}, {"category": "Nd", "mappings": {"default": {"default": "mathematical double struck digit nine", "alternative": "double struck nine", "short": "blackboard nine"}, "mathspeak": {"default": "blackboard 9"}}, "key": "1D7E1"}, {"category": "Nd", "mappings": {"default": {"default": "mathematical sans serif digit zero", "alternative": "sans serif digit zero", "short": "sans serif zero"}, "mathspeak": {"default": "sans serif 0"}}, "key": "1D7E2"}, {"category": "Nd", "mappings": {"default": {"default": "mathematical sans serif digit one", "alternative": "sans serif digit one", "short": "sans serif one"}, "mathspeak": {"default": "sans serif 1"}}, "key": "1D7E3"}, {"category": "Nd", "mappings": {"default": {"default": "mathematical sans serif digit two", "alternative": "sans serif digit two", "short": "sans serif two"}, "mathspeak": {"default": "sans serif 2"}}, "key": "1D7E4"}, {"category": "Nd", "mappings": {"default": {"default": "mathematical sans serif digit three", "alternative": "sans serif digit three", "short": "sans serif three"}, "mathspeak": {"default": "sans serif 3"}}, "key": "1D7E5"}, {"category": "Nd", "mappings": {"default": {"default": "mathematical sans serif digit four", "alternative": "sans serif digit four", "short": "sans serif four"}, "mathspeak": {"default": "sans serif 4"}}, "key": "1D7E6"}, {"category": "Nd", "mappings": {"default": {"default": "mathematical sans serif digit five", "alternative": "sans serif digit five", "short": "sans serif five"}, "mathspeak": {"default": "sans serif 5"}}, "key": "1D7E7"}, {"category": "Nd", "mappings": {"default": {"default": "mathematical sans serif digit six", "alternative": "sans serif digit six", "short": "sans serif six"}, "mathspeak": {"default": "sans serif 6"}}, "key": "1D7E8"}, {"category": "Nd", "mappings": {"default": {"default": "mathematical sans serif digit seven", "alternative": "sans serif digit seven", "short": "sans serif seven"}, "mathspeak": {"default": "sans serif 7"}}, "key": "1D7E9"}, {"category": "Nd", "mappings": {"default": {"default": "mathematical sans serif digit eight", "alternative": "sans serif digit eight", "short": "sans serif eight"}, "mathspeak": {"default": "sans serif 8"}}, "key": "1D7EA"}, {"category": "Nd", "mappings": {"default": {"default": "mathematical sans serif digit nine", "alternative": "sans serif digit nine", "short": "sans serif nine"}, "mathspeak": {"default": "sans serif 9"}}, "key": "1D7EB"}, {"category": "Nd", "mappings": {"default": {"default": "mathematical sans serif bold digit zero", "alternative": "sans serif bold digit zero", "short": "sans serif bold zero"}, "mathspeak": {"default": "sans serif bold 0"}}, "key": "1D7EC"}, {"category": "Nd", "mappings": {"default": {"default": "mathematical sans serif bold digit one", "alternative": "sans serif bold digit one", "short": "sans serif bold one"}, "mathspeak": {"default": "sans serif bold 1"}}, "key": "1D7ED"}, {"category": "Nd", "mappings": {"default": {"default": "mathematical sans serif bold digit two", "alternative": "sans serif bold digit two", "short": "sans serif bold two"}, "mathspeak": {"default": "sans serif bold 2"}}, "key": "1D7EE"}, {"category": "Nd", "mappings": {"default": {"default": "mathematical sans serif bold digit three", "alternative": "sans serif bold digit three", "short": "sans serif bold three"}, "mathspeak": {"default": "sans serif bold 3"}}, "key": "1D7EF"}, {"category": "Nd", "mappings": {"default": {"default": "mathematical sans serif bold digit four", "alternative": "sans serif bold digit four", "short": "sans serif bold four"}, "mathspeak": {"default": "sans serif bold 4"}}, "key": "1D7F0"}, {"category": "Nd", "mappings": {"default": {"default": "mathematical sans serif bold digit five", "alternative": "sans serif bold digit five", "short": "sans serif bold five"}, "mathspeak": {"default": "sans serif bold 5"}}, "key": "1D7F1"}, {"category": "Nd", "mappings": {"default": {"default": "mathematical sans serif bold digit six", "alternative": "sans serif bold digit six", "short": "sans serif bold six"}, "mathspeak": {"default": "sans serif bold 6"}}, "key": "1D7F2"}, {"category": "Nd", "mappings": {"default": {"default": "mathematical sans serif bold digit seven", "alternative": "sans serif bold digit seven", "short": "sans serif bold seven"}, "mathspeak": {"default": "sans serif bold 7"}}, "key": "1D7F3"}, {"category": "Nd", "mappings": {"default": {"default": "mathematical sans serif bold digit eight", "alternative": "sans serif bold digit eight", "short": "sans serif bold eight"}, "mathspeak": {"default": "sans serif bold 8"}}, "key": "1D7F4"}, {"category": "Nd", "mappings": {"default": {"default": "mathematical sans serif bold digit nine", "alternative": "sans serif bold digit nine", "short": "sans serif bold nine"}, "mathspeak": {"default": "sans serif bold 9"}}, "key": "1D7F5"}, {"category": "Nd", "mappings": {"default": {"default": "mathematical monospace digit zero", "alternative": "monospace zero", "short": "thin zero"}, "mathspeak": {"default": "monospace 0"}}, "key": "1D7F6"}, {"category": "Nd", "mappings": {"default": {"default": "mathematical monospace digit one", "alternative": "monospace one", "short": "thin one"}, "mathspeak": {"default": "monospace 1"}}, "key": "1D7F7"}, {"category": "Nd", "mappings": {"default": {"default": "mathematical monospace digit two", "alternative": "monospace two", "short": "thin two"}, "mathspeak": {"default": "monospace 2"}}, "key": "1D7F8"}, {"category": "Nd", "mappings": {"default": {"default": "mathematical monospace digit three", "alternative": "monospace three", "short": "thin three"}, "mathspeak": {"default": "monospace 3"}}, "key": "1D7F9"}, {"category": "Nd", "mappings": {"default": {"default": "mathematical monospace digit four", "alternative": "monospace four", "short": "thin four"}, "mathspeak": {"default": "monospace 4"}}, "key": "1D7FA"}, {"category": "Nd", "mappings": {"default": {"default": "mathematical monospace digit five", "alternative": "monospace five", "short": "thin five"}, "mathspeak": {"default": "monospace 5"}}, "key": "1D7FB"}, {"category": "Nd", "mappings": {"default": {"default": "mathematical monospace digit six", "alternative": "monospace six", "short": "thin six"}, "mathspeak": {"default": "monospace 6"}}, "key": "1D7FC"}, {"category": "Nd", "mappings": {"default": {"default": "mathematical monospace digit seven", "alternative": "monospace seven", "short": "thin seven"}, "mathspeak": {"default": "monospace 7"}}, "key": "1D7FD"}, {"category": "Nd", "mappings": {"default": {"default": "mathematical monospace digit eight", "alternative": "monospace eight", "short": "thin eight"}, "mathspeak": {"default": "monospace 8"}}, "key": "1D7FE"}, {"category": "Nd", "mappings": {"default": {"default": "mathematical monospace digit nine", "alternative": "monospace nine", "short": "thin nine"}, "mathspeak": {"default": "monospace 9"}}, "key": "1D7FF"}, {"category": "No", "mappings": {"default": {"default": "digit zero full stop", "short": "zero period"}, "mathspeak": {"default": "0 period"}}, "key": "1F100"}, {"category": "No", "mappings": {"default": {"default": "digit zero comma", "short": "zero comma"}, "mathspeak": {"default": "0 comma"}}, "key": "1F101"}, {"category": "No", "mappings": {"default": {"default": "digit one comma", "short": "one comma"}, "mathspeak": {"default": "1 comma"}}, "key": "1F102"}, {"category": "No", "mappings": {"default": {"default": "digit two comma", "short": "two comma"}, "mathspeak": {"default": "2 comma"}}, "key": "1F103"}, {"category": "No", "mappings": {"default": {"default": "digit three comma", "short": "three comma"}, "mathspeak": {"default": "3 comma"}}, "key": "1F104"}, {"category": "No", "mappings": {"default": {"default": "digit four comma", "short": "four comma"}, "mathspeak": {"default": "4 comma"}}, "key": "1F105"}, {"category": "No", "mappings": {"default": {"default": "digit five comma", "short": "five comma"}, "mathspeak": {"default": "5 comma"}}, "key": "1F106"}, {"category": "No", "mappings": {"default": {"default": "digit six comma", "short": "six comma"}, "mathspeak": {"default": "6 comma"}}, "key": "1F107"}, {"category": "No", "mappings": {"default": {"default": "digit seven comma", "short": "seven comma"}, "mathspeak": {"default": "7 comma"}}, "key": "1F108"}, {"category": "No", "mappings": {"default": {"default": "digit eight comma", "short": "eight comma"}, "mathspeak": {"default": "8 comma"}}, "key": "1F109"}, {"category": "No", "mappings": {"default": {"default": "digit nine comma", "short": "nine comma"}, "mathspeak": {"default": "9 comma"}}, "key": "1F10A"}]