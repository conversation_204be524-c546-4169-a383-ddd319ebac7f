# Guessy - Complete Local Development Setup Guide

## 🎯 About Guessy

**Guessy** is a comprehensive quiz application featuring:
- Multiple quiz types (General Knowledge, Guess the Word, Audio Questions, Math Mania)
- Real-time multiplayer battles
- Daily challenges and contests
- Coin-based reward system
- Achievement badges
- Leaderboards and statistics

## 🚀 Quick Setup (Automated)

### For Windows Users
```bash
# Run as Administrator
guessy_setup.bat
```

### For macOS/Linux Users
```bash
# Make executable and run
chmod +x guessy_setup.sh
sudo ./guessy_setup.sh
```

## 📋 Manual Setup Guide

### Step 1: Prerequisites

#### Required Software
- **XAMPP** (Apache + MySQL + PHP)
- **Flutter SDK** (3.8.1 or higher)
- **Android Studio** or VS Code
- **Git**
- **Composer** (PHP dependency manager)

#### Download Links
- XAMPP: https://www.apachefriends.org/
- Flutter: https://flutter.dev/docs/get-started/install
- Android Studio: https://developer.android.com/studio

### Step 2: Server Environment Setup

#### Install and Configure XAMPP
1. **Download and Install XAMPP**
   - Install with default settings
   - Start XAMPP Control Panel

2. **Start Services**
   - Start **Apache** service
   - Start **MySQL** service
   - Verify both show green status

3. **Test Installation**
   - Open browser: http://localhost
   - Should show XAMPP dashboard

### Step 3: Database Setup

#### Create Guessy Database
1. **Access phpMyAdmin**
   - URL: http://localhost/phpmyadmin
   - Login (usually no password required)

2. **Create Database**
   ```sql
   CREATE DATABASE guessy_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
   ```

3. **Import Schema**
   - Select `guessy_db` database
   - Go to Import tab
   - Choose file: `adminpanel2.3.4/install/assets/quiz.php`
   - Click "Go" to import

#### Update Database for Guessy
```sql
-- Update app settings for Guessy
UPDATE tbl_settings SET message = 'Guessy - The Ultimate Quiz App' WHERE type = 'app_name';
UPDATE tbl_settings SET message = 'Welcome to Guessy!' WHERE type = 'welcome_message';

-- Update admin credentials (optional)
UPDATE tbl_authenticate SET auth_username = 'guessy_admin' WHERE auth_id = 1;

-- Add Guessy-specific categories
INSERT INTO tbl_category (category_name, image, maxlevel, no_of_que, status) VALUES
('General Knowledge', 'general.png', 10, 15, 1),
('Word Puzzles', 'words.png', 8, 12, 1),
('Math Challenges', 'math.png', 12, 10, 1),
('Science & Tech', 'science.png', 10, 15, 1),
('Sports & Games', 'sports.png', 8, 12, 1),
('Entertainment', 'entertainment.png', 6, 10, 1);
```

### Step 4: Admin Panel Configuration

#### Setup Admin Panel
1. **Copy Admin Panel**
   ```bash
   # Copy to XAMPP htdocs
   cp -r adminpanel2.3.4 C:/xampp/htdocs/guessy_admin
   ```

2. **Configure Database Connection**
   
   Edit `guessy_admin/application/config/database.php`:
   ```php
   $db['default'] = array(
       'dsn' => '',
       'hostname' => 'localhost',
       'username' => 'root',
       'password' => '',
       'database' => 'guessy_db',
       'dbdriver' => 'mysqli',
       'dbprefix' => '',
       'pconnect' => FALSE,
       'db_debug' => (ENVIRONMENT !== 'production'),
       'cache_on' => FALSE,
       'cachedir' => '',
       'char_set' => 'utf8mb4',
       'dbcollat' => 'utf8mb4_unicode_ci',
       'swap_pre' => '',
       'encrypt' => FALSE,
       'compress' => FALSE,
       'stricton' => FALSE,
       'failover' => array(),
       'save_queries' => TRUE
   );
   ```

3. **Configure Base URL**
   
   Edit `guessy_admin/application/config/config.php`:
   ```php
   $config['base_url'] = 'http://localhost/guessy_admin/';
   $config['encryption_key'] = 'guessy_secret_key_2024_secure';
   ```

4. **Test Admin Panel**
   - URL: http://localhost/guessy_admin
   - Login: admin / admin123

### Step 5: Firebase Setup for Guessy

#### Create Firebase Project
1. **Go to Firebase Console**
   - Visit: https://console.firebase.google.com
   - Create project: `guessy-quiz-app`

2. **Enable Services**
   - **Authentication**: Email/Password, Google, Phone
   - **Firestore**: Create database in test mode
   - **Cloud Messaging**: Enable for notifications

3. **Add Android App**
   - Package name: `com.guessy.quiz`
   - App nickname: `Guessy Quiz`
   - Download `google-services.json`

4. **Configure Firestore Rules**
   ```javascript
   rules_version = '2';
   service cloud.firestore {
     match /databases/{database}/documents {
       // Guessy battle rooms
       match /guessy_battles/{roomId} {
         allow read, write: if request.auth != null;
       }
       
       // User data
       match /guessy_users/{userId} {
         allow read, write: if request.auth != null && request.auth.uid == userId;
       }
       
       // Public data
       match /guessy_public/{document=**} {
         allow read: if true;
         allow write: if false;
       }
     }
   }
   ```

### Step 6: Flutter App Configuration

#### Update App Configuration
1. **Change Package Name**
   
   Use Flutter package rename tool:
   ```bash
   cd elite_quiz_app-2.3.4
   dart run change_app_package_name:main com.guessy.quiz
   ```

2. **Update App Configuration**
   
   Edit `lib/core/config/config.dart`:
   ```dart
   /// === Guessy App Configuration ===
   const appName = 'Guessy';
   const packageName = 'com.guessy.quiz';
   
   /// Local Development API URL
   // For Android Emulator
   const panelUrl = 'http://********/guessy_admin';
   
   // For iOS Simulator
   // const panelUrl = 'http://localhost/guessy_admin';
   
   // For Physical Device (replace with your IP)
   // const panelUrl = 'http://*************/guessy_admin';
   
   /// Development Settings
   const bool isLocalDevelopment = true;
   const bool enableApiLogs = true;
   const bool enableDebugMode = true;
   
   /// Guessy Branding
   const Brightness defaultTheme = Brightness.light;
   const String kAppLogo = 'guessy_logo.svg';
   const String kSplashLogo = 'guessy_splash.svg';
   ```

3. **Update Android Manifest**
   
   Edit `android/app/src/main/AndroidManifest.xml`:
   ```xml
   <application
       android:label="Guessy"
       android:icon="@mipmap/ic_launcher"
       android:usesCleartextTraffic="true"
       android:networkSecurityConfig="@xml/network_security_config">
   ```

4. **Add Firebase Config**
   - Copy `google-services.json` to `android/app/`
   - Copy `GoogleService-Info.plist` to `ios/Runner/` (if iOS)

5. **Install Dependencies**
   ```bash
   flutter pub get
   dart run flutter_launcher_icons
   ```

### Step 7: Guessy Branding Updates

#### App Icons and Splash
1. **Update App Icons**
   - Replace icons in `assets/config/launcher_icons/`
   - Run: `dart run flutter_launcher_icons`

2. **Update Splash Screen**
   - Update splash logo in assets
   - Modify splash screen configuration

#### Color Scheme
Edit `lib/core/config/colors.dart`:
```dart
/// Guessy Color Palette
const Color guessyPrimary = Color(0xFF6C63FF);
const Color guessySecondary = Color(0xFF4CAF50);
const Color guessyAccent = Color(0xFFFF9800);
const Color guessyBackground = Color(0xFFF5F5F5);
const Color guessyText = Color(0xFF333333);
```

### Step 8: Testing Guessy Setup

#### Test Admin Panel
1. **Access Admin Panel**
   - URL: http://localhost/guessy_admin
   - Login: admin / admin123

2. **Verify Features**
   - Categories management
   - Questions management
   - User management
   - System settings

#### Test Flutter App
1. **Run App**
   ```bash
   flutter run
   ```

2. **Test Features**
   - App launches with "Guessy" branding
   - Categories load from local server
   - User registration works
   - Quiz gameplay functional

#### Test API Connectivity
```bash
# Test categories API
curl -X POST http://localhost/guessy_admin/Api/get_categories \
  -H "Content-Type: application/json" \
  -d '{"type": 1}'

# Test system config
curl -X POST http://localhost/guessy_admin/Api/get_system_configurations \
  -H "Content-Type: application/json" \
  -d '{}'
```

## 🎨 Customization Options

### App Branding
- Update app name and logo
- Change color scheme
- Modify UI elements
- Add custom sounds

### Content Management
- Add custom categories
- Create unique questions
- Upload category images
- Configure difficulty levels

### Feature Configuration
- Set coin rewards
- Configure badge system
- Setup contest schedules
- Customize game rules

## 🔧 Configuration Files

### Key Files Updated for Guessy
- `lib/core/config/config.dart` - App configuration
- `android/app/src/main/AndroidManifest.xml` - Android settings
- `guessy_admin/application/config/database.php` - Database config
- `guessy_admin/application/config/config.php` - Admin panel config

### Database Configuration
- Database name: `guessy_db`
- Admin panel URL: `http://localhost/guessy_admin`
- API base URL: `http://localhost/guessy_admin/Api`

## 🚀 Quick Commands

### Development Commands
```bash
# Start XAMPP services
sudo /Applications/XAMPP/xamppfiles/xampp start  # macOS
C:\xampp\xampp_start.exe                         # Windows

# Flutter commands
flutter pub get                    # Install dependencies
flutter run                       # Run app
flutter clean                     # Clean build
dart run flutter_launcher_icons   # Generate icons

# Database commands
mysql -u root guessy_db < schema.sql  # Import schema
```

### URLs Reference
- **Admin Panel**: http://localhost/guessy_admin
- **phpMyAdmin**: http://localhost/phpmyadmin
- **API Base**: http://localhost/guessy_admin/Api
- **XAMPP Dashboard**: http://localhost

## 🎯 Success Criteria

Your Guessy setup is successful when:

✅ **Admin Panel**
- Loads with Guessy branding
- Login works (admin/admin123)
- Categories and questions visible
- API endpoints respond correctly

✅ **Flutter App**
- Shows "Guessy" as app name
- Connects to local API
- Categories load properly
- User registration functional

✅ **Database**
- `guessy_db` created successfully
- All tables imported
- Sample data available
- API can read/write data

✅ **Firebase**
- Project `guessy-quiz-app` created
- Authentication working
- Firestore accessible
- Config files in place

## 🆘 Troubleshooting

### Common Issues
1. **Admin panel not loading**: Check Apache service
2. **Database connection failed**: Verify MySQL service and credentials
3. **App can't connect**: Check network configuration and IP address
4. **Firebase auth failed**: Verify config files and project settings

### Debug Commands
```bash
# Check Flutter setup
flutter doctor

# View app logs
flutter logs

# Check XAMPP status
# Windows: Check XAMPP Control Panel
# macOS/Linux: sudo /opt/lampp/lampp status
```

Your **Guessy** quiz application is now ready for local development! 🎉
