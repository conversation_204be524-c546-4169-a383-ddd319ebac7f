/*
 *  /MathJax/fonts/HTML-CSS/TeX/png/Typewriter/Regular/Other.js
 *  
 *  Copyright (c) 2010-2013 The MathJax Consortium
 *
 *  Part of the MathJax library.
 *  See http://www.mathjax.org for details.
 * 
 *  Licensed under the Apache License, Version 2.0;
 *  you may not use this file except in compliance with the License.
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 */

MathJax.OutputJax["HTML-CSS"].defineImageData({MathJax_Typewriter:{160:[[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0]],305:[[4,3,0],[4,4,0],[5,4,0],[6,5,0],[7,6,0],[8,7,0],[9,9,0],[11,10,0],[13,12,0],[16,15,0],[19,17,0],[22,20,0],[26,24,0],[31,28,-1]],567:[[3,5,2],[4,6,2],[4,6,2],[5,8,3],[6,9,3],[7,11,4],[8,14,5],[9,15,5],[11,18,6],[13,23,8],[15,26,9],[18,30,10],[22,36,12],[25,44,15]],768:[[2,1,-3],[3,1,-5],[4,1,-5],[3,2,-5],[4,2,-6],[4,2,-8],[5,3,-10],[6,3,-11],[7,4,-13],[8,5,-16],[9,5,-19],[10,6,-22],[13,7,-27],[15,9,-32]],769:[[3,1,-3],[3,1,-5],[3,1,-5],[3,2,-5],[4,2,-6],[5,2,-8],[5,3,-10],[6,3,-11],[7,4,-13],[8,5,-16],[9,5,-19],[11,6,-22],[13,7,-27],[15,9,-32]],770:[[3,1,-3],[4,2,-4],[5,2,-4],[5,2,-5],[5,2,-6],[7,3,-7],[8,3,-10],[9,4,-10],[10,4,-13],[12,5,-16],[14,6,-18],[17,7,-21],[19,9,-25],[23,10,-31]],771:[[4,1,-3],[4,1,-5],[5,1,-5],[5,2,-5],[6,3,-5],[7,3,-7],[8,3,-10],[9,3,-11],[11,4,-13],[13,5,-16],[15,6,-18],[17,7,-21],[21,8,-26],[24,10,-31]],772:[[4,1,-3],[4,1,-4],[5,1,-4],[6,1,-6],[6,2,-6],[7,3,-7],[8,2,-10],[10,2,-11],[11,3,-14],[13,3,-17],[16,3,-19],[18,4,-23],[21,5,-27],[26,6,-33]],774:[[4,1,-3],[4,1,-5],[5,1,-5],[6,1,-6],[6,2,-6],[7,3,-8],[8,2,-11],[10,3,-11],[11,3,-14],[13,4,-17],[15,5,-19],[18,5,-23],[21,6,-28],[25,7,-34]],776:[[3,1,-3],[4,1,-5],[4,1,-5],[4,2,-5],[5,2,-6],[7,2,-8],[7,2,-11],[8,3,-11],[10,3,-14],[11,4,-17],[13,4,-20],[16,5,-23],[19,6,-28],[22,7,-34]],778:[[2,1,-3],[2,1,-5],[3,1,-5],[3,2,-5],[3,2,-6],[3,2,-8],[4,3,-10],[4,3,-11],[5,3,-14],[7,5,-16],[7,5,-19],[8,6,-22],[10,7,-27],[12,8,-33]],780:[[3,1,-3],[4,1,-4],[5,1,-4],[5,2,-5],[5,2,-6],[7,2,-7],[8,3,-9],[8,3,-10],[10,4,-12],[12,5,-15],[14,5,-17],[16,6,-21],[19,7,-25],[23,9,-30]],915:[[4,4,0],[5,6,0],[5,6,0],[6,7,0],[7,8,0],[9,10,0],[10,13,0],[12,14,0],[14,17,0],[16,21,0],[19,24,0],[23,28,0],[27,34,0],[32,41,0]],916:[[4,4,0],[5,6,0],[5,6,0],[6,7,0],[7,8,0],[9,10,0],[10,13,0],[12,14,0],[14,17,0],[17,21,0],[20,24,0],[23,28,0],[28,34,0],[33,41,0]],920:[[4,4,0],[4,6,0],[5,6,0],[6,7,0],[7,8,0],[8,10,0],[9,13,0],[11,14,0],[13,17,0],[16,21,0],[19,24,0],[22,28,0],[26,34,0],[31,41,0]],923:[[4,4,0],[5,6,0],[5,6,0],[6,7,0],[7,8,0],[9,10,0],[10,13,0],[12,14,0],[14,17,0],[17,21,0],[20,24,0],[23,28,0],[28,34,0],[33,41,0]],926:[[4,4,0],[4,6,0],[5,6,0],[6,7,0],[7,8,0],[9,10,0],[10,13,0],[12,14,0],[14,17,0],[16,21,0],[19,24,0],[23,28,0],[27,33,-1],[32,40,-1]],928:[[4,4,0],[5,6,0],[6,6,0],[7,7,0],[8,8,0],[9,10,0],[11,13,0],[12,14,0],[15,17,0],[17,21,0],[21,24,0],[24,28,0],[29,34,0],[34,41,0]],931:[[4,4,0],[4,6,0],[5,6,0],[6,7,0],[7,8,0],[9,10,0],[10,13,0],[12,14,0],[14,17,0],[16,21,0],[19,24,0],[23,28,0],[27,33,0],[32,40,0]],933:[[4,4,0],[4,6,0],[5,6,0],[6,7,0],[7,8,0],[8,10,0],[10,13,0],[12,14,0],[14,17,0],[16,21,0],[19,24,0],[23,29,0],[27,34,0],[32,41,0]],934:[[4,4,0],[4,6,0],[5,6,0],[6,7,0],[7,8,0],[8,10,0],[10,13,0],[12,14,0],[14,17,0],[16,21,0],[19,24,0],[23,28,0],[27,34,0],[32,41,0]],936:[[4,4,0],[5,6,0],[5,6,0],[6,7,0],[7,8,0],[9,10,0],[10,13,0],[12,14,0],[14,17,0],[16,21,0],[19,24,0],[23,28,0],[27,34,0],[32,41,0]],937:[[4,4,0],[5,6,0],[5,6,0],[6,7,0],[7,8,0],[9,10,0],[10,13,0],[12,14,0],[14,17,0],[17,21,0],[20,24,0],[23,28,0],[28,34,0],[33,42,0]],2018:[[3,3,-1],[3,4,-2],[4,4,-2],[4,5,-3],[5,6,-3],[6,7,-4],[7,7,-5],[9,9,-6],[10,10,-7],[12,12,-9],[14,13,-11],[17,16,-13],[20,19,-15],[23,23,-18]],2019:[[3,3,-2],[3,4,-2],[4,4,-3],[5,4,-4],[5,6,-4],[6,7,-5],[7,8,-6],[9,8,-8],[10,10,-9],[12,12,-11],[14,14,-13],[17,16,-16],[20,19,-19],[24,22,-23]],8242:[[3,2,-2],[3,3,-3],[4,3,-3],[4,4,-3],[5,4,-4],[6,5,-5],[7,6,-7],[8,7,-7],[9,8,-9],[11,10,-11],[13,12,-12],[15,14,-14],[18,16,-18],[21,20,-21]]}});MathJax.Ajax.loadComplete(MathJax.OutputJax["HTML-CSS"].imgDir+"/Typewriter/Regular"+MathJax.OutputJax["HTML-CSS"].imgPacked+"/Other.js");

