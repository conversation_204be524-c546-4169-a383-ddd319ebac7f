/*************************************************************
 *
 *  MathJax/fonts/HTML-CSS/TeX/png/Fraktur/Regular/BasicLatin.js
 *  
 *  Defines the image size data needed for the HTML-CSS OutputJax
 *  to display mathematics using fallback images when the fonts
 *  are not available to the client browser.
 *
 *  ---------------------------------------------------------------------
 *
 *  Copyright (c) 2009-2013 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 *
 */

MathJax.OutputJax["HTML-CSS"].defineImageData({
  "MathJax_Fraktur": {
    0x20: [  // SPACE
      [1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],
      [1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0]
    ],
    0x21: [  // EXCLAMATION MARK
      [2,4,0],[2,6,0],[2,7,0],[3,9,0],[3,10,0],[4,12,0],[4,13,0],[5,17,1],
      [6,20,1],[7,24,1],[8,28,1],[10,33,1],[12,39,2],[14,46,1]
    ],
    0x22: [  // QUOTATION MARK
      [2,2,-2],[2,3,-3],[2,3,-4],[3,4,-5],[3,4,-6],[4,5,-7],[4,5,-8],[5,7,-9],
      [6,8,-11],[7,9,-15],[8,11,-17],[10,13,-19],[11,15,-23],[13,18,-28]
    ],
    0x26: [  // AMPERSAND
      [6,4,0],[7,6,0],[8,7,0],[9,9,0],[11,10,0],[13,12,0],[15,13,0],[18,17,1],
      [21,20,1],[25,25,1],[29,29,1],[35,33,1],[41,39,1],[49,46,0]
    ],
    0x27: [  // APOSTROPHE
      [1,2,-2],[2,3,-3],[2,3,-4],[2,4,-5],[2,4,-6],[3,5,-7],[3,5,-8],[4,7,-9],
      [4,8,-11],[5,9,-15],[6,11,-17],[7,13,-19],[8,15,-23],[9,17,-29]
    ],
    0x28: [  // LEFT PARENTHESIS
      [3,7,2],[3,9,2],[3,11,2],[4,13,3],[5,15,3],[5,16,4],[6,18,4],[7,22,5],
      [9,26,5],[10,32,7],[12,38,8],[14,44,9],[17,51,10],[20,61,12]
    ],
    0x29: [  // RIGHT PARENTHESIS
      [2,7,2],[3,9,2],[3,10,2],[4,13,3],[4,14,3],[5,17,4],[6,18,4],[7,23,5],
      [8,27,6],[10,32,7],[11,38,8],[13,44,9],[16,52,11],[19,62,13]
    ],
    0x2A: [  // ASTERISK
      [2,2,-2],[2,2,-4],[3,3,-4],[3,3,-6],[4,4,-6],[4,4,-8],[5,5,-8],[6,6,-10],
      [7,7,-12],[8,8,-15],[10,10,-17],[11,11,-21],[13,13,-24],[16,16,-29]
    ],
    0x2B: [  // PLUS SIGN
      [5,5,1],[6,7,1],[7,9,2],[9,9,1],[10,11,2],[12,13,2],[14,13,2],[17,17,2],
      [20,20,3],[24,24,4],[28,28,3],[33,32,4],[40,38,5],[47,46,6]
    ],
    0x2C: [  // COMMA
      [2,2,1],[2,3,2],[3,3,2],[3,4,3],[3,5,3],[4,5,4],[5,6,4],[5,7,4],
      [6,8,5],[8,10,6],[9,12,8],[10,14,9],[12,17,10],[15,20,13]
    ],
    0x2D: [  // HYPHEN-MINUS
      [5,1,-1],[6,1,-2],[7,1,-2],[9,1,-3],[10,1,-3],[12,1,-4],[14,1,-4],[17,1,-5],
      [20,2,-6],[24,3,-7],[28,2,-9],[33,2,-11],[40,3,-13],[47,3,-15]
    ],
    0x2E: [  // FULL STOP
      [2,1,0],[2,1,0],[2,2,0],[3,2,0],[3,2,0],[4,2,0],[4,3,0],[5,3,1],
      [6,4,1],[7,4,1],[8,5,1],[10,6,1],[12,7,1],[14,8,1]
    ],
    0x2F: [  // SOLIDUS
      [4,6,1],[4,8,2],[5,10,3],[6,12,3],[7,14,4],[8,16,4],[10,17,4],[11,21,4],
      [13,25,5],[16,31,6],[19,37,7],[22,42,8],[26,50,10],[31,59,12]
    ],
    0x30: [  // DIGIT ZERO
      [4,3,0],[4,4,0],[5,5,0],[6,6,0],[7,7,0],[8,8,0],[9,9,0],[11,12,1],
      [13,14,1],[15,17,1],[18,21,1],[22,24,1],[26,28,1],[30,33,1]
    ],
    0x31: [  // DIGIT ONE
      [4,3,0],[4,4,0],[5,5,0],[6,6,0],[7,7,0],[8,8,0],[10,9,0],[11,12,1],
      [13,13,0],[16,16,0],[19,19,0],[22,24,1],[26,26,0],[31,31,0]
    ],
    0x32: [  // DIGIT TWO
      [4,3,0],[5,4,0],[5,5,0],[6,6,0],[7,7,0],[9,9,1],[10,10,1],[12,11,0],
      [14,13,0],[16,16,0],[19,19,0],[23,22,0],[27,26,0],[32,31,0]
    ],
    0x33: [  // DIGIT THREE
      [3,5,2],[4,6,2],[5,7,2],[6,9,3],[6,10,3],[8,11,3],[9,13,4],[10,15,4],
      [12,18,5],[15,23,7],[17,27,8],[20,31,9],[24,36,10],[29,43,12]
    ],
    0x34: [  // DIGIT FOUR
      [4,5,1],[4,7,2],[5,8,2],[6,10,3],[7,11,3],[8,13,4],[10,14,4],[12,17,5],
      [14,19,6],[16,24,7],[19,27,8],[23,32,9],[27,38,11],[32,45,13]
    ],
    0x35: [  // DIGIT FIVE
      [4,4,1],[4,6,2],[5,7,2],[6,8,2],[7,10,3],[8,11,3],[9,12,3],[11,15,4],
      [13,18,5],[15,23,7],[18,27,8],[21,31,9],[25,35,10],[29,42,12]
    ],
    0x36: [  // DIGIT SIX
      [4,4,0],[4,6,0],[5,7,0],[6,9,0],[7,10,0],[8,12,0],[10,13,0],[11,17,1],
      [14,20,1],[16,25,1],[19,29,1],[22,34,2],[27,39,1],[31,47,1]
    ],
    0x37: [  // DIGIT SEVEN
      [4,4,1],[5,6,2],[5,7,2],[6,8,2],[7,10,3],[9,11,3],[10,12,3],[12,15,4],
      [14,18,5],[17,22,6],[20,26,7],[24,30,8],[28,36,10],[33,43,12]
    ],
    0x38: [  // DIGIT EIGHT
      [4,4,0],[4,7,0],[5,8,0],[6,10,0],[7,11,0],[8,13,0],[9,14,0],[11,18,1],
      [13,21,1],[16,25,0],[19,30,1],[22,35,2],[26,40,1],[31,48,1]
    ],
    0x39: [  // DIGIT NINE
      [4,5,2],[4,6,2],[5,7,2],[6,9,3],[7,10,3],[8,11,3],[10,13,4],[11,15,4],
      [13,18,5],[16,22,6],[19,27,8],[22,31,9],[26,36,10],[31,43,12]
    ],
    0x3A: [  // COLON
      [2,3,0],[2,4,0],[2,5,0],[2,6,0],[3,7,0],[3,8,0],[4,9,0],[4,12,1],
      [5,14,1],[6,16,1],[7,19,0],[8,22,1],[10,26,1],[12,31,1]
    ],
    0x3B: [  // SEMICOLON
      [2,5,2],[2,6,2],[2,7,2],[3,9,3],[3,10,3],[3,11,3],[4,13,4],[5,15,4],
      [5,18,5],[6,23,7],[7,27,8],[9,31,9],[10,37,11],[12,43,13]
    ],
    0x3D: [  // EQUALS SIGN
      [5,2,0],[7,3,-1],[8,3,-1],[9,4,-1],[11,4,-2],[13,4,-2],[15,5,-2],[17,6,-3],
      [21,7,-3],[24,10,-4],[29,10,-5],[34,11,-6],[41,13,-7],[48,16,-9]
    ],
    0x3F: [  // QUESTION MARK
      [3,4,0],[3,6,0],[4,7,0],[5,9,0],[5,10,0],[6,12,0],[7,13,0],[9,17,1],
      [10,20,1],[12,24,0],[14,28,0],[17,33,1],[20,39,1],[24,46,0]
    ],
    0x41: [  // LATIN CAPITAL LETTER A
      [5,5,1],[6,6,0],[7,7,0],[9,9,0],[10,10,0],[12,12,0],[14,14,1],[17,17,1],
      [20,20,1],[24,25,1],[28,29,1],[33,33,1],[40,39,1],[47,48,2]
    ],
    0x42: [  // LATIN CAPITAL LETTER B
      [6,4,0],[7,6,0],[9,7,0],[10,9,0],[12,10,0],[14,12,0],[16,13,0],[19,17,1],
      [23,20,1],[27,25,1],[32,29,1],[38,33,1],[46,39,1],[54,48,2]
    ],
    0x43: [  // LATIN CAPITAL LETTER C
      [5,4,0],[6,6,0],[6,7,0],[8,9,0],[9,10,0],[11,12,0],[12,13,0],[15,17,1],
      [17,20,1],[20,24,1],[24,28,1],[29,33,1],[34,38,1],[40,47,2]
    ],
    0x44: [  // LATIN CAPITAL LETTER D
      [6,4,0],[7,6,0],[8,7,0],[9,9,0],[11,10,0],[13,12,0],[15,14,1],[18,17,1],
      [21,20,1],[25,24,1],[30,28,1],[35,33,1],[42,39,2],[50,47,2]
    ],
    0x45: [  // LATIN CAPITAL LETTER E
      [5,4,0],[6,6,0],[7,7,0],[8,9,0],[9,10,0],[11,12,0],[13,13,0],[15,17,1],
      [18,20,1],[21,24,1],[25,28,1],[30,33,1],[36,38,1],[42,47,2]
    ],
    0x46: [  // LATIN CAPITAL LETTER F
      [5,6,2],[6,7,1],[6,9,2],[8,11,2],[9,13,3],[11,14,2],[12,16,3],[15,20,4],
      [17,23,4],[20,29,6],[24,34,7],[29,39,7],[34,46,9],[40,55,10]
    ],
    0x47: [  // LATIN CAPITAL LETTER G
      [5,4,0],[6,6,0],[7,7,0],[9,9,0],[10,10,0],[12,12,0],[14,13,0],[17,17,1],
      [20,20,1],[24,24,1],[28,28,1],[33,33,1],[40,38,1],[47,47,2]
    ],
    0x48: [  // LATIN CAPITAL LETTER H
      [5,5,1],[6,8,2],[7,9,2],[8,11,2],[9,12,2],[11,15,3],[13,16,3],[15,19,3],
      [18,23,4],[22,28,5],[26,33,6],[30,38,6],[36,45,7],[43,53,9]
    ],
    0x49: [  // LATIN CAPITAL LETTER I
      [4,4,0],[5,6,0],[6,7,0],[7,9,0],[8,10,0],[9,12,0],[11,13,0],[13,17,1],
      [15,20,1],[18,24,1],[21,28,1],[25,33,1],[30,38,1],[35,47,2]
    ],
    0x4A: [  // LATIN CAPITAL LETTER J
      [5,6,2],[6,7,1],[7,9,2],[8,11,2],[9,12,2],[10,14,2],[12,16,3],[14,19,3],
      [16,23,4],[19,28,5],[22,33,6],[26,38,6],[30,46,9],[36,54,9]
    ],
    0x4B: [  // LATIN CAPITAL LETTER K
      [5,4,0],[6,6,0],[7,7,0],[9,9,0],[10,10,0],[12,12,0],[14,13,0],[16,17,1],
      [19,20,1],[23,24,1],[27,28,1],[32,33,1],[38,38,1],[45,47,2]
    ],
    0x4C: [  // LATIN CAPITAL LETTER L
      [5,4,0],[6,6,0],[7,7,0],[8,9,0],[9,10,0],[11,12,0],[13,13,0],[15,17,1],
      [18,20,1],[22,24,1],[26,28,1],[30,33,1],[36,38,1],[43,47,2]
    ],
    0x4D: [  // LATIN CAPITAL LETTER M
      [8,4,0],[9,6,0],[11,7,0],[13,9,0],[15,10,0],[18,12,0],[21,14,1],[25,17,1],
      [29,20,1],[35,24,1],[42,28,1],[49,33,1],[58,39,2],[70,47,2]
    ],
    0x4E: [  // LATIN CAPITAL LETTER N
      [6,5,1],[7,7,1],[9,8,1],[10,10,1],[12,11,1],[14,13,1],[17,14,1],[20,18,2],
      [23,21,2],[28,25,2],[33,29,2],[39,34,2],[46,39,2],[55,48,3]
    ],
    0x4F: [  // LATIN CAPITAL LETTER O
      [6,5,0],[7,6,0],[8,9,0],[9,10,0],[11,11,0],[13,13,0],[15,15,0],[18,18,1],
      [21,21,1],[25,26,1],[29,31,1],[35,35,1],[41,42,1],[49,50,2]
    ],
    0x50: [  // LATIN CAPITAL LETTER P
      [6,5,1],[7,8,2],[8,9,2],[10,12,3],[12,13,3],[14,16,4],[16,17,4],[19,21,5],
      [23,25,6],[27,31,7],[32,37,9],[38,42,10],[45,50,12],[53,60,14]
    ],
    0x51: [  // LATIN CAPITAL LETTER Q
      [6,6,1],[7,7,1],[8,10,1],[10,11,1],[11,13,1],[13,14,1],[16,16,1],[19,19,2],
      [22,22,2],[26,28,3],[31,33,3],[37,37,3],[44,45,4],[52,52,5]
    ],
    0x52: [  // LATIN CAPITAL LETTER R
      [6,4,0],[7,6,0],[9,7,0],[10,9,0],[12,10,0],[14,12,0],[17,13,0],[20,17,1],
      [23,20,1],[28,24,1],[33,28,1],[39,33,1],[46,38,1],[55,47,2]
    ],
    0x53: [  // LATIN CAPITAL LETTER S
      [6,4,0],[7,6,0],[8,7,0],[9,9,0],[11,10,0],[13,12,0],[15,13,0],[18,17,1],
      [21,20,1],[25,25,1],[30,29,1],[35,33,1],[42,39,1],[50,48,2]
    ],
    0x54: [  // LATIN CAPITAL LETTER T
      [5,5,1],[6,7,1],[7,8,1],[8,9,0],[10,11,1],[12,13,1],[14,14,1],[16,17,1],
      [19,20,1],[23,25,1],[27,30,2],[32,33,1],[38,40,2],[45,48,2]
    ],
    0x55: [  // LATIN CAPITAL LETTER U
      [6,5,0],[7,7,0],[8,8,0],[9,10,0],[11,11,0],[13,13,0],[14,14,0],[17,17,1],
      [20,20,1],[23,25,1],[28,29,1],[33,34,1],[39,40,1],[46,47,2]
    ],
    0x56: [  // LATIN CAPITAL LETTER V
      [6,4,0],[7,6,0],[9,7,0],[10,9,0],[12,10,0],[14,12,0],[16,13,0],[20,17,1],
      [23,20,1],[27,24,1],[33,28,1],[39,33,1],[46,38,1],[54,47,2]
    ],
    0x57: [  // LATIN CAPITAL LETTER W
      [8,4,0],[9,6,0],[11,7,0],[13,9,0],[15,10,0],[18,12,0],[21,13,0],[25,17,1],
      [30,20,1],[35,24,1],[42,28,1],[49,33,1],[59,38,1],[70,47,2]
    ],
    0x58: [  // LATIN CAPITAL LETTER X
      [5,4,0],[6,6,0],[7,7,0],[9,9,0],[10,10,0],[12,12,0],[14,13,0],[17,17,1],
      [20,20,1],[24,24,1],[28,28,1],[33,33,1],[40,38,1],[47,47,2]
    ],
    0x59: [  // LATIN CAPITAL LETTER Y
      [6,6,1],[7,9,2],[8,10,2],[9,12,3],[11,14,3],[13,17,4],[15,18,4],[18,22,5],
      [21,26,6],[25,31,7],[29,37,9],[35,43,10],[41,50,12],[49,60,14]
    ],
    0x5A: [  // LATIN CAPITAL LETTER Z
      [4,6,1],[5,8,1],[6,10,1],[7,12,2],[8,14,2],[9,15,2],[11,17,3],[13,20,3],
      [15,24,4],[18,30,5],[21,36,7],[25,41,7],[30,48,8],[35,58,9]
    ],
    0x5B: [  // LEFT SQUARE BRACKET
      [2,6,1],[3,8,1],[3,10,1],[4,12,2],[4,13,2],[5,15,2],[6,17,2],[7,21,3],
      [8,24,4],[10,30,4],[11,35,5],[13,41,6],[16,48,7],[19,57,9]
    ],
    0x5D: [  // RIGHT SQUARE BRACKET
      [3,6,1],[3,8,1],[3,10,1],[3,12,2],[4,13,2],[4,15,2],[5,17,2],[5,21,3],
      [6,24,4],[7,30,4],[8,36,5],[9,41,6],[10,48,7],[12,57,9]
    ],
    0x5E: [  // CIRCUMFLEX ACCENT
      [4,3,-2],[5,3,-4],[5,4,-4],[6,4,-6],[7,5,-6],[9,6,-7],[10,7,-8],[12,8,-10],
      [14,9,-11],[17,11,-15],[20,12,-18],[23,14,-20],[28,17,-24],[33,20,-29]
    ],
    0x61: [  // LATIN SMALL LETTER A
      [4,3,0],[5,4,0],[5,5,0],[6,6,0],[7,7,0],[9,8,0],[10,10,1],[12,12,1],
      [14,14,1],[17,17,1],[20,20,1],[24,23,1],[28,28,2],[33,33,2]
    ],
    0x62: [  // LATIN SMALL LETTER B
      [4,5,0],[4,6,0],[5,8,0],[6,9,0],[7,11,0],[8,12,0],[9,15,1],[11,18,1],
      [13,20,1],[15,25,1],[18,29,1],[21,34,1],[25,40,2],[30,48,2]
    ],
    0x63: [  // LATIN SMALL LETTER C
      [3,4,1],[3,5,1],[4,6,1],[5,7,1],[5,8,1],[6,9,1],[8,10,1],[9,12,1],
      [10,14,1],[12,17,1],[15,21,2],[17,24,2],[20,28,2],[24,34,3]
    ],
    0x64: [  // LATIN SMALL LETTER D
      [3,4,0],[4,6,0],[5,7,0],[6,8,0],[6,10,0],[8,11,0],[9,12,1],[10,15,1],
      [12,18,1],[15,22,1],[17,26,1],[20,30,1],[24,36,2],[29,42,2]
    ],
    0x65: [  // LATIN SMALL LETTER E
      [3,3,0],[4,5,0],[4,6,0],[5,7,0],[6,8,0],[7,9,0],[8,10,1],[9,12,1],
      [11,14,1],[12,17,1],[15,20,1],[17,24,1],[21,28,2],[24,33,2]
    ],
    0x66: [  // LATIN SMALL LETTER F
      [3,6,1],[3,8,2],[4,10,2],[4,12,3],[5,14,3],[6,16,4],[7,18,4],[8,21,5],
      [9,25,6],[11,31,7],[13,37,9],[15,43,10],[18,50,12],[22,60,14]
    ],
    0x67: [  // LATIN SMALL LETTER G
      [4,5,1],[4,7,2],[5,8,2],[6,10,3],[7,11,3],[8,13,4],[9,14,4],[11,17,5],
      [13,20,6],[15,24,7],[18,29,9],[22,33,10],[26,39,12],[30,46,14]
    ],
    0x68: [  // LATIN SMALL LETTER H
      [3,6,2],[4,8,2],[5,9,2],[6,12,3],[6,13,3],[8,15,3],[9,17,4],[11,22,6],
      [12,26,7],[15,31,8],[17,36,9],[21,42,10],[24,48,11],[29,60,15]
    ],
    0x69: [  // LATIN SMALL LETTER I
      [2,4,0],[3,6,0],[3,7,0],[4,9,0],[4,10,0],[5,12,0],[6,13,0],[7,17,1],
      [8,20,1],[9,24,1],[11,28,1],[13,33,1],[15,38,1],[18,46,2]
    ],
    0x6A: [  // LATIN SMALL LETTER J
      [3,6,2],[3,8,2],[3,10,3],[4,12,3],[4,14,3],[5,15,4],[5,18,5],[6,21,5],
      [7,25,7],[8,31,7],[9,36,9],[11,42,10],[12,50,12],[14,59,14]
    ],
    0x6B: [  // LATIN SMALL LETTER K
      [3,5,0],[3,7,0],[4,8,0],[5,10,0],[5,11,0],[6,13,0],[8,14,0],[9,18,1],
      [10,21,1],[12,25,1],[15,29,1],[17,34,1],[21,40,1],[24,47,2]
    ],
    0x6C: [  // LATIN SMALL LETTER L
      [2,5,1],[3,6,0],[3,8,1],[4,9,0],[4,11,1],[5,13,1],[6,14,1],[7,17,1],
      [8,20,1],[10,24,1],[11,29,2],[13,33,1],[16,39,2],[19,47,2]
    ],
    0x6D: [  // LATIN SMALL LETTER M
      [6,5,1],[7,6,1],[8,7,1],[9,8,1],[11,9,1],[13,10,1],[15,11,1],[18,13,1],
      [21,15,1],[25,18,1],[30,22,2],[35,24,1],[42,29,2],[50,34,2]
    ],
    0x6E: [  // LATIN SMALL LETTER N
      [4,4,0],[5,5,0],[6,6,0],[7,7,0],[8,8,0],[9,9,0],[11,10,0],[12,12,1],
      [15,14,1],[17,17,1],[21,20,1],[24,23,1],[29,28,1],[34,33,2]
    ],
    0x6F: [  // LATIN SMALL LETTER O
      [3,4,1],[4,5,1],[5,6,1],[5,7,1],[6,8,1],[7,9,1],[9,10,1],[10,12,1],
      [12,14,1],[14,17,1],[17,21,2],[20,24,2],[23,28,2],[28,34,3]
    ],
    0x70: [  // LATIN SMALL LETTER P
      [3,6,2],[4,7,2],[5,9,3],[6,10,3],[6,12,4],[8,14,4],[9,15,4],[10,18,5],
      [12,21,6],[15,26,7],[17,31,9],[20,35,10],[24,42,12],[29,50,14]
    ],
    0x71: [  // LATIN SMALL LETTER Q
      [3,5,2],[4,6,2],[5,8,3],[5,9,3],[6,11,4],[7,12,4],[9,14,5],[10,16,5],
      [12,20,7],[14,24,8],[17,28,9],[20,32,10],[24,38,12],[28,46,15]
    ],
    0x72: [  // LATIN SMALL LETTER R
      [3,5,1],[4,6,1],[4,7,1],[5,8,1],[6,9,1],[7,10,1],[8,11,1],[9,13,1],
      [11,15,1],[13,18,1],[16,21,1],[18,24,1],[22,28,1],[26,33,1]
    ],
    0x73: [  // LATIN SMALL LETTER S
      [4,4,0],[5,4,0],[5,6,0],[6,7,0],[7,8,0],[8,9,0],[9,11,1],[11,13,1],
      [13,15,1],[15,18,1],[17,21,1],[20,24,1],[24,29,2],[29,34,2]
    ],
    0x74: [  // LATIN SMALL LETTER T
      [3,5,0],[3,6,0],[4,8,0],[5,8,0],[5,10,0],[6,12,0],[7,13,0],[9,16,1],
      [10,18,1],[12,23,1],[14,27,1],[17,31,1],[20,36,1],[23,44,2]
    ],
    0x75: [  // LATIN SMALL LETTER U
      [4,5,1],[5,4,0],[6,6,0],[7,8,1],[8,9,1],[9,10,1],[10,11,1],[12,13,2],
      [15,15,2],[17,18,2],[21,22,2],[24,24,2],[29,29,2],[34,34,3]
    ],
    0x76: [  // LATIN SMALL LETTER V
      [3,5,1],[4,6,1],[5,7,0],[6,8,0],[6,10,1],[8,11,1],[9,12,1],[11,14,1],
      [12,17,2],[15,20,1],[17,24,2],[21,27,2],[24,32,2],[29,37,2]
    ],
    0x77: [  // LATIN SMALL LETTER W
      [5,4,1],[6,5,1],[7,6,1],[9,7,0],[10,9,1],[12,11,1],[14,12,1],[16,14,1],
      [20,16,1],[23,20,2],[27,23,1],[32,27,2],[39,31,2],[46,37,2]
    ],
    0x78: [  // LATIN SMALL LETTER X
      [3,6,2],[4,7,2],[4,8,2],[5,10,3],[6,11,3],[7,12,3],[8,14,4],[9,17,5],
      [11,20,6],[13,24,7],[15,28,8],[17,32,9],[21,38,11],[25,44,13]
    ],
    0x79: [  // LATIN SMALL LETTER Y
      [3,5,1],[4,7,2],[5,9,2],[6,10,3],[6,12,3],[8,13,4],[9,15,4],[10,18,5],
      [12,21,6],[15,26,7],[17,30,9],[21,35,10],[24,41,12],[29,49,14]
    ],
    0x7A: [  // LATIN SMALL LETTER Z
      [4,5,1],[4,6,2],[5,8,2],[5,9,3],[6,11,3],[7,12,4],[8,14,4],[9,16,5],
      [10,19,6],[12,24,7],[14,28,9],[16,32,10],[19,38,12],[22,45,14]
    ]
  }
});

MathJax.Ajax.loadComplete(MathJax.OutputJax["HTML-CSS"].imgDir+"/Fraktur/Regular"+
                          MathJax.OutputJax["HTML-CSS"].imgPacked+"/BasicLatin.js");
