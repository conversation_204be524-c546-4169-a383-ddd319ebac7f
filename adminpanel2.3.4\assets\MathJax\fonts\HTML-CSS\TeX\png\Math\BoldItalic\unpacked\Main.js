/*************************************************************
 *
 *  MathJax/fonts/HTML-CSS/TeX/png/Math/BoldItalic/Main.js
 *  
 *  Defines the image size data needed for the HTML-CSS OutputJax
 *  to display mathematics using fallback images when the fonts
 *  are not available to the client browser.
 *
 *  ---------------------------------------------------------------------
 *
 *  Copyright (c) 2009-2013 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 *
 */

MathJax.OutputJax["HTML-CSS"].defineImageData({
  "MathJax_Math-bold-italic": {
    0x20: [  // SPACE
      [1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],
      [1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0]
    ],
    0x2F: [  // SOLIDUS
      [6,6,1],[7,8,2],[8,10,2],[9,10,2],[11,12,3],[13,16,4],[15,18,4],[18,22,5],
      [21,26,6],[25,31,7],[29,37,8],[35,43,9],[41,51,11],[49,62,14]
    ],
    0x41: [  // LATIN CAPITAL LETTER A
      [6,5,0],[7,6,0],[9,8,0],[10,8,0],[12,9,0],[14,12,0],[17,14,0],[20,17,0],
      [24,20,0],[28,23,0],[33,29,0],[40,33,0],[47,40,0],[56,47,0]
    ],
    0x42: [  // LATIN CAPITAL LETTER B
      [6,5,0],[8,6,0],[9,8,0],[11,8,0],[12,9,0],[15,12,0],[17,14,0],[20,17,0],
      [24,20,0],[29,23,0],[34,28,0],[40,32,0],[48,38,0],[57,46,0]
    ],
    0x43: [  // LATIN CAPITAL LETTER C
      [6,5,0],[8,6,0],[9,8,0],[11,8,0],[12,9,0],[15,12,0],[17,14,0],[20,17,0],
      [24,20,0],[29,24,1],[34,29,1],[40,33,1],[48,40,0],[57,49,2]
    ],
    0x44: [  // LATIN CAPITAL LETTER D
      [7,5,0],[8,6,0],[9,8,0],[11,8,0],[13,9,0],[16,12,0],[18,14,0],[22,17,0],
      [26,20,0],[31,23,0],[36,28,0],[43,32,0],[51,38,0],[61,46,0]
    ],
    0x45: [  // LATIN CAPITAL LETTER E
      [6,5,0],[7,6,0],[9,8,0],[10,8,0],[12,9,0],[14,12,0],[17,14,0],[20,17,0],
      [23,20,0],[28,23,0],[33,28,0],[39,32,0],[46,38,0],[55,46,0]
    ],
    0x46: [  // LATIN CAPITAL LETTER F
      [6,5,0],[7,6,0],[8,8,0],[10,8,0],[12,9,0],[14,12,0],[16,14,0],[19,17,0],
      [23,20,0],[27,23,0],[32,28,0],[38,32,0],[45,38,0],[54,46,0]
    ],
    0x47: [  // LATIN CAPITAL LETTER G
      [6,5,0],[8,6,0],[9,8,0],[11,8,0],[12,9,0],[15,12,0],[17,14,0],[20,17,0],
      [24,20,0],[29,24,1],[34,29,1],[40,33,1],[48,40,0],[57,48,1]
    ],
    0x48: [  // LATIN CAPITAL LETTER H
      [8,5,0],[9,6,0],[11,8,0],[13,8,0],[15,9,0],[18,12,0],[21,14,0],[24,17,0],
      [29,20,0],[34,23,0],[41,28,0],[48,32,0],[58,38,0],[68,46,0]
    ],
    0x49: [  // LATIN CAPITAL LETTER I
      [4,5,0],[5,6,0],[6,8,0],[7,8,0],[8,9,0],[10,12,0],[12,14,0],[14,17,0],
      [16,20,0],[19,23,0],[23,28,0],[27,32,0],[32,38,0],[38,46,0]
    ],
    0x4A: [  // LATIN CAPITAL LETTER J
      [5,5,0],[6,6,0],[7,8,0],[9,8,0],[10,9,0],[12,12,0],[14,14,0],[17,17,0],
      [20,20,0],[23,24,1],[28,29,1],[33,33,1],[39,39,1],[46,47,1]
    ],
    0x4B: [  // LATIN CAPITAL LETTER K
      [7,5,0],[9,6,0],[10,8,0],[12,8,0],[14,9,0],[17,12,0],[20,14,0],[24,17,0],
      [28,20,0],[34,23,0],[40,28,0],[47,32,0],[56,38,0],[67,46,0]
    ],
    0x4C: [  // LATIN CAPITAL LETTER L
      [5,5,0],[6,6,0],[7,8,0],[9,8,0],[10,9,0],[12,12,0],[14,14,0],[17,17,0],
      [20,20,0],[24,23,0],[28,28,0],[34,32,0],[40,38,0],[47,46,0]
    ],
    0x4D: [  // LATIN CAPITAL LETTER M
      [9,6,0],[11,7,0],[12,9,0],[15,9,0],[17,10,0],[21,13,0],[24,15,0],[29,18,0],
      [34,21,0],[41,24,0],[48,29,0],[57,33,0],[68,39,0],[81,47,0]
    ],
    0x4E: [  // LATIN CAPITAL LETTER N
      [8,5,0],[9,6,0],[11,8,0],[13,8,0],[15,9,0],[18,12,0],[20,14,0],[24,17,0],
      [29,20,0],[34,23,0],[41,28,0],[48,32,0],[57,38,0],[68,46,0]
    ],
    0x4F: [  // LATIN CAPITAL LETTER O
      [6,5,0],[7,6,0],[8,8,0],[10,8,0],[12,9,0],[14,12,0],[16,14,0],[19,17,0],
      [23,20,0],[27,24,1],[32,29,1],[38,33,1],[46,40,1],[54,48,2]
    ],
    0x50: [  // LATIN CAPITAL LETTER P
      [6,5,0],[8,7,0],[9,9,0],[10,9,0],[12,10,0],[15,13,0],[17,15,0],[20,18,0],
      [24,21,0],[28,24,0],[34,29,0],[40,33,0],[48,39,0],[56,47,0]
    ],
    0x51: [  // LATIN CAPITAL LETTER Q
      [6,6,1],[7,8,2],[8,10,2],[10,10,2],[12,12,3],[14,16,4],[16,18,4],[19,22,5],
      [23,26,6],[27,30,7],[32,36,8],[38,41,9],[46,50,11],[54,60,14]
    ],
    0x52: [  // LATIN CAPITAL LETTER R
      [7,5,0],[8,6,0],[9,8,0],[11,8,0],[13,9,0],[15,12,0],[18,14,0],[21,17,0],
      [25,20,0],[30,24,1],[35,29,1],[42,33,1],[49,39,0],[59,47,1]
    ],
    0x53: [  // LATIN CAPITAL LETTER S
      [5,5,0],[6,6,0],[7,8,0],[9,8,0],[10,9,0],[12,12,0],[14,14,0],[17,17,0],
      [20,20,0],[24,24,1],[28,29,1],[34,33,1],[40,41,2],[47,48,2]
    ],
    0x54: [  // LATIN CAPITAL LETTER T
      [6,5,0],[7,6,0],[8,8,0],[9,8,0],[11,9,0],[13,12,0],[15,14,0],[18,17,0],
      [22,19,0],[26,22,0],[30,27,0],[36,31,0],[43,38,0],[51,45,0]
    ],
    0x55: [  // LATIN CAPITAL LETTER U
      [7,5,0],[8,6,0],[9,8,0],[11,8,0],[13,9,0],[15,12,0],[18,14,0],[21,17,0],
      [25,20,0],[29,24,1],[35,29,1],[41,33,1],[49,39,0],[58,47,1]
    ],
    0x56: [  // LATIN CAPITAL LETTER V
      [7,5,0],[8,6,0],[9,8,0],[11,8,0],[13,9,0],[15,12,0],[18,14,0],[21,17,0],
      [25,20,0],[29,24,1],[35,29,1],[41,33,1],[49,39,0],[58,47,1]
    ],
    0x57: [  // LATIN CAPITAL LETTER W
      [9,5,0],[10,6,0],[12,8,0],[15,8,0],[17,9,0],[20,12,0],[24,14,0],[28,17,0],
      [34,20,0],[40,24,1],[48,29,1],[56,33,1],[67,39,0],[80,48,1]
    ],
    0x58: [  // LATIN CAPITAL LETTER X
      [7,5,0],[8,6,0],[10,8,0],[12,8,0],[14,9,0],[16,12,0],[19,14,0],[23,17,0],
      [27,20,0],[32,23,0],[38,28,0],[45,32,0],[53,39,1],[63,46,0]
    ],
    0x59: [  // LATIN CAPITAL LETTER Y
      [7,5,0],[8,6,0],[9,8,0],[11,8,0],[13,9,0],[15,12,0],[18,14,0],[21,17,0],
      [25,20,0],[29,23,0],[35,28,0],[41,32,0],[49,38,0],[58,45,0]
    ],
    0x5A: [  // LATIN CAPITAL LETTER Z
      [6,5,0],[7,6,0],[8,8,0],[10,8,0],[12,9,0],[14,12,0],[16,14,0],[19,17,0],
      [23,20,0],[27,23,0],[32,28,0],[38,32,0],[45,38,0],[54,46,0]
    ],
    0x61: [  // LATIN SMALL LETTER A
      [5,3,0],[6,4,0],[6,5,0],[8,5,0],[9,6,0],[11,8,0],[12,9,0],[15,11,0],
      [17,13,0],[20,15,0],[24,18,0],[29,21,0],[34,25,0],[40,31,1]
    ],
    0x62: [  // LATIN SMALL LETTER B
      [4,5,0],[5,6,0],[6,8,0],[7,8,0],[8,9,0],[9,12,0],[10,14,0],[12,17,0],
      [15,20,0],[17,23,0],[21,28,0],[24,32,0],[29,38,0],[34,47,1]
    ],
    0x63: [  // LATIN SMALL LETTER C
      [4,3,0],[5,4,0],[5,5,0],[6,5,0],[8,6,0],[9,8,0],[10,9,0],[12,11,0],
      [15,13,0],[17,15,0],[20,18,0],[24,21,0],[29,25,0],[34,31,1]
    ],
    0x64: [  // LATIN SMALL LETTER D
      [5,5,0],[6,6,0],[7,8,0],[8,8,0],[9,9,0],[11,12,0],[12,14,0],[15,17,0],
      [17,20,0],[21,23,0],[25,28,0],[29,32,0],[34,38,0],[41,47,1]
    ],
    0x65: [  // LATIN SMALL LETTER E
      [4,3,0],[5,4,0],[5,5,0],[6,5,0],[8,6,0],[9,8,0],[10,9,0],[12,11,0],
      [15,13,0],[17,15,0],[20,18,0],[24,21,0],[29,25,0],[34,31,1]
    ],
    0x66: [  // LATIN SMALL LETTER F
      [5,6,1],[6,8,2],[7,10,2],[8,10,2],[9,12,3],[11,16,4],[13,18,4],[15,22,5],
      [18,26,6],[21,30,7],[25,36,8],[30,41,9],[35,50,11],[42,59,13]
    ],
    0x67: [  // LATIN SMALL LETTER G
      [4,4,1],[5,6,2],[6,7,2],[7,7,2],[8,9,3],[9,12,4],[11,13,4],[13,16,5],
      [15,19,6],[18,22,7],[22,26,8],[26,30,9],[30,36,11],[36,43,13]
    ],
    0x68: [  // LATIN SMALL LETTER H
      [5,5,0],[6,6,0],[7,8,0],[8,8,0],[9,9,0],[11,12,0],[13,14,0],[15,17,0],
      [18,20,0],[21,23,0],[25,28,0],[30,33,1],[36,39,0],[42,47,1]
    ],
    0x69: [  // LATIN SMALL LETTER I
      [3,5,0],[4,6,0],[4,8,0],[5,8,0],[6,9,0],[7,12,0],[8,14,0],[9,17,0],
      [11,20,0],[12,23,0],[15,28,0],[17,32,0],[21,38,0],[24,47,1]
    ],
    0x6A: [  // LATIN SMALL LETTER J
      [5,6,1],[5,8,2],[6,10,2],[7,10,2],[8,12,3],[9,16,4],[10,18,4],[12,22,5],
      [14,26,6],[16,30,7],[19,36,8],[23,41,9],[27,49,11],[31,59,13]
    ],
    0x6B: [  // LATIN SMALL LETTER K
      [4,5,0],[5,6,0],[6,8,0],[7,8,0],[8,9,0],[10,12,0],[12,14,0],[14,17,0],
      [16,20,0],[19,23,0],[23,28,0],[27,32,0],[32,38,0],[38,47,1]
    ],
    0x6C: [  // LATIN SMALL LETTER L
      [3,5,0],[3,6,0],[3,8,0],[4,8,0],[5,9,0],[5,12,0],[6,14,0],[7,17,0],
      [9,20,0],[10,23,0],[12,28,0],[14,32,0],[17,38,0],[20,47,1]
    ],
    0x6D: [  // LATIN SMALL LETTER M
      [7,3,0],[9,4,0],[10,5,0],[12,5,0],[14,6,0],[17,8,0],[20,9,0],[24,11,0],
      [28,13,0],[33,15,0],[40,18,0],[47,21,0],[56,25,0],[66,31,1]
    ],
    0x6E: [  // LATIN SMALL LETTER N
      [5,3,0],[6,4,0],[7,5,0],[9,5,0],[10,6,0],[12,8,0],[14,9,0],[16,11,0],
      [19,13,0],[23,15,0],[27,18,0],[32,21,0],[38,25,0],[45,31,1]
    ],
    0x6F: [  // LATIN SMALL LETTER O
      [4,3,0],[5,4,0],[6,5,0],[7,5,0],[8,6,0],[10,8,0],[12,9,0],[14,11,0],
      [16,13,0],[19,15,0],[23,18,0],[27,21,0],[32,25,0],[38,31,1]
    ],
    0x70: [  // LATIN SMALL LETTER P
      [6,4,1],[6,6,2],[7,7,2],[8,7,2],[10,9,3],[11,12,4],[13,13,4],[15,16,5],
      [18,19,6],[21,22,7],[25,26,8],[30,30,9],[35,36,11],[42,44,14]
    ],
    0x71: [  // LATIN SMALL LETTER Q
      [4,4,1],[5,6,2],[6,7,2],[7,7,2],[8,9,3],[10,12,4],[11,13,4],[13,16,5],
      [16,19,6],[19,22,7],[22,26,8],[26,30,9],[31,36,11],[37,44,14]
    ],
    0x72: [  // LATIN SMALL LETTER R
      [4,3,0],[5,4,0],[5,5,0],[6,5,0],[7,6,0],[9,8,0],[10,9,0],[12,11,0],
      [14,13,0],[17,15,0],[20,18,0],[24,21,0],[28,25,0],[33,31,1]
    ],
    0x73: [  // LATIN SMALL LETTER S
      [4,3,0],[4,4,0],[5,5,0],[6,5,0],[7,6,0],[8,8,0],[10,9,0],[12,11,0],
      [14,13,0],[16,15,0],[19,18,0],[23,21,0],[27,25,0],[32,31,1]
    ],
    0x74: [  // LATIN SMALL LETTER T
      [3,5,0],[4,6,0],[4,7,0],[5,7,0],[6,9,0],[7,12,0],[8,13,0],[9,16,0],
      [11,19,0],[13,22,0],[16,26,0],[18,30,0],[22,36,0],[26,43,1]
    ],
    0x75: [  // LATIN SMALL LETTER U
      [5,3,0],[6,4,0],[7,5,0],[8,5,0],[9,6,0],[11,8,0],[13,9,0],[16,11,0],
      [18,13,0],[22,15,0],[26,18,0],[31,21,0],[36,25,0],[43,31,1]
    ],
    0x76: [  // LATIN SMALL LETTER V
      [4,3,0],[5,4,0],[6,5,0],[7,5,0],[8,6,0],[9,8,0],[11,9,0],[13,11,0],
      [15,13,0],[18,15,0],[22,18,0],[25,21,0],[30,25,0],[36,31,1]
    ],
    0x77: [  // LATIN SMALL LETTER W
      [6,3,0],[7,4,0],[8,5,0],[10,5,0],[11,6,0],[14,8,0],[16,9,0],[19,11,0],
      [22,13,0],[27,15,0],[32,18,0],[37,21,0],[44,25,0],[53,31,1]
    ],
    0x78: [  // LATIN SMALL LETTER X
      [5,3,0],[5,4,0],[6,5,0],[8,5,0],[9,6,0],[10,8,0],[12,9,0],[14,11,0],
      [17,13,0],[20,15,0],[24,18,0],[28,21,0],[34,25,0],[40,31,1]
    ],
    0x79: [  // LATIN SMALL LETTER Y
      [5,4,1],[5,6,2],[6,7,2],[7,7,2],[9,9,3],[10,12,4],[12,13,4],[14,16,5],
      [17,19,6],[20,22,7],[23,26,8],[28,30,9],[33,36,11],[39,43,13]
    ],
    0x7A: [  // LATIN SMALL LETTER Z
      [4,3,0],[5,4,0],[6,5,0],[7,5,0],[8,6,0],[9,8,0],[11,9,0],[13,11,0],
      [15,13,0],[18,15,0],[22,18,0],[25,21,0],[30,25,0],[36,31,1]
    ],
    0xA0: [  // NO-BREAK SPACE
      [1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],
      [1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0]
    ],
    0x393: [  // GREEK CAPITAL LETTER GAMMA
      [6,5,0],[7,6,0],[8,8,0],[10,8,0],[11,9,0],[13,12,0],[16,14,0],[19,17,0],
      [22,20,0],[26,23,0],[31,28,0],[37,32,0],[43,38,0],[52,46,0]
    ],
    0x394: [  // GREEK CAPITAL LETTER DELTA
      [7,5,0],[8,7,1],[9,9,1],[11,9,1],[13,10,1],[15,13,1],[18,15,1],[21,18,1],
      [25,21,1],[30,24,1],[36,30,1],[42,34,1],[50,41,1],[60,48,1]
    ],
    0x398: [  // GREEK CAPITAL LETTER THETA
      [6,5,0],[7,6,0],[9,8,0],[10,8,0],[12,9,0],[14,12,0],[17,14,0],[20,17,0],
      [24,20,0],[28,24,1],[34,29,1],[40,33,1],[47,39,1],[56,47,1]
    ],
    0x39B: [  // GREEK CAPITAL LETTER LAMDA
      [6,5,0],[7,6,0],[8,8,0],[10,8,0],[11,9,0],[13,12,0],[16,14,0],[18,17,0],
      [22,20,0],[26,24,0],[31,29,0],[36,33,0],[43,40,0],[51,47,0]
    ],
    0x39E: [  // GREEK CAPITAL LETTER XI
      [6,5,0],[8,6,0],[9,8,0],[11,8,0],[12,9,0],[15,12,0],[17,14,0],[20,17,0],
      [24,20,0],[29,23,0],[34,27,0],[40,31,0],[48,38,0],[57,45,0]
    ],
    0x3A0: [  // GREEK CAPITAL LETTER PI
      [8,5,0],[9,6,0],[11,8,0],[13,8,0],[15,9,0],[18,12,0],[21,14,0],[24,17,0],
      [29,20,0],[34,23,0],[41,27,0],[48,32,0],[57,38,0],[68,46,0]
    ],
    0x3A3: [  // GREEK CAPITAL LETTER SIGMA
      [7,5,0],[8,6,0],[9,8,0],[11,8,0],[13,9,0],[15,12,0],[18,14,0],[21,17,0],
      [25,20,0],[30,23,0],[36,28,0],[42,32,0],[50,38,0],[60,46,0]
    ],
    0x3A5: [  // GREEK CAPITAL LETTER UPSILON
      [6,5,0],[7,6,0],[8,8,0],[10,8,0],[12,9,0],[14,12,0],[16,14,0],[19,17,0],
      [23,20,0],[27,23,0],[32,28,0],[38,32,0],[45,39,0],[53,47,0]
    ],
    0x3A6: [  // GREEK CAPITAL LETTER PHI
      [6,5,0],[7,6,0],[8,8,0],[9,8,0],[11,9,0],[13,12,0],[15,14,0],[18,17,0],
      [21,20,0],[25,23,0],[29,28,0],[35,32,0],[41,38,0],[49,46,0]
    ],
    0x3A8: [  // GREEK CAPITAL LETTER PSI
      [6,5,0],[7,6,0],[8,8,0],[10,8,0],[12,9,0],[14,12,0],[16,14,0],[19,17,0],
      [23,20,0],[27,23,0],[32,28,0],[38,32,0],[45,38,0],[53,45,0]
    ],
    0x3A9: [  // GREEK CAPITAL LETTER OMEGA
      [7,5,0],[8,6,0],[9,8,0],[11,8,0],[13,9,0],[15,12,0],[18,14,0],[21,17,0],
      [25,20,0],[29,23,0],[35,28,0],[41,32,0],[49,39,0],[58,47,0]
    ],
    0x3B1: [  // GREEK SMALL LETTER ALPHA
      [5,3,0],[6,4,0],[7,5,0],[9,5,0],[10,6,0],[12,8,0],[14,9,0],[17,11,0],
      [20,13,0],[24,15,0],[28,18,0],[34,21,0],[40,25,0],[47,31,1]
    ],
    0x3B2: [  // GREEK SMALL LETTER BETA
      [5,6,1],[6,8,2],[7,10,2],[8,10,2],[9,12,3],[11,16,4],[13,18,4],[15,22,5],
      [18,26,6],[21,30,7],[25,36,8],[30,41,9],[36,50,11],[42,60,14]
    ],
    0x3B3: [  // GREEK SMALL LETTER GAMMA
      [5,4,1],[6,6,2],[7,7,2],[8,7,2],[9,9,3],[11,12,4],[13,13,4],[15,16,5],
      [18,19,6],[21,22,7],[25,26,8],[29,30,9],[35,37,12],[41,45,15]
    ],
    0x3B4: [  // GREEK SMALL LETTER DELTA
      [4,5,0],[5,7,0],[6,8,0],[7,8,0],[8,10,0],[9,13,0],[10,16,0],[12,18,0],
      [15,21,0],[17,25,0],[21,29,0],[24,34,0],[29,41,0],[34,49,1]
    ],
    0x3B5: [  // GREEK SMALL LETTER EPSILON
      [4,3,0],[5,4,0],[5,5,0],[6,5,0],[7,6,0],[9,8,0],[10,9,0],[12,11,0],
      [14,13,0],[17,16,1],[20,19,1],[23,22,1],[28,27,1],[33,32,1]
    ],
    0x3B6: [  // GREEK SMALL LETTER ZETA
      [4,6,1],[5,8,2],[6,10,2],[7,10,2],[8,12,3],[9,16,4],[11,18,4],[13,22,5],
      [15,26,6],[18,31,7],[21,37,8],[25,43,9],[30,51,11],[35,61,13]
    ],
    0x3B7: [  // GREEK SMALL LETTER ETA
      [5,4,1],[5,6,2],[6,7,2],[7,7,2],[9,9,3],[10,12,4],[12,13,4],[14,16,5],
      [17,19,6],[20,22,7],[24,26,8],[28,30,9],[33,36,11],[39,45,15]
    ],
    0x3B8: [  // GREEK SMALL LETTER THETA
      [4,5,0],[5,6,0],[6,8,0],[7,8,0],[8,9,0],[10,12,0],[11,14,0],[13,17,0],
      [16,20,0],[19,23,0],[22,28,0],[26,32,0],[31,39,0],[37,47,1]
    ],
    0x3B9: [  // GREEK SMALL LETTER IOTA
      [3,3,0],[4,4,0],[4,5,0],[5,5,0],[6,6,0],[7,8,0],[8,9,0],[9,11,0],
      [11,13,0],[13,15,0],[16,18,0],[18,21,0],[22,25,0],[26,31,1]
    ],
    0x3BA: [  // GREEK SMALL LETTER KAPPA
      [5,3,0],[6,4,0],[7,5,0],[8,5,0],[9,6,0],[11,8,0],[13,9,0],[15,11,0],
      [18,13,0],[22,15,0],[26,18,0],[30,21,0],[36,25,0],[43,31,1]
    ],
    0x3BB: [  // GREEK SMALL LETTER LAMDA
      [5,5,0],[6,6,0],[7,8,0],[8,8,0],[9,9,0],[11,12,0],[13,14,0],[16,17,0],
      [18,20,0],[22,24,1],[26,29,1],[31,33,1],[36,38,0],[43,47,1]
    ],
    0x3BC: [  // GREEK SMALL LETTER MU
      [5,4,1],[6,6,2],[7,7,2],[8,7,2],[10,9,3],[12,12,4],[14,13,4],[16,16,5],
      [19,19,6],[23,22,7],[27,26,8],[32,31,10],[38,37,12],[45,45,15]
    ],
    0x3BD: [  // GREEK SMALL LETTER NU
      [5,3,0],[6,4,0],[6,5,0],[8,5,0],[9,6,0],[11,8,0],[12,9,0],[15,11,0],
      [17,13,0],[20,15,0],[24,18,0],[29,21,0],[34,25,0],[40,31,1]
    ],
    0x3BE: [  // GREEK SMALL LETTER XI
      [4,6,1],[5,8,2],[5,10,2],[6,10,2],[7,12,3],[9,16,4],[10,18,4],[12,22,5],
      [14,26,6],[17,30,7],[20,36,8],[23,43,9],[28,51,11],[33,61,13]
    ],
    0x3BF: [  // GREEK SMALL LETTER OMICRON
      [4,3,0],[5,4,0],[6,5,0],[7,5,0],[8,6,0],[10,8,0],[12,9,0],[14,11,0],
      [16,13,0],[19,15,0],[23,18,0],[27,21,0],[32,25,0],[38,31,1]
    ],
    0x3C0: [  // GREEK SMALL LETTER PI
      [5,3,0],[6,4,0],[7,5,0],[8,5,0],[10,6,0],[12,8,0],[14,9,0],[16,11,0],
      [19,13,0],[23,15,0],[27,18,0],[32,21,0],[38,25,0],[45,31,1]
    ],
    0x3C1: [  // GREEK SMALL LETTER RHO
      [5,4,1],[5,6,2],[6,7,2],[8,7,2],[9,9,3],[10,12,4],[12,13,4],[14,16,5],
      [17,19,6],[20,22,7],[24,26,8],[29,30,9],[34,37,12],[40,45,15]
    ],
    0x3C2: [  // GREEK SMALL LETTER FINAL SIGMA
      [4,4,1],[4,5,1],[5,6,1],[6,6,1],[7,7,1],[8,10,2],[9,12,3],[11,14,3],
      [13,16,3],[16,18,3],[18,22,4],[22,26,5],[26,31,6],[31,37,7]
    ],
    0x3C3: [  // GREEK SMALL LETTER SIGMA
      [5,3,0],[6,4,0],[7,5,0],[8,5,0],[10,6,0],[12,8,0],[14,9,0],[16,11,0],
      [19,13,0],[23,15,0],[27,18,0],[32,21,0],[38,25,0],[45,30,1]
    ],
    0x3C4: [  // GREEK SMALL LETTER TAU
      [5,3,0],[6,4,0],[6,5,0],[8,5,0],[9,6,0],[11,8,0],[12,9,0],[15,11,0],
      [17,13,0],[20,16,1],[24,19,1],[29,22,1],[34,25,0],[40,31,1]
    ],
    0x3C5: [  // GREEK SMALL LETTER UPSILON
      [5,3,0],[5,4,0],[6,5,0],[8,5,0],[9,6,0],[10,8,0],[12,9,0],[14,11,0],
      [17,13,0],[20,15,0],[24,18,0],[28,21,0],[34,25,0],[40,31,1]
    ],
    0x3C6: [  // GREEK SMALL LETTER PHI
      [5,4,1],[6,6,2],[7,7,2],[9,7,2],[10,9,3],[12,12,4],[14,13,4],[17,16,5],
      [20,19,6],[24,22,7],[28,26,8],[33,31,10],[39,37,12],[47,44,14]
    ],
    0x3C7: [  // GREEK SMALL LETTER CHI
      [5,4,1],[6,6,2],[7,7,2],[9,7,2],[10,9,3],[12,12,4],[14,13,4],[17,16,5],
      [20,19,6],[23,22,7],[28,26,8],[33,30,9],[39,36,11],[46,43,13]
    ],
    0x3C8: [  // GREEK SMALL LETTER PSI
      [6,6,1],[7,8,2],[8,10,2],[9,10,2],[11,12,3],[13,16,4],[15,18,4],[17,22,5],
      [21,26,6],[24,30,7],[29,36,8],[34,41,9],[41,50,11],[48,59,13]
    ],
    0x3C9: [  // GREEK SMALL LETTER OMEGA
      [5,3,0],[6,4,0],[7,5,0],[9,5,0],[10,6,0],[12,8,0],[14,9,0],[17,11,0],
      [20,13,0],[23,15,0],[28,18,0],[33,21,0],[39,25,0],[46,31,1]
    ],
    0x3D1: [  // GREEK THETA SYMBOL
      [5,5,0],[6,6,0],[7,8,0],[8,8,0],[9,9,0],[11,12,0],[13,14,0],[16,17,0],
      [18,20,0],[22,23,0],[26,28,0],[31,32,0],[36,39,0],[43,47,1]
    ],
    0x3D5: [  // GREEK PHI SYMBOL
      [5,6,1],[6,8,2],[7,10,2],[9,10,2],[10,12,3],[12,16,4],[14,18,4],[16,22,5],
      [20,26,6],[23,30,7],[27,36,8],[32,41,9],[39,49,11],[46,59,13]
    ],
    0x3D6: [  // GREEK PI SYMBOL
      [7,3,0],[8,4,0],[10,5,0],[12,5,0],[14,6,0],[16,8,0],[19,9,0],[23,11,0],
      [27,13,0],[32,15,0],[38,18,0],[45,21,0],[53,25,0],[63,31,1]
    ],
    0x3F1: [  // GREEK RHO SYMBOL
      [5,4,1],[5,6,2],[6,7,2],[8,7,2],[9,9,3],[10,12,4],[12,13,4],[14,16,5],
      [17,19,6],[20,22,7],[24,26,8],[28,30,9],[34,36,11],[40,44,14]
    ],
    0x3F5: [  // GREEK LUNATE EPSILON SYMBOL
      [4,3,0],[4,4,0],[5,5,0],[6,5,0],[7,6,0],[8,8,0],[9,9,0],[11,11,0],
      [13,13,0],[15,15,0],[18,18,0],[22,21,0],[26,25,0],[30,30,1]
    ]
  }
});

MathJax.Ajax.loadComplete(MathJax.OutputJax["HTML-CSS"].imgDir+"/Math/BoldItalic"+
                          MathJax.OutputJax["HTML-CSS"].imgPacked+"/Main.js");
