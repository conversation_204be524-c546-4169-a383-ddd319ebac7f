/*
 *  /MathJax/fonts/HTML-CSS/TeX/png/Script/Regular/BasicLatin.js
 *  
 *  Copyright (c) 2010-2013 The MathJax Consortium
 *
 *  Part of the MathJax library.
 *  See http://www.mathjax.org for details.
 * 
 *  Licensed under the Apache License, Version 2.0;
 *  you may not use this file except in compliance with the License.
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 */

MathJax.OutputJax["HTML-CSS"].defineImageData({MathJax_Script:{32:[[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0]],65:[[7,5,0],[9,6,0],[10,7,0],[12,9,0],[14,10,0],[17,12,0],[20,14,0],[24,17,0],[28,20,0],[34,25,1],[40,29,1],[48,34,1],[56,41,1],[67,48,1]],66:[[7,5,0],[8,6,0],[10,7,0],[11,8,0],[13,10,0],[16,12,0],[19,14,0],[22,17,0],[26,20,0],[31,24,1],[37,30,1],[44,35,1],[52,40,2],[62,48,2]],67:[[6,5,0],[7,6,0],[9,7,0],[10,9,0],[12,10,0],[14,12,0],[16,14,0],[20,17,0],[23,20,0],[27,25,1],[33,29,1],[39,34,1],[46,41,1],[54,50,3]],68:[[6,5,0],[8,6,0],[9,7,0],[11,8,0],[12,10,0],[15,12,0],[17,14,0],[20,17,0],[24,20,0],[29,25,1],[34,30,2],[40,35,2],[48,42,2],[57,49,2]],69:[[5,5,0],[6,6,0],[8,7,0],[9,8,0],[10,10,0],[12,12,0],[14,14,0],[17,17,0],[20,20,0],[24,24,1],[29,28,1],[34,33,1],[40,40,0],[48,47,1]],70:[[7,5,0],[9,6,0],[10,7,0],[12,8,0],[14,10,0],[17,12,0],[20,14,0],[24,17,0],[28,21,1],[33,26,2],[39,31,2],[47,36,2],[55,43,2],[66,51,2]],71:[[6,5,0],[7,6,0],[8,7,0],[9,9,0],[11,10,0],[13,12,0],[15,14,0],[18,17,0],[21,22,2],[25,25,1],[29,30,2],[35,35,2],[41,42,2],[49,50,3]],72:[[9,5,0],[11,6,0],[13,7,0],[15,8,0],[18,10,0],[21,12,0],[25,14,0],[29,17,0],[35,20,0],[41,25,1],[49,30,2],[58,35,2],[69,42,2],[82,50,3]],73:[[7,5,0],[8,6,0],[10,7,0],[12,8,0],[14,10,0],[16,12,0],[19,14,0],[22,17,0],[27,20,0],[32,25,1],[38,29,1],[44,34,1],[53,41,1],[63,48,1]],74:[[8,7,2],[10,9,3],[12,10,3],[14,12,4],[16,14,4],[19,17,5],[22,20,6],[27,24,7],[32,28,8],[38,36,12],[45,41,13],[53,48,15],[63,57,17],[75,68,21]],75:[[9,5,0],[10,6,0],[12,7,0],[15,8,0],[17,10,0],[20,12,0],[24,14,0],[28,17,0],[34,21,1],[40,26,2],[48,30,2],[56,35,2],[67,43,3],[80,50,3]],76:[[8,5,0],[9,6,0],[11,7,0],[13,8,0],[15,10,0],[18,12,0],[21,14,0],[25,17,0],[29,20,0],[35,25,1],[41,29,1],[49,34,1],[58,41,1],[69,48,1]],77:[[9,5,0],[11,6,0],[12,7,0],[15,8,0],[17,10,0],[21,12,0],[24,15,1],[29,18,1],[34,21,1],[40,27,3],[48,31,3],[57,36,3],[68,43,3],[80,51,4]],78:[[9,5,0],[11,6,0],[12,7,0],[15,8,0],[17,10,0],[21,12,0],[24,14,0],[29,17,0],[34,22,2],[40,27,3],[48,31,3],[57,36,2],[67,42,2],[80,51,2]],79:[[6,5,0],[7,6,0],[8,7,0],[10,8,0],[12,10,0],[14,12,0],[16,14,0],[19,17,0],[23,20,0],[27,25,1],[32,29,1],[38,33,0],[45,40,0],[53,47,0]],80:[[8,5,0],[9,6,0],[11,7,0],[13,8,0],[15,10,0],[18,12,0],[21,14,0],[24,17,0],[29,22,2],[34,25,1],[41,30,2],[48,35,2],[57,42,2],[68,50,3]],81:[[7,5,0],[8,6,0],[9,7,0],[11,8,0],[13,10,0],[15,12,0],[18,14,0],[21,17,0],[25,20,0],[30,25,1],[35,29,1],[42,34,1],[49,41,1],[59,48,1]],82:[[8,5,0],[9,6,0],[10,7,0],[12,8,0],[14,10,0],[16,12,0],[19,14,0],[22,17,0],[26,20,0],[31,25,1],[36,29,1],[43,34,1],[50,41,1],[60,48,1]],83:[[8,5,0],[9,6,0],[10,7,0],[12,9,0],[15,10,0],[17,12,0],[20,14,0],[24,17,0],[29,21,1],[34,25,1],[40,30,2],[48,35,2],[57,42,2],[67,49,2]],84:[[7,5,0],[9,6,0],[10,7,0],[12,8,0],[14,10,0],[17,12,0],[20,14,0],[24,17,0],[28,22,2],[33,27,2],[39,31,2],[47,36,2],[55,43,2],[66,51,2]],85:[[7,5,0],[8,6,0],[10,7,0],[12,8,0],[14,10,0],[16,12,0],[19,14,0],[23,17,0],[27,20,0],[32,25,1],[38,29,1],[45,34,1],[53,41,1],[64,48,1]],86:[[6,5,0],[8,6,0],[9,7,0],[10,8,0],[12,10,0],[15,12,0],[17,14,0],[20,17,0],[24,20,0],[28,25,1],[34,29,1],[40,34,1],[47,41,1],[56,48,1]],87:[[8,6,1],[9,7,1],[11,8,1],[13,9,1],[15,11,1],[18,13,1],[20,15,1],[24,18,1],[29,21,1],[34,26,2],[41,30,2],[48,35,2],[57,42,2],[68,49,2]],88:[[8,5,0],[10,6,0],[12,7,0],[14,8,0],[16,10,0],[19,12,0],[23,14,0],[27,17,0],[32,20,0],[38,25,1],[45,29,1],[53,34,1],[63,41,1],[75,48,1]],89:[[7,5,0],[8,6,0],[10,7,0],[12,8,0],[14,10,0],[16,12,0],[19,14,0],[23,17,0],[27,20,0],[32,25,1],[38,29,1],[45,34,1],[54,41,1],[64,48,1]],90:[[8,5,0],[9,6,0],[11,7,0],[13,8,0],[15,10,0],[18,12,0],[21,14,0],[24,17,0],[29,20,0],[34,25,1],[41,29,1],[48,34,1],[57,41,1],[68,48,1]]}});MathJax.Ajax.loadComplete(MathJax.OutputJax["HTML-CSS"].imgDir+"/Script/Regular"+MathJax.OutputJax["HTML-CSS"].imgPacked+"/BasicLatin.js");

