/*
 *  /MathJax/fonts/HTML-CSS/TeX/png/Main/Regular/MiscTechnical.js
 *  
 *  Copyright (c) 2010-2013 The MathJax Consortium
 *
 *  Part of the MathJax library.
 *  See http://www.mathjax.org for details.
 * 
 *  Licensed under the Apache License, Version 2.0;
 *  you may not use this file except in compliance with the License.
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 */

MathJax.OutputJax["HTML-CSS"].defineImageData({MathJax_Main:{8968:[[3,8,2],[4,9,2],[5,11,3],[5,12,3],[6,15,4],[8,17,4],[9,20,5],[10,25,7],[12,28,7],[14,34,9],[17,40,10],[20,47,12],[24,56,14],[28,67,17]],8969:[[2,8,2],[3,9,2],[3,11,3],[3,12,3],[4,15,4],[5,17,4],[5,20,5],[7,24,6],[8,28,7],[9,34,9],[11,40,10],[13,47,12],[15,56,14],[18,67,17]],8970:[[3,8,2],[4,9,2],[5,11,3],[5,12,3],[6,15,4],[8,17,4],[8,20,5],[10,24,6],[12,28,7],[14,34,9],[17,40,10],[20,47,12],[24,56,14],[28,67,17]],8971:[[2,8,2],[3,9,2],[3,11,3],[3,12,3],[4,15,4],[5,17,4],[5,20,5],[6,24,6],[8,28,7],[9,34,9],[11,40,10],[13,47,12],[15,56,14],[18,67,17]],8994:[[7,3,0],[8,4,0],[10,3,-1],[12,4,-1],[14,5,-1],[16,6,-1],[19,6,-2],[22,8,-2],[27,8,-3],[32,9,-4],[37,12,-4],[44,13,-5],[53,16,-6],[63,19,-7]],8995:[[7,2,-1],[8,3,-1],[10,3,-1],[12,4,-1],[14,4,-2],[16,5,-2],[19,6,-2],[22,6,-3],[27,8,-3],[32,9,-4],[38,10,-5],[44,12,-6],[53,15,-7],[63,17,-8]],9136:[[3,8,2],[3,9,2],[4,11,3],[5,12,3],[5,15,4],[6,17,4],[7,20,5],[9,24,6],[10,28,7],[12,33,8],[14,40,10],[17,46,11],[20,55,13],[24,65,16]],9137:[[3,8,2],[3,9,2],[4,11,3],[5,12,3],[5,15,4],[6,17,4],[7,20,5],[9,24,6],[10,28,7],[12,33,8],[14,39,10],[17,47,12],[20,55,14],[24,65,16]]}});MathJax.Ajax.loadComplete(MathJax.OutputJax["HTML-CSS"].imgDir+"/Main/Regular"+MathJax.OutputJax["HTML-CSS"].imgPacked+"/MiscTechnical.js");

