<?xml version="1.0" standalone="no"?> <!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.0//EN" "http://www.w3.org/TR/2001/REC-SVG-20010904/DTD/svg10.dtd" > <svg xmlns="http://www.w3.org/2000/svg" width="100%" height="100%">
<defs >
<font id="MathJax_Size3-Regular" horiz-adv-x="683" ><font-face
    font-family="MathJax_Size3"
    units-per-em="1000"
    panose-1="0 0 0 0 0 0 0 0 0 0"
    ascent="1717"
    descent="-1150"
    alphabetic="0" />
<missing-glyph horiz-adv-x="250" d="M50 0V533H200V0H50ZM100 50H150V483H100V50Z" />
<glyph unicode=" " glyph-name="space" horiz-adv-x="250" />
<glyph unicode="(" glyph-name="parenleft" horiz-adv-x="736" d="M701 -940Q701 -943 695 -949H664L660 -945Q630 -921 573 -860Q263 -519 216 53Q209 133 209 251Q209 367 216 449Q263 1020 573 1361Q630 1422 660 1446L664 1450H695Q701 1444 701 1441Q701
1437 690 1425Q495 1223 401 934T306 250Q306 -67 370 -323Q462 -687 690 -924Q701 -936 701 -940Z" />
<glyph unicode=")" glyph-name="parenright" horiz-adv-x="736" d="M428 251Q428 1044 34 1438Q34 1446 38 1448T56 1450H71L75 1446Q105 1422 162 1361Q472 1020 519 449Q526 367 526 251Q526 134 519 53Q472 -519 162 -860Q105 -921 75 -945L71 -949H56Q41 -949
38 -947T34 -937Q81 -890 140 -813Q428 -429 428 251Z" />
<glyph unicode="/" glyph-name="slash" horiz-adv-x="1044" d="M81 -949Q70 -949 63 -940T55 -921Q55 -917 56 -915Q59 -906 498 264T939 1438Q944 1450 960 1450Q972 1450 980 1441T988 1421Q982 1403 839 1020L398 -155Q107 -934 103 -938Q96 -949 81 -949Z" />
<glyph unicode="[" glyph-name="bracketleft" horiz-adv-x="528" d="M247 -949V1450H516V1388H309V-887H516V-949H247Z" />
<glyph unicode="\" glyph-name="backslash" horiz-adv-x="1044" d="M988 -922Q988 -933 980 -941T962 -949Q947 -949 940 -938Q936 -934 645 -155L204 1020Q56 1416 56 1424Q56 1436 64 1443T84 1450Q97 1448 103 1439Q107 1435 398 656L839 -519Q988 -918 988 -922Z" />
<glyph unicode="]" glyph-name="bracketright" horiz-adv-x="528" d="M11 1388V1450H280V-949H11V-887H218V1388H11Z" />
<glyph unicode="{" glyph-name="braceleft" horiz-adv-x="750" d="M618 -943L612 -949H582L568 -943Q472 -903 411 -841T332 -703Q327 -682 327 -653T325 -350Q324 -28 323 -18Q311 60 262 124T144 226Q132 233 131 237Q130 240 130 250T131 264T134 269T144 275Q207
308 256 367Q310 436 323 519Q324 529 325 851Q326 1124 326 1154T332 1205Q369 1358 566 1443L582 1450H612L618 1444V1429Q618 1413 616 1411Q616 1409 606 1405Q570 1387 544 1367Q449 1294 429 1200Q425 1180 425 1152T423 851Q422 579 422 549T416 498Q401
429 351 369T221 267Q218 265 212 262T203 257T197 254L188 251L205 242Q290 200 345 138T416 3Q421 -18 421 -48T423 -349Q423 -397 423 -472Q424 -677 428 -694Q429 -697 429 -699Q449 -793 544 -866Q570 -886 606 -904Q616 -908 616 -910Q618 -912 618 -928V-943Z"
/>
<glyph unicode="}" glyph-name="braceright" horiz-adv-x="750" d="M131 1414T131 1429T133 1447T148 1450H153H167L182 1444Q276 1404 336 1343T415 1207Q421 1184 421 1154T423 851L424 531L426 517Q443 399 547 313Q584 285 608 274Q615 270 616 267T618 251Q618
241 618 238T615 232T608 227Q550 198 508 151Q441 78 426 -15L424 -29L423 -350Q422 -622 422 -652T415 -706Q397 -780 337 -841T182 -943L167 -949H153Q137 -949 134 -947T131 -935V-928V-922Q131 -913 133 -910T142 -904Q146 -903 148 -902Q298 -820 323 -680Q324
-663 325 -349T327 -19Q340 60 392 125Q415 154 452 184Q493 216 541 241L561 250L541 260Q354 357 327 520Q326 537 325 850T323 1181Q298 1321 148 1403Q134 1409 132 1413Q131 1414 131 1429Z" />
<glyph unicode="&#xa0;" glyph-name="uni00A0" horiz-adv-x="250" />
<glyph unicode="&#x2c6;" glyph-name="circumflex" horiz-adv-x="1444" d="M1439 564Q1434 564 1080 631T722 698Q719 698 362 631Q7 564 4 564L0 583Q-4 602 -4 603L720 772L1083 688Q1446 603 1447 603Q1447 602 1443 583L1439 564Z" />
<glyph unicode="&#x2dc;" glyph-name="tilde" horiz-adv-x="1444" d="M11 610Q11 614 6 628T1 643Q1 646 50 662T119 683Q254 726 368 741Q419 749 476 749Q535 749 547 748Q624 740 751 703Q841 677 899 669Q944 662 999 662Q1162 662 1408 742Q1429 749 1432
749Q1432 745 1437 731T1442 716Q1442 711 1325 676Q1155 623 1012 611Q1000 610 955 610T898 611Q821 618 692 656Q602 682 545 690Q499 697 444 697Q281 697 35 617Q14 610 11 610Z" />
<glyph unicode="&#x302;" glyph-name="uni0302" horiz-adv-x="0" d="M-5 564Q-9 564 -363 631T-722 698Q-725 698 -1082 631Q-1437 564 -1440 564L-1444 583Q-1448 602 -1448 603L-724 772L-361 688Q2 603 3 603Q3 602 -1 583L-5 564Z" />
<glyph unicode="&#x303;" glyph-name="tildecomb" horiz-adv-x="0" d="M-1433 610Q-1433 614 -1438 628T-1443 643Q-1443 646 -1394 662T-1325 683Q-1190 726 -1076 741Q-1025 749 -968 749Q-909 749 -897 748Q-820 740 -693 703Q-603 677 -545 669Q-500 662 -445
662Q-282 662 -36 742Q-15 749 -12 749Q-12 745 -7 731T-2 716Q-2 711 -119 676Q-289 623 -432 611Q-444 610 -489 610T-546 611Q-623 618 -752 656Q-842 682 -899 690Q-945 697 -1000 697Q-1163 697 -1409 617Q-1430 610 -1433 610Z" />
<glyph unicode="&#x221a;" glyph-name="radical" horiz-adv-x="1000" d="M424 -948Q422 -947 313 -434T202 80Q202 83 170 31Q163 20 155 6T142 -14L137 -21Q131 -16 124 -8L111 5L264 248L473 -720Q473 -717 727 359T983 1440Q989 1450 1001 1450Q1007 1450 1013
1445T1020 1433Q1020 1425 742 244T460 -941Q457 -950 436 -950Q424 -950 424 -948Z" />
<glyph unicode="&#x2308;" glyph-name="uni2308" horiz-adv-x="583" d="M246 -949V1450H571V1388H308V-949H246Z" />
<glyph unicode="&#x2309;" glyph-name="uni2309" horiz-adv-x="583" d="M11 1388V1450H336V-949H274V1388H11Z" />
<glyph unicode="&#x230a;" glyph-name="uni230A" horiz-adv-x="583" d="M246 -949V1450H308V-887H571V-949H246Z" />
<glyph unicode="&#x230b;" glyph-name="uni230B" horiz-adv-x="583" d="M274 -887V1450H336V-949H11V-887H274Z" />
<glyph unicode="&#x27e8;" glyph-name="uni27E8" horiz-adv-x="750" d="M126 242V259L361 845Q595 1431 597 1435Q608 1450 624 1450Q637 1450 645 1441T654 1419V1411L422 831Q190 253 190 250T422 -331L654 -910V-919Q654 -933 645 -941T624 -950T597 -935Q595
-931 361 -345L126 242Z" />
<glyph unicode="&#x27e9;" glyph-name="uni27E9" horiz-adv-x="750" d="M125 -949Q95 -949 95 -919V-910L327 -331Q559 247 559 250T327 831Q94 1411 94 1424Q94 1426 95 1428Q104 1450 124 1450Q141 1450 152 1435Q154 1431 388 845L623 259V242L388 -345Q153
-933 152 -934Q144 -949 125 -949Z" />
<glyph unicode="&#xeffd;" glyph-name="uniEFFD" horiz-adv-x="300" d="M0 0V100H100V0H0ZM200 0V100H300V0H200Z" />
<glyph unicode="&#xeffe;" glyph-name="uniEFFE" horiz-adv-x="300" d="M0 0V100H100V0H0ZM200 0V100H300V0H200Z" />
<glyph unicode="&#xefff;" glyph-name="uniEFFF" horiz-adv-x="100" d="M0 0V100H100V0H0Z" />
</font>
</defs>
</svg>
