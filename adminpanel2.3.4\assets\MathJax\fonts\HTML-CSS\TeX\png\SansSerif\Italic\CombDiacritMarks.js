/*
 *  /MathJax/fonts/HTML-CSS/TeX/png/SansSerif/Italic/CombDiacritMarks.js
 *  
 *  Copyright (c) 2010-2013 The MathJax Consortium
 *
 *  Part of the MathJax library.
 *  See http://www.mathjax.org for details.
 * 
 *  Licensed under the Apache License, Version 2.0;
 *  you may not use this file except in compliance with the License.
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 */

MathJax.OutputJax["HTML-CSS"].defineImageData({"MathJax_SansSerif-italic":{768:[[2,1,-4],[3,2,-4],[3,2,-6],[3,2,-7],[3,2,-7],[4,3,-9],[5,4,-10],[5,4,-13],[6,5,-15],[7,6,-17],[8,7,-21],[9,8,-24],[11,10,-30],[13,11,-34]],769:[[3,1,-4],[3,2,-4],[3,2,-6],[4,2,-7],[4,2,-7],[6,3,-9],[6,4,-10],[7,4,-13],[8,5,-15],[10,6,-17],[11,7,-21],[12,8,-24],[15,10,-30],[18,11,-34]],770:[[4,1,-4],[4,2,-4],[5,2,-6],[5,2,-7],[6,2,-7],[7,3,-9],[8,4,-10],[9,4,-13],[10,5,-15],[13,6,-17],[15,7,-21],[17,8,-24],[20,10,-30],[24,11,-34]],771:[[4,1,-3],[4,1,-5],[4,2,-5],[5,3,-6],[6,2,-7],[6,3,-9],[8,3,-10],[9,3,-13],[11,4,-15],[12,5,-17],[15,6,-21],[17,6,-25],[21,8,-31],[24,9,-35]],772:[[4,1,-3],[4,1,-5],[5,1,-6],[5,2,-6],[6,2,-6],[8,3,-9],[9,2,-11],[10,2,-13],[11,3,-16],[14,3,-18],[16,4,-22],[18,4,-25],[22,5,-31],[26,6,-36]],774:[[3,1,-4],[4,2,-4],[4,2,-6],[5,3,-6],[6,3,-6],[7,4,-8],[8,4,-10],[9,4,-13],[11,6,-14],[13,6,-17],[15,8,-20],[18,9,-23],[21,11,-29],[24,12,-33]],775:[[2,1,-3],[2,1,-5],[2,2,-5],[3,2,-7],[3,2,-7],[3,2,-10],[3,3,-10],[4,3,-13],[4,3,-16],[5,4,-18],[6,5,-22],[7,5,-26],[8,6,-33],[9,7,-38]],776:[[3,1,-3],[4,1,-5],[4,2,-5],[5,2,-7],[5,2,-7],[6,2,-10],[7,2,-11],[8,3,-13],[10,3,-16],[11,4,-18],[13,4,-23],[15,5,-26],[19,6,-33],[21,7,-38]],778:[[2,1,-3],[2,2,-4],[3,2,-5],[3,2,-7],[4,2,-7],[4,3,-9],[5,3,-10],[6,5,-11],[7,5,-14],[8,6,-16],[9,7,-21],[11,8,-24],[13,10,-30],[15,11,-34]],779:[[3,2,-2],[4,2,-4],[4,2,-5],[5,3,-6],[5,3,-6],[7,3,-9],[8,4,-9],[9,5,-11],[10,5,-14],[13,6,-16],[15,8,-20],[17,9,-23],[20,10,-30],[24,11,-34]],780:[[3,1,-3],[4,2,-4],[4,2,-5],[5,2,-6],[5,2,-6],[6,3,-9],[8,4,-10],[9,4,-12],[10,5,-14],[12,6,-16],[15,7,-19],[17,8,-22],[20,10,-28],[23,11,-32]]}});MathJax.Ajax.loadComplete(MathJax.OutputJax["HTML-CSS"].imgDir+"/SansSerif/Italic"+MathJax.OutputJax["HTML-CSS"].imgPacked+"/CombDiacritMarks.js");

