<!DOCTYPE html>
<html lang="">
<meta content="width=device-width, initial-scale=1.0" name="viewport">

<head>
    <script src="../flutter_tex.js" type="text/javascript">
    </script>
    <link crossorigin="anonymous" href="../flutter_tex.css" rel="stylesheet">
    <link crossorigin="anonymous" href="katex.min.css" rel="stylesheet">
    <script crossorigin="anonymous" src="katex.min.js"></script>
    <script crossorigin="anonymous" src="contrib/mathtex-script-type.min.js"></script>
    <script crossorigin="anonymous" src="contrib/auto-render.min.js"></script>
    <script crossorigin="anonymous" src="contrib/mhchem.min.js"></script>
    <title>katex</title>
    <script>
        var formulasScrollStyle =
            '.katex-display > .katex {                                    \n' +
            '   display: inline-block;                                    \n' +
            '   white-space: nowrap;                                      \n' +
            '   max-width: 100%;                                          \n' +
            '   overflow-x: scroll;                                       \n' +
            '   text-align: initial;                                      \n' +
            '}                                                            \n' +

            '.katex {                                                     \n' +
            '   font: normal 1.21em KaTeX_Main, Times New Roman, serif;   \n' +
            '   line-height: 1.2;                                         \n' +
            '   white-space: normal;                                      \n' +
            '   text-indent: 0;                                           \n' +
            '}';
    </script>
</head>

<body>
    <div id="TeXView"></div>

    <script>

        teXView = document.getElementById('TeXView');

        function initView(jsonData) {
            appendStyle(formulasScrollStyle);
            initTeXView(jsonData, "katex");
        }

        function renderTeXView(onCompleteCallback) {
            renderMathInElement(teXView);
            setTimeout(function () {
                onCompleteCallback();
            }, 0)
        }


    </script>
</body>

</html>