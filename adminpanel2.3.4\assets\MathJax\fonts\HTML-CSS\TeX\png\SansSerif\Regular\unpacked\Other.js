/*************************************************************
 *
 *  MathJax/fonts/HTML-CSS/TeX/png/SansSerif/Regular/Other.js
 *  
 *  Defines the image size data needed for the HTML-CSS OutputJax
 *  to display mathematics using fallback images when the fonts
 *  are not available to the client browser.
 *
 *  ---------------------------------------------------------------------
 *
 *  Copyright (c) 2009-2013 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 *
 */

MathJax.OutputJax["HTML-CSS"].defineImageData({
  "MathJax_SansSerif": {
    0xA0: [  // NO-BREAK SPACE
      [1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],
      [1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0]
    ],
    0x131: [  // LATIN SMALL LETTER DOTLESS I
      [2,3,0],[2,4,0],[2,5,0],[2,6,0],[3,6,0],[3,8,0],[4,9,0],[4,11,0],
      [5,13,0],[6,15,0],[7,17,0],[8,20,0],[10,26,0],[11,30,0]
    ],
    0x237: [  // LATIN SMALL LETTER DOTLESS J
      [3,4,1],[3,6,2],[3,7,2],[4,9,3],[4,9,3],[5,11,3],[6,13,4],[7,16,5],
      [8,18,5],[9,21,6],[11,25,8],[12,30,10],[15,38,12],[17,43,14]
    ],
    0x300: [  // COMBINING GRAVE ACCENT
      [2,1,-4],[3,2,-4],[4,2,-6],[3,2,-7],[4,2,-7],[4,3,-9],[6,4,-10],[6,4,-13],
      [7,5,-15],[8,6,-17],[10,7,-20],[11,8,-24],[13,10,-29],[15,11,-34]
    ],
    0x301: [  // COMBINING ACUTE ACCENT
      [3,1,-4],[3,2,-4],[3,2,-6],[4,2,-7],[4,2,-7],[5,3,-9],[5,4,-10],[7,4,-13],
      [7,5,-15],[8,6,-17],[9,7,-20],[12,8,-24],[13,10,-29],[15,11,-34]
    ],
    0x302: [  // COMBINING CIRCUMFLEX ACCENT
      [3,1,-4],[4,2,-4],[5,2,-6],[5,2,-7],[5,2,-7],[6,3,-9],[8,4,-10],[9,4,-13],
      [10,5,-15],[12,6,-17],[14,7,-20],[17,8,-24],[20,10,-29],[23,11,-34]
    ],
    0x303: [  // COMBINING TILDE
      [3,1,-4],[4,1,-5],[5,2,-6],[5,3,-6],[5,2,-7],[6,3,-9],[8,3,-11],[9,3,-14],
      [10,4,-16],[12,5,-18],[14,6,-21],[17,6,-25],[20,8,-31],[23,9,-35]
    ],
    0x304: [  // COMBINING MACRON
      [3,1,-3],[4,1,-5],[5,1,-6],[6,2,-6],[6,2,-6],[7,3,-9],[8,2,-11],[9,2,-13],
      [11,3,-16],[13,3,-18],[15,4,-22],[18,4,-25],[21,5,-31],[25,6,-36]
    ],
    0x306: [  // COMBINING BREVE
      [3,1,-4],[4,2,-4],[5,2,-6],[6,3,-6],[5,3,-6],[7,4,-8],[8,4,-10],[9,4,-13],
      [10,6,-14],[13,6,-17],[15,8,-19],[17,9,-23],[20,11,-28],[25,12,-33]
    ],
    0x307: [  // COMBINING DOT ABOVE
      [2,1,-4],[2,1,-5],[2,2,-6],[2,2,-7],[3,2,-7],[3,2,-10],[3,3,-11],[4,3,-14],
      [4,3,-17],[4,4,-19],[5,5,-23],[6,5,-26],[7,6,-33],[7,7,-38]
    ],
    0x308: [  // COMBINING DIAERESIS
      [3,1,-4],[4,1,-5],[3,2,-6],[4,2,-7],[5,2,-7],[6,2,-10],[6,2,-12],[8,3,-14],
      [9,3,-17],[11,4,-19],[12,4,-23],[15,5,-27],[17,6,-33],[21,7,-38]
    ],
    0x30A: [  // COMBINING RING ABOVE
      [3,1,-4],[3,2,-4],[4,2,-6],[3,2,-7],[4,2,-7],[5,3,-9],[6,3,-11],[6,5,-12],
      [7,6,-14],[8,6,-17],[10,7,-20],[11,8,-24],[13,10,-29],[15,11,-34]
    ],
    0x30B: [  // COMBINING DOUBLE ACUTE ACCENT
      [3,2,-3],[4,2,-4],[4,2,-6],[5,3,-6],[5,3,-6],[6,3,-9],[7,4,-10],[9,5,-12],
      [10,5,-15],[12,6,-17],[13,7,-20],[16,8,-24],[19,10,-29],[22,11,-34]
    ],
    0x30C: [  // COMBINING CARON
      [3,1,-3],[4,2,-4],[5,2,-5],[5,2,-6],[5,2,-6],[6,3,-9],[8,4,-10],[9,4,-12],
      [10,5,-14],[12,6,-16],[14,7,-19],[17,8,-22],[20,10,-28],[23,11,-32]
    ],
    0x393: [  // GREEK CAPITAL LETTER GAMMA
      [4,5,0],[5,6,0],[5,8,0],[6,9,0],[7,9,0],[9,12,0],[10,14,0],[12,17,0],
      [14,20,0],[17,23,0],[20,27,0],[24,32,0],[28,39,0],[33,45,0]
    ],
    0x394: [  // GREEK CAPITAL LETTER DELTA
      [6,5,0],[7,6,0],[8,8,0],[10,9,0],[11,9,0],[14,12,0],[16,14,0],[19,17,0],
      [22,20,0],[26,23,0],[31,27,0],[37,32,0],[44,39,0],[52,45,0]
    ],
    0x398: [  // GREEK CAPITAL LETTER THETA
      [5,5,0],[6,6,0],[8,8,0],[9,9,0],[10,9,0],[12,12,0],[15,14,0],[17,19,1],
      [20,22,1],[24,25,1],[29,27,0],[34,34,1],[40,42,1],[48,49,1]
    ],
    0x39B: [  // GREEK CAPITAL LETTER LAMDA
      [5,5,0],[5,6,0],[6,8,0],[7,9,0],[9,9,0],[10,12,0],[12,14,0],[14,17,0],
      [17,20,0],[20,23,0],[23,27,0],[28,32,0],[33,39,0],[39,45,0]
    ],
    0x39E: [  // GREEK CAPITAL LETTER XI
      [5,5,0],[6,6,0],[7,8,0],[8,9,0],[9,9,0],[11,12,0],[13,14,0],[15,17,0],
      [18,20,0],[21,23,0],[25,27,0],[29,32,0],[35,39,0],[42,45,0]
    ],
    0x3A0: [  // GREEK CAPITAL LETTER PI
      [5,5,0],[6,6,0],[7,8,0],[8,9,0],[9,9,0],[11,12,0],[13,14,0],[15,17,0],
      [18,20,0],[21,23,0],[25,27,0],[29,32,0],[35,39,0],[41,45,0]
    ],
    0x3A3: [  // GREEK CAPITAL LETTER SIGMA
      [5,5,0],[6,6,0],[7,8,0],[8,9,0],[10,9,0],[12,12,0],[13,14,0],[16,17,0],
      [19,20,0],[22,23,0],[27,27,0],[31,32,0],[37,39,0],[44,45,0]
    ],
    0x3A5: [  // GREEK CAPITAL LETTER UPSILON
      [5,5,0],[6,6,0],[8,8,0],[9,9,0],[10,9,0],[12,12,0],[15,14,0],[17,18,0],
      [20,21,0],[24,24,0],[29,27,0],[34,33,0],[40,41,0],[48,47,0]
    ],
    0x3A6: [  // GREEK CAPITAL LETTER PHI
      [5,5,0],[6,6,0],[7,8,0],[8,9,0],[10,9,0],[12,12,0],[13,14,0],[16,17,0],
      [19,20,0],[22,23,0],[27,27,0],[31,32,0],[37,39,0],[44,45,0]
    ],
    0x3A8: [  // GREEK CAPITAL LETTER PSI
      [5,5,0],[6,6,0],[8,8,0],[9,9,0],[10,9,0],[12,12,0],[15,14,0],[17,17,0],
      [20,20,0],[24,23,0],[29,27,0],[34,32,0],[40,39,0],[48,45,0]
    ],
    0x3A9: [  // GREEK CAPITAL LETTER OMEGA
      [5,5,0],[6,6,0],[7,8,0],[8,9,0],[10,9,0],[12,12,0],[14,14,0],[16,18,0],
      [19,21,0],[23,24,0],[27,27,0],[32,33,0],[38,41,0],[45,47,0]
    ],
    0x2013: [  // EN DASH
      [4,1,-1],[5,1,-2],[5,1,-2],[6,1,-3],[7,1,-3],[9,3,-3],[10,2,-5],[12,2,-6],
      [14,3,-7],[17,3,-8],[20,3,-9],[24,4,-11],[28,5,-13],[33,5,-15]
    ],
    0x2014: [  // EM DASH
      [7,1,-1],[9,1,-2],[10,1,-2],[12,1,-3],[14,1,-3],[17,3,-3],[20,2,-5],[24,2,-6],
      [28,3,-7],[33,3,-8],[40,3,-9],[47,4,-11],[56,5,-13],[66,5,-15]
    ],
    0x2018: [  // LEFT SINGLE QUOTATION MARK
      [2,2,-3],[2,2,-4],[2,3,-5],[3,4,-5],[3,4,-5],[4,4,-8],[4,4,-10],[5,6,-11],
      [6,6,-14],[7,8,-15],[8,9,-18],[9,10,-22],[11,13,-26],[13,15,-30]
    ],
    0x2019: [  // RIGHT SINGLE QUOTATION MARK
      [2,2,-3],[2,2,-4],[2,3,-5],[3,3,-6],[3,3,-6],[4,4,-8],[4,5,-9],[5,6,-11],
      [6,7,-13],[7,8,-15],[8,9,-18],[9,10,-22],[11,13,-26],[13,15,-30]
    ],
    0x201C: [  // LEFT DOUBLE QUOTATION MARK
      [4,2,-3],[4,2,-4],[5,3,-5],[6,4,-5],[7,4,-5],[8,4,-8],[10,4,-10],[11,6,-11],
      [13,6,-14],[16,8,-15],[19,9,-18],[22,10,-22],[26,13,-26],[31,15,-30]
    ],
    0x201D: [  // RIGHT DOUBLE QUOTATION MARK
      [3,2,-3],[3,2,-4],[4,3,-5],[4,3,-6],[5,3,-6],[6,4,-8],[7,5,-9],[8,6,-11],
      [9,7,-13],[11,8,-15],[13,9,-18],[16,10,-22],[18,13,-26],[22,15,-30]
    ]
  }
});

MathJax.Ajax.loadComplete(MathJax.OutputJax["HTML-CSS"].imgDir+"/SansSerif/Regular"+
                          MathJax.OutputJax["HTML-CSS"].imgPacked+"/Other.js");
