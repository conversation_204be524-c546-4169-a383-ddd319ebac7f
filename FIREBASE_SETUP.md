# Firebase Setup Guide for Elite Quiz

## Overview

Firebase provides essential services for Elite Quiz:
- **Authentication** - User login/registration
- **Firestore** - Real-time battle rooms
- **Cloud Messaging** - Push notifications
- **Analytics** - User behavior tracking

## Step 1: Create Firebase Project

### 1.1 Go to Firebase Console
- Visit: https://console.firebase.google.com
- Sign in with Google account

### 1.2 Create New Project
1. Click "Create a project"
2. Project name: `elite-quiz-local` (or your preferred name)
3. Enable Google Analytics (recommended)
4. Choose Analytics account or create new
5. Click "Create project"

## Step 2: Configure Authentication

### 2.1 Enable Authentication
1. Go to **Authentication** in left sidebar
2. Click **Get started**
3. Go to **Sign-in method** tab

### 2.2 Enable Sign-in Providers
Enable the following providers:

**Email/Password:**
1. Click on "Email/Password"
2. Enable "Email/Password"
3. Enable "Email link (passwordless sign-in)" if desired
4. Click "Save"

**Google:**
1. Click on "Google"
2. Enable Google sign-in
3. Set project support email
4. Click "Save"

**Phone:**
1. Click on "Phone"
2. Enable phone authentication
3. Add test phone numbers if needed
4. Click "Save"

**Apple (for iOS):**
1. Click on "Apple"
2. Enable Apple sign-in
3. Configure Apple Developer settings
4. Click "Save"

## Step 3: Setup Firestore Database

### 3.1 Create Firestore Database
1. Go to **Firestore Database** in sidebar
2. Click **Create database**
3. Choose **Start in test mode** (for development)
4. Select location (choose closest to your users)
5. Click **Done**

### 3.2 Configure Security Rules
Replace default rules with:

```javascript
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Battle rooms - authenticated users only
    match /battleRoom/{roomId} {
      allow read, write: if request.auth != null;
    }
    
    // User data - owner only
    match /users/{userId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }
    
    // Public data (categories, questions) - read only
    match /public/{document=**} {
      allow read: if true;
      allow write: if false;
    }
    
    // Allow authenticated users to read/write their own data
    match /{document=**} {
      allow read, write: if request.auth != null;
    }
  }
}
```

### 3.3 Create Indexes
Go to **Indexes** tab and create:

1. **Battle Room Index:**
   - Collection: `battleRoom`
   - Fields: `createdBy` (Ascending), `createdAt` (Descending)

2. **User Statistics Index:**
   - Collection: `userStats`
   - Fields: `userId` (Ascending), `date` (Descending)

## Step 4: Setup Cloud Messaging

### 4.1 Enable Cloud Messaging
1. Go to **Cloud Messaging** in sidebar
2. Cloud Messaging is automatically enabled

### 4.2 Get Server Key
1. Go to **Project Settings** (gear icon)
2. Go to **Cloud Messaging** tab
3. Copy **Server key** - you'll need this for admin panel

## Step 5: Add Android App

### 5.1 Register Android App
1. Go to **Project Settings**
2. Click **Add app** → **Android**
3. Android package name: `com.wrteam.flutterquiz`
4. App nickname: `Elite Quiz Android`
5. SHA-1 certificate (optional for development)
6. Click **Register app**

### 5.2 Download Config File
1. Download `google-services.json`
2. Place in: `elite_quiz_app-2.3.4/android/app/google-services.json`

### 5.3 Add Firebase SDK
The Flutter app already includes Firebase dependencies in `pubspec.yaml`

## Step 6: Add iOS App (Optional)

### 6.1 Register iOS App
1. Go to **Project Settings**
2. Click **Add app** → **iOS**
3. iOS bundle ID: `com.wrteam.flutterquiz`
4. App nickname: `Elite Quiz iOS`
5. Click **Register app**

### 6.2 Download Config File
1. Download `GoogleService-Info.plist`
2. Place in: `elite_quiz_app-2.3.4/ios/Runner/GoogleService-Info.plist`

## Step 7: Configure Admin Panel

### 7.1 Get Firebase Config
1. Go to **Project Settings**
2. Go to **General** tab
3. Scroll to **Your apps** section
4. Click on **Web app** (or add web app if not exists)
5. Copy the Firebase config object

### 7.2 Update Admin Panel Settings
Add Firebase configuration to admin panel database:

```sql
-- Insert Firebase settings
INSERT INTO tbl_settings (type, message) VALUES 
('firebase_api_key', 'your_api_key'),
('firebase_auth_domain', 'your_project.firebaseapp.com'),
('firebase_project_id', 'your_project_id'),
('firebase_storage_bucket', 'your_project.appspot.com'),
('firebase_messaging_sender_id', 'your_sender_id'),
('firebase_app_id', 'your_app_id'),
('fcm_server_key', 'your_server_key');
```

## Step 8: Test Firebase Integration

### 8.1 Test Authentication
1. Run Flutter app
2. Try registering with email
3. Check Firebase Console → Authentication → Users

### 8.2 Test Firestore
1. Create a battle room in app
2. Check Firebase Console → Firestore → Data
3. Should see `battleRoom` collection

### 8.3 Test Cloud Messaging
1. Send test notification from Firebase Console
2. Go to Cloud Messaging → Send your first message
3. Target: Single device or topic

## Firebase Configuration Files

### For Flutter App

**android/app/google-services.json:**
```json
{
  "project_info": {
    "project_number": "123456789",
    "project_id": "elite-quiz-local",
    "storage_bucket": "elite-quiz-local.appspot.com"
  },
  "client": [
    {
      "client_info": {
        "mobilesdk_app_id": "1:123456789:android:abcdef",
        "android_client_info": {
          "package_name": "com.wrteam.flutterquiz"
        }
      }
    }
  ]
}
```

**ios/Runner/GoogleService-Info.plist:**
```xml
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
    <key>API_KEY</key>
    <string>your_api_key</string>
    <key>GCM_SENDER_ID</key>
    <string>123456789</string>
    <key>PLIST_VERSION</key>
    <string>1</string>
    <key>BUNDLE_ID</key>
    <string>com.wrteam.flutterquiz</string>
    <key>PROJECT_ID</key>
    <string>elite-quiz-local</string>
</dict>
</plist>
```

## Environment-Specific Configuration

### Development Environment
```dart
// lib/core/config/firebase_config.dart
class FirebaseConfig {
  static const String projectId = 'elite-quiz-local';
  static const String apiKey = 'your_dev_api_key';
  static const String authDomain = 'elite-quiz-local.firebaseapp.com';
  static const String storageBucket = 'elite-quiz-local.appspot.com';
  static const String messagingSenderId = '123456789';
  static const String appId = 'your_app_id';
}
```

### Production Environment
```dart
// Use different project for production
class FirebaseConfig {
  static const String projectId = 'elite-quiz-prod';
  static const String apiKey = 'your_prod_api_key';
  // ... other production config
}
```

## Security Best Practices

### 1. Firestore Rules
- Never use `allow read, write: if true;` in production
- Implement proper user-based access control
- Validate data before writing

### 2. Authentication
- Enable email verification
- Implement proper password policies
- Use multi-factor authentication for admin

### 3. API Keys
- Restrict API keys to specific domains/apps
- Use different projects for dev/staging/prod
- Regularly rotate server keys

## Troubleshooting

### Common Issues

**1. Google Services Plugin Error**
```
Error: google-services.json not found
```
**Solution:** Ensure `google-services.json` is in `android/app/` directory

**2. Firebase Not Initialized**
```
Error: Firebase has not been correctly initialized
```
**Solution:** Check Firebase initialization in `main.dart`

**3. Authentication Failed**
```
Error: Sign-in failed
```
**Solution:** 
- Check if authentication provider is enabled
- Verify SHA-1 certificate for Google sign-in
- Check network connectivity

**4. Firestore Permission Denied**
```
Error: Missing or insufficient permissions
```
**Solution:** Check Firestore security rules

**5. FCM Token Not Generated**
```
Error: FCM token is null
```
**Solution:**
- Check if Cloud Messaging is enabled
- Verify app permissions for notifications
- Test on physical device (not emulator)

### Debug Commands

```bash
# Check Firebase CLI
firebase --version

# Login to Firebase
firebase login

# List projects
firebase projects:list

# Deploy Firestore rules
firebase deploy --only firestore:rules

# Test Firestore rules
firebase emulators:start --only firestore
```

## Next Steps

1. **Test all authentication methods**
2. **Create sample battle rooms**
3. **Send test push notifications**
4. **Monitor Firebase usage in console**
5. **Set up Firebase Analytics events**

Your Firebase setup is now complete! 🔥
