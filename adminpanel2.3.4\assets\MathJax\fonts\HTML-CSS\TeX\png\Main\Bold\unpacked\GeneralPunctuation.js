/*************************************************************
 *
 *  MathJax/fonts/HTML-CSS/TeX/png/Main/Bold/GeneralPunctuation.js
 *  
 *  Defines the image size data needed for the HTML-CSS OutputJax
 *  to display mathematics using fallback images when the fonts
 *  are not available to the client browser.
 *
 *  ---------------------------------------------------------------------
 *
 *  Copyright (c) 2009-2013 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 *
 */

MathJax.OutputJax["HTML-CSS"].defineImageData({
  "MathJax_Main-bold": {
    0x2002: [  // ??
      [1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],
      [1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0]
    ],
    0x2003: [  // ??
      [1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],
      [1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0]
    ],
    0x2004: [  // ??
      [1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],
      [1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0]
    ],
    0x2005: [  // ??
      [1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],
      [1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0]
    ],
    0x2006: [  // ??
      [1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],
      [1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0]
    ],
    0x2009: [  // ??
      [1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],
      [1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0]
    ],
    0x200A: [  // ??
      [1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],
      [1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0]
    ],
    0x2013: [  // EN DASH
      [4,1,-1],[5,1,-2],[6,1,-3],[7,1,-3],[8,1,-3],[10,1,-4],[12,2,-4],[14,3,-5],
      [16,3,-6],[19,2,-8],[23,3,-10],[27,3,-12],[32,3,-14],[38,4,-17]
    ],
    0x2014: [  // EM DASH
      [8,1,-1],[10,1,-2],[12,1,-3],[14,1,-3],[16,1,-3],[20,1,-4],[23,2,-4],[27,3,-5],
      [32,3,-6],[38,2,-8],[45,3,-10],[54,3,-12],[64,3,-14],[76,4,-17]
    ],
    0x2018: [  // LEFT SINGLE QUOTATION MARK
      [2,3,-2],[3,3,-3],[3,4,-4],[3,4,-4],[4,5,-4],[5,7,-5],[5,8,-6],[6,9,-8],
      [7,11,-9],[9,12,-11],[10,15,-13],[12,17,-15],[14,20,-18],[17,25,-21]
    ],
    0x2019: [  // RIGHT SINGLE QUOTATION MARK
      [2,3,-2],[3,4,-2],[3,4,-4],[4,4,-4],[4,5,-4],[5,7,-5],[6,8,-6],[7,9,-8],
      [8,11,-9],[9,13,-10],[11,15,-13],[13,17,-15],[15,20,-18],[18,25,-21]
    ],
    0x201C: [  // LEFT DOUBLE QUOTATION MARK
      [4,3,-2],[5,3,-3],[6,4,-4],[7,4,-4],[8,5,-4],[10,7,-5],[11,8,-6],[14,9,-8],
      [16,11,-9],[19,12,-11],[23,15,-13],[27,17,-15],[32,20,-18],[38,25,-21]
    ],
    0x201D: [  // RIGHT DOUBLE QUOTATION MARK
      [4,3,-2],[5,4,-2],[5,4,-4],[6,4,-4],[7,5,-4],[9,7,-5],[10,8,-6],[12,9,-8],
      [14,11,-9],[17,13,-10],[20,15,-13],[23,17,-15],[28,20,-18],[33,25,-21]
    ],
    0x2020: [  // DAGGER
      [4,6,1],[4,8,2],[5,10,2],[6,10,2],[7,12,3],[8,15,3],[9,18,4],[11,22,5],
      [13,26,6],[15,29,6],[18,36,8],[21,41,9],[25,49,11],[30,61,14]
    ],
    0x2021: [  // DOUBLE DAGGER
      [4,6,1],[4,8,2],[5,10,2],[6,10,2],[7,12,3],[8,15,3],[9,18,4],[11,22,5],
      [13,26,6],[15,29,6],[18,36,8],[21,41,9],[25,49,11],[29,59,13]
    ],
    0x2026: [  // HORIZONTAL ELLIPSIS
      [9,2,0],[11,2,0],[12,2,0],[15,2,0],[17,3,0],[21,4,0],[24,4,0],[29,5,0],
      [34,5,0],[41,6,0],[48,7,0],[57,8,0],[68,10,0],[81,12,0]
    ],
    0x2032: [  // PRIME
      [3,4,0],[3,5,0],[4,6,0],[4,6,0],[5,7,0],[6,10,-1],[7,11,-1],[8,13,-1],
      [10,15,-1],[11,18,-1],[13,21,-1],[16,25,-2],[19,30,-2],[22,35,-2]
    ]
  }
});

MathJax.Ajax.loadComplete(MathJax.OutputJax["HTML-CSS"].imgDir+"/Main/Bold"+
                          MathJax.OutputJax["HTML-CSS"].imgPacked+"/GeneralPunctuation.js");
