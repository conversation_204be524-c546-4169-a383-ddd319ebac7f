/*
 *  /MathJax/fonts/HTML-CSS/TeX/png/Main/Regular/Latin1Supplement.js
 *  
 *  Copyright (c) 2010-2013 The MathJax Consortium
 *
 *  Part of the MathJax library.
 *  See http://www.mathjax.org for details.
 * 
 *  Licensed under the Apache License, Version 2.0;
 *  you may not use this file except in compliance with the License.
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 */

MathJax.OutputJax["HTML-CSS"].defineImageData({MathJax_Main:{160:[[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0]],168:[[3,1,-4],[4,2,-4],[4,2,-6],[5,2,-6],[6,2,-7],[7,3,-9],[8,3,-11],[10,3,-14],[12,4,-16],[14,4,-19],[16,5,-22],[19,6,-26],[23,7,-31],[27,8,-37]],172:[[5,3,0],[6,3,0],[6,4,0],[8,4,0],[9,4,-1],[11,5,-2],[12,6,-1],[15,7,-2],[17,8,-2],[21,10,-3],[24,11,-4],[29,13,-4],[34,16,-5],[41,18,-6]],175:[[3,1,-3],[4,1,-5],[5,1,-6],[6,1,-6],[6,1,-7],[8,1,-10],[9,1,-11],[10,2,-13],[12,3,-15],[15,2,-18],[17,2,-22],[20,3,-26],[24,3,-30],[29,4,-36]],176:[[3,2,-3],[3,2,-4],[4,2,-6],[5,2,-6],[5,3,-6],[6,3,-9],[7,4,-10],[9,5,-13],[10,6,-15],[12,7,-17],[14,8,-22],[17,8,-25],[20,10,-30],[24,12,-36]],177:[[5,5,0],[6,6,0],[8,8,0],[9,8,0],[10,9,0],[12,12,0],[15,14,0],[17,17,0],[20,20,0],[24,23,0],[29,27,0],[34,31,0],[40,37,0],[48,45,0]],180:[[3,2,-3],[4,2,-4],[4,3,-5],[5,3,-5],[6,3,-6],[7,4,-8],[8,5,-9],[10,6,-12],[11,7,-14],[13,7,-17],[16,9,-19],[19,10,-23],[22,12,-28],[26,13,-34]],215:[[5,4,0],[6,5,0],[7,6,0],[8,6,0],[9,7,0],[11,9,0],[13,10,0],[15,12,0],[18,14,0],[21,17,0],[25,20,0],[30,23,0],[36,27,-1],[42,33,-1]],247:[[5,4,0],[6,6,0],[8,7,0],[9,7,0],[10,8,0],[12,10,0],[15,12,0],[17,15,1],[20,18,1],[24,19,1],[29,23,1],[34,27,2],[40,32,2],[48,39,2]]}});MathJax.Ajax.loadComplete(MathJax.OutputJax["HTML-CSS"].imgDir+"/Main/Regular"+MathJax.OutputJax["HTML-CSS"].imgPacked+"/Latin1Supplement.js");

