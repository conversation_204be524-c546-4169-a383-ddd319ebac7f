/*
 *  /MathJax/fonts/HTML-CSS/TeX/png/Main/Regular/CombDiactForSymbols.js
 *  
 *  Copyright (c) 2010-2013 The MathJax Consortium
 *
 *  Part of the MathJax library.
 *  See http://www.mathjax.org for details.
 * 
 *  Licensed under the Apache License, Version 2.0;
 *  you may not use this file except in compliance with the License.
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 */

MathJax.OutputJax["HTML-CSS"].defineImageData({MathJax_Main:{8407:[[4,2,-3],[4,2,-4],[5,3,-5],[6,3,-6],[7,3,-7],[8,4,-8],[10,5,-9],[11,5,-12],[14,6,-14],[16,7,-17],[18,9,-20],[21,10,-24],[26,12,-28],[30,14,-33]]}});MathJax.Ajax.loadComplete(MathJax.OutputJax["HTML-CSS"].imgDir+"/Main/Regular"+MathJax.OutputJax["HTML-CSS"].imgPacked+"/CombDiactForSymbols.js");

