/*
 *  /MathJax/jax/output/CommonHTML/fonts/TeX/Caligraphic-Bold.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

(function(b){var a="MathJax_Caligraphic-Bold";b.FONTDATA.FONTS[a]={className:b.FONTDATA.familyName(a),centerline:315,ascent:840,descent:211,weight:"bold",skew:{65:0.224,66:0.16,67:0.16,68:0.0958,69:0.128,70:0.128,71:0.128,72:0.128,73:0.0319,74:0.192,75:0.0639,76:0.16,77:0.16,78:0.0958,79:0.128,80:0.0958,81:0.128,82:0.0958,83:0.16,84:0.0319,85:0.0958,86:0.0319,87:0.0958,88:0.16,89:0.0958,90:0.16},32:[0,0,250,0,0],48:[460,17,575,46,528],49:[461,0,575,80,494],50:[460,0,575,51,517],51:[461,211,575,48,525],52:[469,194,575,32,542],53:[461,211,575,57,517],54:[660,17,575,48,526],55:[476,211,575,64,558],56:[661,17,575,48,526],57:[461,210,575,48,526],65:[751,49,921,39,989],66:[705,17,748,40,740],67:[703,20,613,20,599],68:[686,0,892,20,885],69:[703,16,607,37,627],70:[686,30,814,17,930],71:[703,113,682,50,671],72:[686,48,987,20,946],73:[686,0,642,-27,746],74:[686,114,779,53,937],75:[703,17,871,40,834],76:[703,17,788,41,751],77:[703,49,1378,38,1353],78:[840,49,937,-24,1105],79:[703,17,906,63,882],80:[686,67,810,20,846],81:[703,146,939,120,905],82:[686,17,990,20,981],83:[703,16,696,25,721],84:[720,69,644,38,947],85:[686,24,715,-10,771],86:[686,77,737,25,774],87:[686,77,1169,25,1206],88:[686,-1,817,56,906],89:[686,164,759,36,797],90:[686,0,818,46,853],160:[0,0,250,0,0]};b.fontLoaded("TeX/"+a.substr(8))})(MathJax.OutputJax.CommonHTML);
