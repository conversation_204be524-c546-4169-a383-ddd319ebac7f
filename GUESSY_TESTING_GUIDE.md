# Guessy - Complete Testing and Verification Guide

## 🎯 Pre-Testing Setup Verification

### ✅ Environment Checklist
- [ ] XAMPP installed and running (Apache + MySQL)
- [ ] Guessy admin panel copied to `htdocs/guessy_admin`
- [ ] Database `guessy_db` created and populated
- [ ] Flutter app configured with Guessy branding
- [ ] Firebase project `guessy-quiz-app` created

### ✅ File Structure Verification
```
Project Root/
├── guessy_setup.bat (Windows)
├── guessy_setup.sh (macOS/Linux)
├── guessy_admin_config.php
├── guessy_database_setup.sql
├── update_flutter_package.dart
├── adminpanel2.3.4/ (source)
├── elite_quiz_app-2.3.4/ (Flutter app)
└── Documentation files
```

## 🚀 Quick Setup Test

### Option 1: Automated Setup
```bash
# Windows (run as Administrator)
guessy_setup.bat

# macOS/Linux
chmod +x guessy_setup.sh
sudo ./guessy_setup.sh
```

### Option 2: Manual Verification
Follow each step in `GUESSY_COMPLETE_SETUP.md`

## 🔍 Testing Sequence

### Phase 1: Admin Panel Testing

#### 1.1 Access Guessy Admin Panel
```bash
# Open in browser
http://localhost/guessy_admin
```

**Expected Result:** Guessy-branded login page loads

#### 1.2 Admin Login Test
- **Username:** `guessy_admin` or `admin`
- **Password:** `guessy123` or `admin123`

**Expected Result:** Dashboard loads with Guessy branding

#### 1.3 Database Connection Test
1. Navigate to **Categories** section
2. Verify Guessy categories are visible:
   - General Knowledge
   - Word Puzzles
   - Math Challenges
   - Science & Technology
   - Sports & Games
   - Entertainment

**Expected Result:** All Guessy categories display correctly

#### 1.4 API Endpoints Test
```bash
# Test Guessy categories API
curl -X POST http://localhost/guessy_admin/Api/get_categories \
  -H "Content-Type: application/json" \
  -d '{"type": 1}'

# Test system configuration
curl -X POST http://localhost/guessy_admin/Api/get_system_configurations \
  -H "Content-Type: application/json" \
  -d '{}'
```

**Expected Result:** JSON responses with Guessy data

### Phase 2: Flutter App Testing

#### 2.1 Launch Guessy Flutter App
```bash
cd elite_quiz_app-2.3.4
flutter run
```

**Expected Result:** App launches with "Guessy" branding

#### 2.2 App Configuration Test
1. Check app name displays as "Guessy"
2. Verify package name is `com.guessy.quiz`
3. Check splash screen shows Guessy branding

**Expected Result:** All branding elements show "Guessy"

#### 2.3 API Connectivity Test
1. Navigate to Quiz Zone
2. Check if Guessy categories load
3. Verify categories match admin panel

**Expected Result:** Categories load from local Guessy server

#### 2.4 User Registration Test
1. Go to Sign Up screen
2. Register with email and password
3. Complete profile setup

**Expected Result:** 
- User created in Firebase Auth
- User appears in `guessy_db.tbl_users`

### Phase 3: Guessy Features Testing

#### 3.1 Quiz Categories Test
Test each Guessy category:
1. **General Knowledge** - Basic trivia questions
2. **Word Puzzles** - Language and vocabulary
3. **Math Challenges** - Mathematical problems
4. **Science & Technology** - Science facts
5. **Sports & Games** - Sports trivia
6. **Entertainment** - Entertainment questions

**Expected Result:** Each category loads appropriate questions

#### 3.2 Quiz Gameplay Test
1. Select "General Knowledge" category
2. Start a quiz
3. Answer questions
4. Complete quiz
5. Check results and coin rewards

**Expected Result:** 
- Questions display correctly
- Score calculated properly
- Coins awarded and updated in database

#### 3.3 Coin System Test
1. Check initial coin balance (should be 100)
2. Play quiz and earn coins
3. Verify coin balance updates
4. Check coin transaction in database

**Expected Result:** Coin system works correctly

### Phase 4: Advanced Features Testing

#### 4.1 Battle System Test (if Firebase configured)
1. Create a battle room
2. Check if room appears in Firestore
3. Test real-time updates

**Expected Result:** Battle room created in `guessy_battles` collection

#### 4.2 Bookmark System Test
1. Bookmark a question during quiz
2. Check bookmarks section
3. Verify bookmark in `tbl_bookmark`

**Expected Result:** Bookmark saves correctly

#### 4.3 Leaderboard Test
1. Complete multiple quizzes
2. Check daily leaderboard
3. Verify user ranking

**Expected Result:** User appears in leaderboard

## 🎨 Guessy Branding Verification

### Visual Elements
- [ ] App name shows "Guessy"
- [ ] Admin panel title shows "Guessy"
- [ ] Package name is `com.guessy.quiz`
- [ ] Database name is `guessy_db`
- [ ] API endpoints use `/guessy_admin/Api/`

### Content Verification
- [ ] Categories are Guessy-themed
- [ ] Questions are appropriate for each category
- [ ] Badge descriptions mention "Guessy"
- [ ] Settings reflect Guessy branding

## 🔧 Network Configuration Testing

### Android Emulator
```dart
// Should use in config.dart
const panelUrl = 'http://********/guessy_admin';
```

### iOS Simulator
```dart
// Should use in config.dart
const panelUrl = 'http://localhost/guessy_admin';
```

### Physical Device
```dart
// Should use your computer's IP
const panelUrl = 'http://*************/guessy_admin';
```

## 🆘 Troubleshooting Common Issues

### Issue 1: Guessy Admin Panel Not Loading
**Symptoms:** Browser shows "This site can't be reached"

**Solutions:**
1. Check Apache service in XAMPP
2. Verify URL: `http://localhost/guessy_admin`
3. Check if files copied to correct location

### Issue 2: Database Connection Failed
**Symptoms:** Admin panel shows database error

**Solutions:**
1. Check MySQL service in XAMPP
2. Verify database name is `guessy_db`
3. Check credentials in `application/config/database.php`

### Issue 3: Flutter App Can't Connect
**Symptoms:** Categories not loading, API errors

**Solutions:**
1. Check network configuration for your device type
2. Verify admin panel API is accessible
3. Check network security config allows HTTP

### Issue 4: Package Name Issues
**Symptoms:** Firebase auth fails, build errors

**Solutions:**
1. Run `dart update_flutter_package.dart`
2. Update Firebase project with new package
3. Download new `google-services.json`

### Issue 5: Guessy Branding Not Applied
**Symptoms:** Still shows "Elite Quiz" branding

**Solutions:**
1. Run `php guessy_admin_config.php`
2. Import `guessy_database_setup.sql`
3. Check Flutter app configuration

## 📊 Performance Testing

### Load Testing
1. Create multiple user accounts
2. Play multiple quizzes simultaneously
3. Monitor server performance

### Memory Testing
1. Play extended quiz sessions
2. Monitor app memory usage
3. Check for memory leaks

### Database Performance
```sql
-- Check Guessy database performance
SELECT COUNT(*) FROM tbl_users;
SELECT COUNT(*) FROM tbl_question;
SELECT COUNT(*) FROM tbl_category;

-- Check recent activity
SELECT * FROM tbl_users ORDER BY date_registered DESC LIMIT 10;
```

## ✅ Success Criteria

### Complete Guessy Setup Success

Your Guessy setup is successful when:

1. **Admin Panel:**
   - [ ] Loads at `http://localhost/guessy_admin`
   - [ ] Login works with Guessy credentials
   - [ ] Shows Guessy categories and questions
   - [ ] API endpoints respond with Guessy data

2. **Flutter App:**
   - [ ] Shows "Guessy" as app name
   - [ ] Package name is `com.guessy.quiz`
   - [ ] Connects to Guessy local API
   - [ ] Categories load from Guessy server

3. **Database:**
   - [ ] `guessy_db` created successfully
   - [ ] Guessy-specific data loaded
   - [ ] All tables populated correctly

4. **Firebase:**
   - [ ] Project `guessy-quiz-app` configured
   - [ ] Authentication working
   - [ ] Firestore accessible for battles

## 🎉 Final Verification Commands

### Quick Test Commands
```bash
# Test admin panel
curl -I http://localhost/guessy_admin

# Test API
curl -X POST http://localhost/guessy_admin/Api/get_categories \
  -H "Content-Type: application/json" \
  -d '{"type": 1}'

# Test Flutter app
cd elite_quiz_app-2.3.4
flutter doctor
flutter run --debug
```

### Database Verification
```sql
-- Connect to Guessy database
USE guessy_db;

-- Check Guessy settings
SELECT * FROM tbl_settings WHERE type LIKE '%guessy%';

-- Check categories
SELECT id, category_name FROM tbl_category;

-- Check sample questions
SELECT id, question, category FROM tbl_question LIMIT 5;
```

## 🚀 Next Steps After Successful Testing

1. **Customize Content:**
   - Add more Guessy-specific questions
   - Upload custom category images
   - Configure app settings

2. **Enhance Branding:**
   - Update app icons and splash screen
   - Customize color scheme
   - Add Guessy logo and graphics

3. **Production Preparation:**
   - Setup production server
   - Configure production Firebase
   - Update security settings

Your **Guessy** quiz application is now fully tested and ready for development! 🎯🎉
