{"name": "jspdf", "version": "2.5.1", "homepage": "https://github.com/mrrio/jspdf", "description": "PDF Document creation from JavaScript", "main": ["dist/jspdf.umd.js", "dist/jspdf.umd.min.js", "dist/jspdf.es.js", "dist/jspdf.es.min.js", "dist/jspdf.node.js", "dist/jspdf.node.min.js"], "moduleType": ["amd", "globals", "node", "es6"], "keywords": ["pdf"], "dependencies": {"fflate": "101arrowz/fflate#^0.4.8"}, "optionalDependencies": {"canvg": "^3.0.6", "core-js": "^3.6.0", "dompurify": "^2.0.12", "html2canvas": "^1.0.0-rc.5"}, "devDependencies": {"@babel/core": "^7.8.4", "@babel/plugin-external-helpers": "7.2.0", "@babel/preset-env": "^7.8.4", "@rollup/plugin-replace": "^2.3.3", "@types/jasmine": "^3.5.11", "@types/node": "^14.0.18", "@typescript-eslint/parser": "^3.6.0", "@typescript-eslint/eslint-plugin": "^3.6.0", "chalk": "^4.1.0", "codeclimate-test-reporter": "0.5.1", "core-js": "^3.6.5", "diff": "4.0.1", "docdash": "1.1.0", "eslint": "^7.4.0", "eslint-plugin-jasmine": "^4.1.1", "folder-delete": "1.0.4", "inquirer": "^6.5.2", "jasmine": "3.5.0", "jasmine-core": "3.5.0", "jasmine-expect": "4.0.3", "js-yaml": "3.13.1", "jsdoc": "^3.6.3", "karma": "5.1.0", "karma-babel-preprocessor": "8.0.1", "karma-chrome-launcher": "3.1.0", "karma-coverage": "2.0.2", "karma-firefox-launcher": "1.3.0", "karma-ie-launcher": "1.0.0", "karma-jasmine": "3.3.1", "karma-jasmine-matchers": "4.0.2", "karma-mocha-reporter": "2.2.5", "karma-sauce-launcher": "4.1.5", "karma-typescript": "^5.0.3", "karma-verbose-reporter": "0.0.6", "local-web-server": "2.6.1", "log-utils": "^1.0.0", "markdown": "0.5.0", "preprocess": "^3.2.0", "prettier": "^1.19.1", "regenerator-runtime": "^0.13.5", "requirejs": "^2.3.6", "rollup": "^2.18.2", "rollup-plugin-license": "^2.1.0", "rollup-plugin-node-resolve": "4.2.3", "rollup-plugin-preprocess": "0.0.4", "rollup-plugin-terser": "^6.1.0", "typescript": "^3.9.6"}, "license": "MIT", "ignore": ["**/.*", "src", "CNAME", "examples", "node_modules", "bower_components", "test", "coverage", "docs"]}