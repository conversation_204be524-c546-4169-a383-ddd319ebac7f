[{"locale": "en"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical bold capital a", "alternative": "bold capital a", "short": "bold cap a"}, "mathspeak": {"default": "bold upper A"}}, "key": "1D400"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical bold capital b", "alternative": "bold capital b", "short": "bold cap b"}, "mathspeak": {"default": "bold upper B"}}, "key": "1D401"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical bold capital c", "alternative": "bold capital c", "short": "bold cap c"}, "mathspeak": {"default": "bold upper C"}}, "key": "1D402"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical bold capital d", "alternative": "bold capital d", "short": "bold cap d"}, "mathspeak": {"default": "bold upper D"}}, "key": "1D403"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical bold capital e", "alternative": "bold capital e", "short": "bold cap e"}, "mathspeak": {"default": "bold upper E"}}, "key": "1D404"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical bold capital f", "alternative": "bold capital f", "short": "bold cap f"}, "mathspeak": {"default": "bold upper F"}}, "key": "1D405"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical bold capital g", "alternative": "bold capital g", "short": "bold cap g"}, "mathspeak": {"default": "bold upper G"}}, "key": "1D406"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical bold capital h", "alternative": "bold capital h", "short": "bold cap h"}, "mathspeak": {"default": "bold upper H"}}, "key": "1D407"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical bold capital i", "alternative": "bold capital i", "short": "bold cap i"}, "mathspeak": {"default": "bold upper I"}}, "key": "1D408"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical bold capital j", "alternative": "bold capital j", "short": "bold cap j"}, "mathspeak": {"default": "bold upper J"}}, "key": "1D409"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical bold capital k", "alternative": "bold capital k", "short": "bold cap k"}, "mathspeak": {"default": "bold upper K"}}, "key": "1D40A"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical bold capital l", "alternative": "bold capital l", "short": "bold cap l"}, "mathspeak": {"default": "bold upper L"}}, "key": "1D40B"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical bold capital m", "alternative": "bold capital m", "short": "bold cap m"}, "mathspeak": {"default": "bold upper M"}}, "key": "1D40C"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical bold capital n", "alternative": "bold capital n", "short": "bold cap n"}, "mathspeak": {"default": "bold upper N"}}, "key": "1D40D"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical bold capital o", "alternative": "bold capital o", "short": "bold cap o"}, "mathspeak": {"default": "bold upper O"}}, "key": "1D40E"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical bold capital p", "alternative": "bold capital p", "short": "bold cap p"}, "mathspeak": {"default": "bold upper P"}}, "key": "1D40F"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical bold capital q", "alternative": "bold capital q", "short": "bold cap q"}, "mathspeak": {"default": "bold upper Q"}}, "key": "1D410"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical bold capital r", "alternative": "bold capital r", "short": "bold cap r"}, "mathspeak": {"default": "bold upper R"}}, "key": "1D411"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical bold capital s", "alternative": "bold capital s", "short": "bold cap s"}, "mathspeak": {"default": "bold upper S"}}, "key": "1D412"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical bold capital t", "alternative": "bold capital t", "short": "bold cap t"}, "mathspeak": {"default": "bold upper T"}}, "key": "1D413"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical bold capital u", "alternative": "bold capital u", "short": "bold cap u"}, "mathspeak": {"default": "bold upper U"}}, "key": "1D414"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical bold capital v", "alternative": "bold capital v", "short": "bold cap v"}, "mathspeak": {"default": "bold upper V"}}, "key": "1D415"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical bold capital w", "alternative": "bold capital w", "short": "bold cap w"}, "mathspeak": {"default": "bold upper W"}}, "key": "1D416"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical bold capital x", "alternative": "bold capital x", "short": "bold cap x"}, "mathspeak": {"default": "bold upper X"}}, "key": "1D417"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical bold capital y", "alternative": "bold capital y", "short": "bold cap y"}, "mathspeak": {"default": "bold upper Y"}}, "key": "1D418"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical bold capital z", "alternative": "bold capital z", "short": "bold cap z"}, "mathspeak": {"default": "bold upper Z"}}, "key": "1D419"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical bold small a", "alternative": "bold small a", "short": "bold a"}}, "key": "1D41A"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical bold small b", "alternative": "bold small b", "short": "bold b"}}, "key": "1D41B"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical bold small c", "alternative": "bold small c", "short": "bold c"}}, "key": "1D41C"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical bold small d", "alternative": "bold small d", "short": "bold d"}}, "key": "1D41D"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical bold small e", "alternative": "bold small e", "short": "bold e"}}, "key": "1D41E"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical bold small f", "alternative": "bold small f", "short": "bold f"}}, "key": "1D41F"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical bold small g", "alternative": "bold small g", "short": "bold g"}}, "key": "1D420"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical bold small h", "alternative": "bold small h", "short": "bold h"}}, "key": "1D421"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical bold small i", "alternative": "bold small i", "short": "bold i"}}, "key": "1D422"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical bold small j", "alternative": "bold small j", "short": "bold j"}}, "key": "1D423"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical bold small k", "alternative": "bold small k", "short": "bold k"}}, "key": "1D424"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical bold small l", "alternative": "bold small l", "short": "bold l"}}, "key": "1D425"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical bold small m", "alternative": "bold small m", "short": "bold m"}}, "key": "1D426"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical bold small n", "alternative": "bold small n", "short": "bold n"}}, "key": "1D427"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical bold small o", "alternative": "bold small o", "short": "bold o"}}, "key": "1D428"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical bold small p", "alternative": "bold small p", "short": "bold p"}}, "key": "1D429"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical bold small q", "alternative": "bold small q", "short": "bold q"}}, "key": "1D42A"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical bold small r", "alternative": "bold small r", "short": "bold r"}}, "key": "1D42B"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical bold small s", "alternative": "bold small s", "short": "bold s"}}, "key": "1D42C"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical bold small t", "alternative": "bold small t", "short": "bold t"}}, "key": "1D42D"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical bold small u", "alternative": "bold small u", "short": "bold u"}}, "key": "1D42E"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical bold small v", "alternative": "bold small v", "short": "bold v"}}, "key": "1D42F"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical bold small w", "alternative": "bold small w", "short": "bold w"}}, "key": "1D430"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical bold small x", "alternative": "bold small x", "short": "bold x"}}, "key": "1D431"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical bold small y", "alternative": "bold small y", "short": "bold y"}}, "key": "1D432"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical bold small z", "alternative": "bold small z", "short": "bold z"}}, "key": "1D433"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical italic capital a", "alternative": "italic capital a", "short": "italic cap a"}, "mathspeak": {"default": "italic upper A"}}, "key": "1D434"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical italic capital b", "alternative": "italic capital b", "short": "italic cap b"}, "mathspeak": {"default": "italic upper B"}}, "key": "1D435"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical italic capital c", "alternative": "italic capital c", "short": "italic cap c"}, "mathspeak": {"default": "italic upper C"}}, "key": "1D436"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical italic capital d", "alternative": "italic capital d", "short": "italic cap d"}, "mathspeak": {"default": "italic upper D"}}, "key": "1D437"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical italic capital e", "alternative": "italic capital e", "short": "italic cap e"}, "mathspeak": {"default": "italic upper E"}}, "key": "1D438"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical italic capital f", "alternative": "italic capital f", "short": "italic cap f"}, "mathspeak": {"default": "italic upper F"}}, "key": "1D439"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical italic capital g", "alternative": "italic capital g", "short": "italic cap g"}, "mathspeak": {"default": "italic upper G"}}, "key": "1D43A"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical italic capital h", "alternative": "italic capital h", "short": "italic cap h"}, "mathspeak": {"default": "italic upper H"}}, "key": "1D43B"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical italic capital i", "alternative": "italic capital i", "short": "italic cap i"}, "mathspeak": {"default": "italic upper I"}}, "key": "1D43C"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical italic capital j", "alternative": "italic capital j", "short": "italic cap j"}, "mathspeak": {"default": "italic upper J"}}, "key": "1D43D"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical italic capital k", "alternative": "italic capital k", "short": "italic cap k"}, "mathspeak": {"default": "italic upper K"}}, "key": "1D43E"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical italic capital l", "alternative": "italic capital l", "short": "italic cap l"}, "mathspeak": {"default": "italic upper L"}}, "key": "1D43F"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical italic capital m", "alternative": "italic capital m", "short": "italic cap m"}, "mathspeak": {"default": "italic upper M"}}, "key": "1D440"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical italic capital n", "alternative": "italic capital n", "short": "italic cap n"}, "mathspeak": {"default": "italic upper N"}}, "key": "1D441"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical italic capital o", "alternative": "italic capital o", "short": "italic cap o"}, "mathspeak": {"default": "italic upper O"}}, "key": "1D442"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical italic capital p", "alternative": "italic capital p", "short": "italic cap p"}, "mathspeak": {"default": "italic upper P"}}, "key": "1D443"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical italic capital q", "alternative": "italic capital q", "short": "italic cap q"}, "mathspeak": {"default": "italic upper Q"}}, "key": "1D444"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical italic capital r", "alternative": "italic capital r", "short": "italic cap r"}, "mathspeak": {"default": "italic upper R"}}, "key": "1D445"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical italic capital s", "alternative": "italic capital s", "short": "italic cap s"}, "mathspeak": {"default": "italic upper S"}}, "key": "1D446"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical italic capital t", "alternative": "italic capital t", "short": "italic cap t"}, "mathspeak": {"default": "italic upper T"}}, "key": "1D447"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical italic capital u", "alternative": "italic capital u", "short": "italic cap u"}, "mathspeak": {"default": "italic upper U"}}, "key": "1D448"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical italic capital v", "alternative": "italic capital v", "short": "italic cap v"}, "mathspeak": {"default": "italic upper V"}}, "key": "1D449"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical italic capital w", "alternative": "italic capital w", "short": "italic cap w"}, "mathspeak": {"default": "italic upper W"}}, "key": "1D44A"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical italic capital x", "alternative": "italic capital x", "short": "italic cap x"}, "mathspeak": {"default": "italic upper X"}}, "key": "1D44B"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical italic capital y", "alternative": "italic capital y", "short": "italic cap y"}, "mathspeak": {"default": "italic upper Y"}}, "key": "1D44C"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical italic capital z", "alternative": "italic capital z", "short": "italic cap z"}, "mathspeak": {"default": "italic upper Z"}}, "key": "1D44D"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical italic small a", "alternative": "italic small a", "short": "italic a"}}, "key": "1D44E"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical italic small b", "alternative": "italic small b", "short": "italic b"}}, "key": "1D44F"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical italic small c", "alternative": "italic small c", "short": "italic c"}}, "key": "1D450"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical italic small d", "alternative": "italic small d", "short": "italic d"}}, "key": "1D451"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical italic small e", "alternative": "italic small e", "short": "italic e"}}, "key": "1D452"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical italic small f", "alternative": "italic small f", "short": "italic f"}}, "key": "1D453"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical italic small g", "alternative": "italic small g", "short": "italic g"}}, "key": "1D454"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical italic small i", "alternative": "italic small i", "short": "italic i"}}, "key": "1D456"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical italic small j", "alternative": "italic small j", "short": "italic j"}}, "key": "1D457"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical italic small k", "alternative": "italic small k", "short": "italic k"}}, "key": "1D458"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical italic small l", "alternative": "italic small l", "short": "italic l"}}, "key": "1D459"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical italic small m", "alternative": "italic small m", "short": "italic m"}}, "key": "1D45A"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical italic small n", "alternative": "italic small n", "short": "italic n"}}, "key": "1D45B"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical italic small o", "alternative": "italic small o", "short": "italic o"}}, "key": "1D45C"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical italic small p", "alternative": "italic small p", "short": "italic p"}}, "key": "1D45D"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical italic small q", "alternative": "italic small q", "short": "italic q"}}, "key": "1D45E"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical italic small r", "alternative": "italic small r", "short": "italic r"}}, "key": "1D45F"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical italic small s", "alternative": "italic small s", "short": "italic s"}}, "key": "1D460"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical italic small t", "alternative": "italic small t", "short": "italic t"}}, "key": "1D461"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical italic small u", "alternative": "italic small u", "short": "italic u"}}, "key": "1D462"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical italic small v", "alternative": "italic small v", "short": "italic v"}}, "key": "1D463"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical italic small w", "alternative": "italic small w", "short": "italic w"}}, "key": "1D464"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical italic small x", "alternative": "italic small x", "short": "italic x"}}, "key": "1D465"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical italic small y", "alternative": "italic small y", "short": "italic y"}}, "key": "1D466"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical italic small z", "alternative": "italic small z", "short": "italic z"}}, "key": "1D467"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical script capital a", "alternative": "script capital a", "short": "script cap a"}, "mathspeak": {"default": "script upper A"}}, "key": "1D49C"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical script capital c", "alternative": "script capital c", "short": "script cap c"}, "mathspeak": {"default": "script upper C"}}, "key": "1D49E"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical script capital d", "alternative": "script capital d", "short": "script cap d"}, "mathspeak": {"default": "script upper D"}}, "key": "1D49F"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical script capital g", "alternative": "script capital g", "short": "script cap g"}, "mathspeak": {"default": "script upper G"}}, "key": "1D4A2"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical script capital j", "alternative": "script capital j", "short": "script cap j"}, "mathspeak": {"default": "script upper J"}}, "key": "1D4A5"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical script capital k", "alternative": "script capital k", "short": "script cap k"}, "mathspeak": {"default": "script upper K"}}, "key": "1D4A6"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical script capital n", "alternative": "script capital n", "short": "script cap n"}, "mathspeak": {"default": "script upper N"}}, "key": "1D4A9"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical script capital o", "alternative": "script capital o", "short": "script cap o"}, "mathspeak": {"default": "script upper O"}}, "key": "1D4AA"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical script capital p", "alternative": "script capital p", "short": "script cap p"}, "mathspeak": {"default": "script upper P"}}, "key": "1D4AB"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical script capital q", "alternative": "script capital q", "short": "script cap q"}, "mathspeak": {"default": "script upper Q"}}, "key": "1D4AC"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical script capital s", "alternative": "script capital s", "short": "script cap s"}, "mathspeak": {"default": "script upper S"}}, "key": "1D4AE"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical script capital t", "alternative": "script capital t", "short": "script cap t"}, "mathspeak": {"default": "script upper T"}}, "key": "1D4AF"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical script capital u", "alternative": "script capital u", "short": "script cap u"}, "mathspeak": {"default": "script upper U"}}, "key": "1D4B0"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical script capital v", "alternative": "script capital v", "short": "script cap v"}, "mathspeak": {"default": "script upper V"}}, "key": "1D4B1"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical script capital w", "alternative": "script capital w", "short": "script cap w"}, "mathspeak": {"default": "script upper W"}}, "key": "1D4B2"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical script capital x", "alternative": "script capital x", "short": "script cap x"}, "mathspeak": {"default": "script upper X"}}, "key": "1D4B3"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical script capital y", "alternative": "script capital y", "short": "script cap y"}, "mathspeak": {"default": "script upper Y"}}, "key": "1D4B4"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical script capital z", "alternative": "script capital z", "short": "script cap z"}, "mathspeak": {"default": "script upper Z"}}, "key": "1D4B5"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical script small a", "alternative": "script small a", "short": "script a"}}, "key": "1D4B6"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical script small b", "alternative": "script small b", "short": "script b"}}, "key": "1D4B7"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical script small c", "alternative": "script small c", "short": "script c"}}, "key": "1D4B8"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical script small d", "alternative": "script small d", "short": "script d"}}, "key": "1D4B9"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical script small f", "alternative": "script small f", "short": "script f"}}, "key": "1D4BB"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical script small h", "alternative": "script small h", "short": "script h"}}, "key": "1D4BD"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical script small i", "alternative": "script small i", "short": "script i"}}, "key": "1D4BE"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical script small j", "alternative": "script small j", "short": "script j"}}, "key": "1D4BF"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical script small k", "alternative": "script small k", "short": "script k"}}, "key": "1D4C0"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical script small l", "alternative": "script small l", "short": "script l"}}, "key": "1D4C1"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical script small m", "alternative": "script small m", "short": "script m"}}, "key": "1D4C2"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical script small n", "alternative": "script small n", "short": "script n"}}, "key": "1D4C3"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical script small p", "alternative": "script small p", "short": "script p"}}, "key": "1D4C5"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical script small q", "alternative": "script small q", "short": "script q"}}, "key": "1D4C6"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical script small r", "alternative": "script small r", "short": "script r"}}, "key": "1D4C7"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical script small s", "alternative": "script small s", "short": "script s"}}, "key": "1D4C8"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical script small t", "alternative": "script small t", "short": "script t"}}, "key": "1D4C9"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical script small u", "alternative": "script small u", "short": "script u"}}, "key": "1D4CA"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical script small v", "alternative": "script small v", "short": "script v"}}, "key": "1D4CB"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical script small w", "alternative": "script small w", "short": "script w"}}, "key": "1D4CC"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical script small x", "alternative": "script small x", "short": "script x"}}, "key": "1D4CD"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical script small y", "alternative": "script small y", "short": "script y"}}, "key": "1D4CE"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical script small z", "alternative": "script small z", "short": "script z"}}, "key": "1D4CF"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical bold script capital a", "alternative": "bold script capital a", "short": "bold script cap a"}, "mathspeak": {"default": "bold script upper A"}}, "key": "1D4D0"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical bold script capital b", "alternative": "bold script capital b", "short": "bold script cap b"}, "mathspeak": {"default": "bold script upper B"}}, "key": "1D4D1"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical bold script capital c", "alternative": "bold script capital c", "short": "bold script cap c"}, "mathspeak": {"default": "bold script upper C"}}, "key": "1D4D2"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical bold script capital d", "alternative": "bold script capital d", "short": "bold script cap d"}, "mathspeak": {"default": "bold script upper D"}}, "key": "1D4D3"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical bold script capital e", "alternative": "bold script capital e", "short": "bold script cap e"}, "mathspeak": {"default": "bold script upper E"}}, "key": "1D4D4"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical bold script capital f", "alternative": "bold script capital f", "short": "bold script cap f"}, "mathspeak": {"default": "bold script upper F"}}, "key": "1D4D5"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical bold script capital g", "alternative": "bold script capital g", "short": "bold script cap g"}, "mathspeak": {"default": "bold script upper G"}}, "key": "1D4D6"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical bold script capital h", "alternative": "bold script capital h", "short": "bold script cap h"}, "mathspeak": {"default": "bold script upper H"}}, "key": "1D4D7"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical bold script capital i", "alternative": "bold script capital i", "short": "bold script cap i"}, "mathspeak": {"default": "bold script upper I"}}, "key": "1D4D8"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical bold script capital j", "alternative": "bold script capital j", "short": "bold script cap j"}, "mathspeak": {"default": "bold script upper J"}}, "key": "1D4D9"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical bold script capital k", "alternative": "bold script capital k", "short": "bold script cap k"}, "mathspeak": {"default": "bold script upper K"}}, "key": "1D4DA"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical bold script capital l", "alternative": "bold script capital l", "short": "bold script cap l"}, "mathspeak": {"default": "bold script upper L"}}, "key": "1D4DB"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical bold script capital m", "alternative": "bold script capital m", "short": "bold script cap m"}, "mathspeak": {"default": "bold script upper M"}}, "key": "1D4DC"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical bold script capital n", "alternative": "bold script capital n", "short": "bold script cap n"}, "mathspeak": {"default": "bold script upper N"}}, "key": "1D4DD"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical bold script capital o", "alternative": "bold script capital o", "short": "bold script cap o"}, "mathspeak": {"default": "bold script upper O"}}, "key": "1D4DE"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical bold script capital p", "alternative": "bold script capital p", "short": "bold script cap p"}, "mathspeak": {"default": "bold script upper P"}}, "key": "1D4DF"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical bold script capital q", "alternative": "bold script capital q", "short": "bold script cap q"}, "mathspeak": {"default": "bold script upper Q"}}, "key": "1D4E0"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical bold script capital r", "alternative": "bold script capital r", "short": "bold script cap r"}, "mathspeak": {"default": "bold script upper R"}}, "key": "1D4E1"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical bold script capital s", "alternative": "bold script capital s", "short": "bold script cap s"}, "mathspeak": {"default": "bold script upper S"}}, "key": "1D4E2"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical bold script capital t", "alternative": "bold script capital t", "short": "bold script cap t"}, "mathspeak": {"default": "bold script upper T"}}, "key": "1D4E3"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical bold script capital u", "alternative": "bold script capital u", "short": "bold script cap u"}, "mathspeak": {"default": "bold script upper U"}}, "key": "1D4E4"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical bold script capital v", "alternative": "bold script capital v", "short": "bold script cap v"}, "mathspeak": {"default": "bold script upper V"}}, "key": "1D4E5"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical bold script capital w", "alternative": "bold script capital w", "short": "bold script cap w"}, "mathspeak": {"default": "bold script upper W"}}, "key": "1D4E6"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical bold script capital x", "alternative": "bold script capital x", "short": "bold script cap x"}, "mathspeak": {"default": "bold script upper X"}}, "key": "1D4E7"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical bold script capital y", "alternative": "bold script capital y", "short": "bold script cap y"}, "mathspeak": {"default": "bold script upper Y"}}, "key": "1D4E8"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical bold script capital z", "alternative": "bold script capital z", "short": "bold script cap z"}, "mathspeak": {"default": "bold script upper Z"}}, "key": "1D4E9"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical bold script small a", "alternative": "bold script small a", "short": "bold script a"}}, "key": "1D4EA"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical bold script small b", "alternative": "bold script small b", "short": "bold script b"}}, "key": "1D4EB"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical bold script small c", "alternative": "bold script small c", "short": "bold script c"}}, "key": "1D4EC"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical bold script small d", "alternative": "bold script small d", "short": "bold script d"}}, "key": "1D4ED"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical bold script small e", "alternative": "bold script small e", "short": "bold script e"}}, "key": "1D4EE"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical bold script small f", "alternative": "bold script small f", "short": "bold script f"}}, "key": "1D4EF"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical bold script small g", "alternative": "bold script small g", "short": "bold script g"}}, "key": "1D4F0"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical bold script small h", "alternative": "bold script small h", "short": "bold script h"}}, "key": "1D4F1"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical bold script small i", "alternative": "bold script small i", "short": "bold script i"}}, "key": "1D4F2"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical bold script small j", "alternative": "bold script small j", "short": "bold script j"}}, "key": "1D4F3"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical bold script small k", "alternative": "bold script small k", "short": "bold script k"}}, "key": "1D4F4"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical bold script small l", "alternative": "bold script small l", "short": "bold script l"}}, "key": "1D4F5"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical bold script small m", "alternative": "bold script small m", "short": "bold script m"}}, "key": "1D4F6"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical bold script small n", "alternative": "bold script small n", "short": "bold script n"}}, "key": "1D4F7"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical bold script small o", "alternative": "bold script small o", "short": "bold script o"}}, "key": "1D4F8"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical bold script small p", "alternative": "bold script small p", "short": "bold script p"}}, "key": "1D4F9"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical bold script small q", "alternative": "bold script small q", "short": "bold script q"}}, "key": "1D4FA"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical bold script small r", "alternative": "bold script small r", "short": "bold script r"}}, "key": "1D4FB"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical bold script small s", "alternative": "bold script small s", "short": "bold script s"}}, "key": "1D4FC"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical bold script small t", "alternative": "bold script small t", "short": "bold script t"}}, "key": "1D4FD"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical bold script small u", "alternative": "bold script small u", "short": "bold script u"}}, "key": "1D4FE"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical bold script small v", "alternative": "bold script small v", "short": "bold script v"}}, "key": "1D4FF"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical bold script small w", "alternative": "bold script small w", "short": "bold script w"}}, "key": "1D500"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical bold script small x", "alternative": "bold script small x", "short": "bold script x"}}, "key": "1D501"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical bold script small y", "alternative": "bold script small y", "short": "bold script y"}}, "key": "1D502"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical bold script small z", "alternative": "bold script small z", "short": "bold script z"}}, "key": "1D503"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical fraktur capital a", "alternative": "fraktur capital a", "short": "fraktur cap a"}, "mathspeak": {"default": "fraktur upper A"}}, "key": "1D504"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical fraktur capital b", "alternative": "fraktur capital b", "short": "fraktur cap b"}, "mathspeak": {"default": "fraktur upper B"}}, "key": "1D505"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical fraktur capital d", "alternative": "fraktur capital d", "short": "fraktur cap d"}, "mathspeak": {"default": "fraktur upper D"}}, "key": "1D507"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical fraktur capital e", "alternative": "fraktur capital e", "short": "fraktur cap e"}, "mathspeak": {"default": "fraktur upper E"}}, "key": "1D508"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical fraktur capital f", "alternative": "fraktur capital f", "short": "fraktur cap f"}, "mathspeak": {"default": "fraktur upper F"}}, "key": "1D509"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical fraktur capital g", "alternative": "fraktur capital g", "short": "fraktur cap g"}, "mathspeak": {"default": "fraktur upper G"}}, "key": "1D50A"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical fraktur capital j", "alternative": "fraktur capital j", "short": "fraktur cap j"}, "mathspeak": {"default": "fraktur upper J"}}, "key": "1D50D"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical fraktur capital k", "alternative": "fraktur capital k", "short": "fraktur cap k"}, "mathspeak": {"default": "fraktur upper K"}}, "key": "1D50E"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical fraktur capital l", "alternative": "fraktur capital l", "short": "fraktur cap l"}, "mathspeak": {"default": "fraktur upper L"}}, "key": "1D50F"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical fraktur capital m", "alternative": "fraktur capital m", "short": "fraktur cap m"}, "mathspeak": {"default": "fraktur upper M"}}, "key": "1D510"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical fraktur capital n", "alternative": "fraktur capital n", "short": "fraktur cap n"}, "mathspeak": {"default": "fraktur upper N"}}, "key": "1D511"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical fraktur capital o", "alternative": "fraktur capital o", "short": "fraktur cap o"}, "mathspeak": {"default": "fraktur upper O"}}, "key": "1D512"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical fraktur capital p", "alternative": "fraktur capital p", "short": "fraktur cap p"}, "mathspeak": {"default": "fraktur upper P"}}, "key": "1D513"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical fraktur capital q", "alternative": "fraktur capital q", "short": "fraktur cap q"}, "mathspeak": {"default": "fraktur upper Q"}}, "key": "1D514"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical fraktur capital s", "alternative": "fraktur capital s", "short": "fraktur cap s"}, "mathspeak": {"default": "fraktur upper S"}}, "key": "1D516"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical fraktur capital t", "alternative": "fraktur capital t", "short": "fraktur cap t"}, "mathspeak": {"default": "fraktur upper T"}}, "key": "1D517"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical fraktur capital u", "alternative": "fraktur capital u", "short": "fraktur cap u"}, "mathspeak": {"default": "fraktur upper U"}}, "key": "1D518"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical fraktur capital v", "alternative": "fraktur capital v", "short": "fraktur cap v"}, "mathspeak": {"default": "fraktur upper V"}}, "key": "1D519"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical fraktur capital w", "alternative": "fraktur capital w", "short": "fraktur cap w"}, "mathspeak": {"default": "fraktur upper W"}}, "key": "1D51A"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical fraktur capital x", "alternative": "fraktur capital x", "short": "fraktur cap x"}, "mathspeak": {"default": "fraktur upper X"}}, "key": "1D51B"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical fraktur capital y", "alternative": "fraktur capital y", "short": "fraktur cap y"}, "mathspeak": {"default": "fraktur upper Y"}}, "key": "1D51C"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical fraktur small a", "alternative": "fraktur small a", "short": "fraktur a"}}, "key": "1D51E"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical fraktur small b", "alternative": "fraktur small b", "short": "fraktur b"}}, "key": "1D51F"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical fraktur small c", "alternative": "fraktur small c", "short": "fraktur c"}}, "key": "1D520"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical fraktur small d", "alternative": "fraktur small d", "short": "fraktur d"}}, "key": "1D521"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical fraktur small e", "alternative": "fraktur small e", "short": "fraktur e"}}, "key": "1D522"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical fraktur small f", "alternative": "fraktur small f", "short": "fraktur f"}}, "key": "1D523"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical fraktur small g", "alternative": "fraktur small g", "short": "fraktur g"}}, "key": "1D524"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical fraktur small h", "alternative": "fraktur small h", "short": "fraktur h"}}, "key": "1D525"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical fraktur small i", "alternative": "fraktur small i", "short": "fraktur i"}}, "key": "1D526"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical fraktur small j", "alternative": "fraktur small j", "short": "fraktur j"}}, "key": "1D527"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical fraktur small k", "alternative": "fraktur small k", "short": "fraktur k"}}, "key": "1D528"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical fraktur small l", "alternative": "fraktur small l", "short": "fraktur l"}}, "key": "1D529"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical fraktur small m", "alternative": "fraktur small m", "short": "fraktur m"}}, "key": "1D52A"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical fraktur small n", "alternative": "fraktur small n", "short": "fraktur n"}}, "key": "1D52B"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical fraktur small o", "alternative": "fraktur small o", "short": "fraktur o"}}, "key": "1D52C"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical fraktur small p", "alternative": "fraktur small p", "short": "fraktur p"}}, "key": "1D52D"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical fraktur small q", "alternative": "fraktur small q", "short": "fraktur q"}}, "key": "1D52E"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical fraktur small r", "alternative": "fraktur small r", "short": "fraktur r"}}, "key": "1D52F"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical fraktur small s", "alternative": "fraktur small s", "short": "fraktur s"}}, "key": "1D530"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical fraktur small t", "alternative": "fraktur small t", "short": "fraktur t"}}, "key": "1D531"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical fraktur small u", "alternative": "fraktur small u", "short": "fraktur u"}}, "key": "1D532"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical fraktur small v", "alternative": "fraktur small v", "short": "fraktur v"}}, "key": "1D533"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical fraktur small w", "alternative": "fraktur small w", "short": "fraktur w"}}, "key": "1D534"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical fraktur small x", "alternative": "fraktur small x", "short": "fraktur x"}}, "key": "1D535"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical fraktur small y", "alternative": "fraktur small y", "short": "fraktur y"}}, "key": "1D536"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical fraktur small z", "alternative": "fraktur small z", "short": "fraktur z"}}, "key": "1D537"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical double struck capital a", "alternative": "double struck capital a", "short": "double struck cap a"}, "mathspeak": {"default": "double struck upper A"}}, "key": "1D538"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical double struck capital b", "alternative": "double struck capital b", "short": "double struck cap b"}, "mathspeak": {"default": "double struck upper B"}}, "key": "1D539"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical double struck capital d", "alternative": "double struck capital d", "short": "double struck cap d"}, "mathspeak": {"default": "double struck upper D"}}, "key": "1D53B"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical double struck capital e", "alternative": "double struck capital e", "short": "double struck cap e"}, "mathspeak": {"default": "double struck upper E"}}, "key": "1D53C"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical double struck capital f", "alternative": "double struck capital f", "short": "double struck cap f"}, "mathspeak": {"default": "double struck upper F"}}, "key": "1D53D"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical double struck capital g", "alternative": "double struck capital g", "short": "double struck cap g"}, "mathspeak": {"default": "double struck upper G"}}, "key": "1D53E"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical double struck capital i", "alternative": "double struck capital i", "short": "double struck cap i"}, "mathspeak": {"default": "double struck upper I"}}, "key": "1D540"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical double struck capital j", "alternative": "double struck capital j", "short": "double struck cap j"}, "mathspeak": {"default": "double struck upper J"}}, "key": "1D541"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical double struck capital k", "alternative": "double struck capital k", "short": "double struck cap k"}, "mathspeak": {"default": "double struck upper K"}}, "key": "1D542"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical double struck capital l", "alternative": "double struck capital l", "short": "double struck cap l"}, "mathspeak": {"default": "double struck upper L"}}, "key": "1D543"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical double struck capital m", "alternative": "double struck capital m", "short": "double struck cap m"}, "mathspeak": {"default": "double struck upper M"}}, "key": "1D544"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical double struck capital o", "alternative": "double struck capital o", "short": "double struck cap o"}, "mathspeak": {"default": "double struck upper O"}}, "key": "1D546"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical double struck capital s", "alternative": "double struck capital s", "short": "double struck cap s"}, "mathspeak": {"default": "double struck upper S"}}, "key": "1D54A"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical double struck capital t", "alternative": "double struck capital t", "short": "double struck cap t"}, "mathspeak": {"default": "double struck upper T"}}, "key": "1D54B"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical double struck capital u", "alternative": "double struck capital u", "short": "double struck cap u"}, "mathspeak": {"default": "double struck upper U"}}, "key": "1D54C"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical double struck capital v", "alternative": "double struck capital v", "short": "double struck cap v"}, "mathspeak": {"default": "double struck upper V"}}, "key": "1D54D"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical double struck capital w", "alternative": "double struck capital w", "short": "double struck cap w"}, "mathspeak": {"default": "double struck upper W"}}, "key": "1D54E"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical double struck capital x", "alternative": "double struck capital x", "short": "double struck cap x"}, "mathspeak": {"default": "double struck upper X"}}, "key": "1D54F"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical double struck capital y", "alternative": "double struck capital y", "short": "double struck cap y"}, "mathspeak": {"default": "double struck upper Y"}}, "key": "1D550"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical double struck small a", "alternative": "double struck small a", "short": "double struck a"}}, "key": "1D552"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical double struck small b", "alternative": "double struck small b", "short": "double struck b"}}, "key": "1D553"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical double struck small c", "alternative": "double struck small c", "short": "double struck c"}}, "key": "1D554"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical double struck small d", "alternative": "double struck small d", "short": "double struck d"}}, "key": "1D555"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical double struck small e", "alternative": "double struck small e", "short": "double struck e"}}, "key": "1D556"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical double struck small f", "alternative": "double struck small f", "short": "double struck f"}}, "key": "1D557"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical double struck small g", "alternative": "double struck small g", "short": "double struck g"}}, "key": "1D558"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical double struck small h", "alternative": "double struck small h", "short": "double struck h"}}, "key": "1D559"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical double struck small i", "alternative": "double struck small i", "short": "double struck i"}}, "key": "1D55A"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical double struck small j", "alternative": "double struck small j", "short": "double struck j"}}, "key": "1D55B"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical double struck small k", "alternative": "double struck small k", "short": "double struck k"}}, "key": "1D55C"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical double struck small l", "alternative": "double struck small l", "short": "double struck l"}}, "key": "1D55D"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical double struck small m", "alternative": "double struck small m", "short": "double struck m"}}, "key": "1D55E"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical double struck small n", "alternative": "double struck small n", "short": "double struck n"}}, "key": "1D55F"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical double struck small o", "alternative": "double struck small o", "short": "double struck o"}}, "key": "1D560"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical double struck small p", "alternative": "double struck small p", "short": "double struck p"}}, "key": "1D561"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical double struck small q", "alternative": "double struck small q", "short": "double struck q"}}, "key": "1D562"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical double struck small r", "alternative": "double struck small r", "short": "double struck r"}}, "key": "1D563"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical double struck small s", "alternative": "double struck small s", "short": "double struck s"}}, "key": "1D564"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical double struck small t", "alternative": "double struck small t", "short": "double struck t"}}, "key": "1D565"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical double struck small u", "alternative": "double struck small u", "short": "double struck u"}}, "key": "1D566"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical double struck small v", "alternative": "double struck small v", "short": "double struck v"}}, "key": "1D567"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical double struck small w", "alternative": "double struck small w", "short": "double struck w"}}, "key": "1D568"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical double struck small x", "alternative": "double struck small x", "short": "double struck x"}}, "key": "1D569"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical double struck small y", "alternative": "double struck small y", "short": "double struck y"}}, "key": "1D56A"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical double struck small z", "alternative": "double struck small z", "short": "double struck z"}}, "key": "1D56B"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical bold fraktur capital a", "alternative": "bold fraktur capital a", "short": "bold fraktur cap a"}, "mathspeak": {"default": "bold fraktur upper A"}}, "key": "1D56C"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical bold fraktur capital b", "alternative": "bold fraktur capital b", "short": "bold fraktur cap b"}, "mathspeak": {"default": "bold fraktur upper B"}}, "key": "1D56D"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical bold fraktur capital c", "alternative": "bold fraktur capital c", "short": "bold fraktur cap c"}, "mathspeak": {"default": "bold fraktur upper C"}}, "key": "1D56E"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical bold fraktur capital d", "alternative": "bold fraktur capital d", "short": "bold fraktur cap d"}, "mathspeak": {"default": "bold fraktur upper D"}}, "key": "1D56F"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical bold fraktur capital e", "alternative": "bold fraktur capital e", "short": "bold fraktur cap e"}, "mathspeak": {"default": "bold fraktur upper E"}}, "key": "1D570"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical bold fraktur capital f", "alternative": "bold fraktur capital f", "short": "bold fraktur cap f"}, "mathspeak": {"default": "bold fraktur upper F"}}, "key": "1D571"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical bold fraktur capital g", "alternative": "bold fraktur capital g", "short": "bold fraktur cap g"}, "mathspeak": {"default": "bold fraktur upper G"}}, "key": "1D572"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical bold fraktur capital h", "alternative": "bold fraktur capital h", "short": "bold fraktur cap h"}, "mathspeak": {"default": "bold fraktur upper H"}}, "key": "1D573"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical bold fraktur capital i", "alternative": "bold fraktur capital i", "short": "bold fraktur cap i"}, "mathspeak": {"default": "bold fraktur upper I"}}, "key": "1D574"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical bold fraktur capital j", "alternative": "bold fraktur capital j", "short": "bold fraktur cap j"}, "mathspeak": {"default": "bold fraktur upper J"}}, "key": "1D575"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical bold fraktur capital k", "alternative": "bold fraktur capital k", "short": "bold fraktur cap k"}, "mathspeak": {"default": "bold fraktur upper K"}}, "key": "1D576"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical bold fraktur capital l", "alternative": "bold fraktur capital l", "short": "bold fraktur cap l"}, "mathspeak": {"default": "bold fraktur upper L"}}, "key": "1D577"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical bold fraktur capital m", "alternative": "bold fraktur capital m", "short": "bold fraktur cap m"}, "mathspeak": {"default": "bold fraktur upper M"}}, "key": "1D578"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical bold fraktur capital n", "alternative": "bold fraktur capital n", "short": "bold fraktur cap n"}, "mathspeak": {"default": "bold fraktur upper N"}}, "key": "1D579"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical bold fraktur capital o", "alternative": "bold fraktur capital o", "short": "bold fraktur cap o"}, "mathspeak": {"default": "bold fraktur upper O"}}, "key": "1D57A"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical bold fraktur capital p", "alternative": "bold fraktur capital p", "short": "bold fraktur cap p"}, "mathspeak": {"default": "bold fraktur upper P"}}, "key": "1D57B"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical bold fraktur capital q", "alternative": "bold fraktur capital q", "short": "bold fraktur cap q"}, "mathspeak": {"default": "bold fraktur upper Q"}}, "key": "1D57C"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical bold fraktur capital r", "alternative": "bold fraktur capital r", "short": "bold fraktur cap r"}, "mathspeak": {"default": "bold fraktur upper R"}}, "key": "1D57D"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical bold fraktur capital s", "alternative": "bold fraktur capital s", "short": "bold fraktur cap s"}, "mathspeak": {"default": "bold fraktur upper S"}}, "key": "1D57E"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical bold fraktur capital t", "alternative": "bold fraktur capital t", "short": "bold fraktur cap t"}, "mathspeak": {"default": "bold fraktur upper T"}}, "key": "1D57F"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical bold fraktur capital u", "alternative": "bold fraktur capital u", "short": "bold fraktur cap u"}, "mathspeak": {"default": "bold fraktur upper U"}}, "key": "1D580"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical bold fraktur capital v", "alternative": "bold fraktur capital v", "short": "bold fraktur cap v"}, "mathspeak": {"default": "bold fraktur upper V"}}, "key": "1D581"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical bold fraktur capital w", "alternative": "bold fraktur capital w", "short": "bold fraktur cap w"}, "mathspeak": {"default": "bold fraktur upper W"}}, "key": "1D582"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical bold fraktur capital x", "alternative": "bold fraktur capital x", "short": "bold fraktur cap x"}, "mathspeak": {"default": "bold fraktur upper X"}}, "key": "1D583"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical bold fraktur capital y", "alternative": "bold fraktur capital y", "short": "bold fraktur cap y"}, "mathspeak": {"default": "bold fraktur upper Y"}}, "key": "1D584"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical bold fraktur capital z", "alternative": "bold fraktur capital z", "short": "bold fraktur cap z"}, "mathspeak": {"default": "bold fraktur upper Z"}}, "key": "1D585"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical bold fraktur small a", "alternative": "bold fraktur small a", "short": "bold fraktur a"}}, "key": "1D586"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical bold fraktur small b", "alternative": "bold fraktur small b", "short": "bold fraktur b"}}, "key": "1D587"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical bold fraktur small c", "alternative": "bold fraktur small c", "short": "bold fraktur c"}}, "key": "1D588"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical bold fraktur small d", "alternative": "bold fraktur small d", "short": "bold fraktur d"}}, "key": "1D589"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical bold fraktur small e", "alternative": "bold fraktur small e", "short": "bold fraktur e"}}, "key": "1D58A"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical bold fraktur small f", "alternative": "bold fraktur small f", "short": "bold fraktur f"}}, "key": "1D58B"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical bold fraktur small g", "alternative": "bold fraktur small g", "short": "bold fraktur g"}}, "key": "1D58C"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical bold fraktur small h", "alternative": "bold fraktur small h", "short": "bold fraktur h"}}, "key": "1D58D"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical bold fraktur small i", "alternative": "bold fraktur small i", "short": "bold fraktur i"}}, "key": "1D58E"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical bold fraktur small j", "alternative": "bold fraktur small j", "short": "bold fraktur j"}}, "key": "1D58F"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical bold fraktur small k", "alternative": "bold fraktur small k", "short": "bold fraktur k"}}, "key": "1D590"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical bold fraktur small l", "alternative": "bold fraktur small l", "short": "bold fraktur l"}}, "key": "1D591"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical bold fraktur small m", "alternative": "bold fraktur small m", "short": "bold fraktur m"}}, "key": "1D592"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical bold fraktur small n", "alternative": "bold fraktur small n", "short": "bold fraktur n"}}, "key": "1D593"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical bold fraktur small o", "alternative": "bold fraktur small o", "short": "bold fraktur o"}}, "key": "1D594"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical bold fraktur small p", "alternative": "bold fraktur small p", "short": "bold fraktur p"}}, "key": "1D595"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical bold fraktur small q", "alternative": "bold fraktur small q", "short": "bold fraktur q"}}, "key": "1D596"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical bold fraktur small r", "alternative": "bold fraktur small r", "short": "bold fraktur r"}}, "key": "1D597"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical bold fraktur small s", "alternative": "bold fraktur small s", "short": "bold fraktur s"}}, "key": "1D598"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical bold fraktur small t", "alternative": "bold fraktur small t", "short": "bold fraktur t"}}, "key": "1D599"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical bold fraktur small u", "alternative": "bold fraktur small u", "short": "bold fraktur u"}}, "key": "1D59A"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical bold fraktur small v", "alternative": "bold fraktur small v", "short": "bold fraktur v"}}, "key": "1D59B"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical bold fraktur small w", "alternative": "bold fraktur small w", "short": "bold fraktur w"}}, "key": "1D59C"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical bold fraktur small x", "alternative": "bold fraktur small x", "short": "bold fraktur x"}}, "key": "1D59D"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical bold fraktur small y", "alternative": "bold fraktur small y", "short": "bold fraktur y"}}, "key": "1D59E"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical bold fraktur small z", "alternative": "bold fraktur small z", "short": "bold fraktur z"}}, "key": "1D59F"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical sans serif capital a", "alternative": "sans serif capital a", "short": "sans serif cap a"}, "mathspeak": {"default": "sans serif upper A"}}, "key": "1D5A0"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical sans serif capital b", "alternative": "sans serif capital b", "short": "sans serif cap b"}, "mathspeak": {"default": "sans serif upper B"}}, "key": "1D5A1"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical sans serif capital c", "alternative": "sans serif capital c", "short": "sans serif cap c"}, "mathspeak": {"default": "sans serif upper C"}}, "key": "1D5A2"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical sans serif capital d", "alternative": "sans serif capital d", "short": "sans serif cap d"}, "mathspeak": {"default": "sans serif upper D"}}, "key": "1D5A3"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical sans serif capital e", "alternative": "sans serif capital e", "short": "sans serif cap e"}, "mathspeak": {"default": "sans serif upper E"}}, "key": "1D5A4"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical sans serif capital f", "alternative": "sans serif capital f", "short": "sans serif cap f"}, "mathspeak": {"default": "sans serif upper F"}}, "key": "1D5A5"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical sans serif capital g", "alternative": "sans serif capital g", "short": "sans serif cap g"}, "mathspeak": {"default": "sans serif upper G"}}, "key": "1D5A6"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical sans serif capital h", "alternative": "sans serif capital h", "short": "sans serif cap h"}, "mathspeak": {"default": "sans serif upper H"}}, "key": "1D5A7"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical sans serif capital i", "alternative": "sans serif capital i", "short": "sans serif cap i"}, "mathspeak": {"default": "sans serif upper I"}}, "key": "1D5A8"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical sans serif capital j", "alternative": "sans serif capital j", "short": "sans serif cap j"}, "mathspeak": {"default": "sans serif upper J"}}, "key": "1D5A9"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical sans serif capital k", "alternative": "sans serif capital k", "short": "sans serif cap k"}, "mathspeak": {"default": "sans serif upper K"}}, "key": "1D5AA"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical sans serif capital l", "alternative": "sans serif capital l", "short": "sans serif cap l"}, "mathspeak": {"default": "sans serif upper L"}}, "key": "1D5AB"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical sans serif capital m", "alternative": "sans serif capital m", "short": "sans serif cap m"}, "mathspeak": {"default": "sans serif upper M"}}, "key": "1D5AC"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical sans serif capital n", "alternative": "sans serif capital n", "short": "sans serif cap n"}, "mathspeak": {"default": "sans serif upper N"}}, "key": "1D5AD"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical sans serif capital o", "alternative": "sans serif capital o", "short": "sans serif cap o"}, "mathspeak": {"default": "sans serif upper O"}}, "key": "1D5AE"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical sans serif capital p", "alternative": "sans serif capital p", "short": "sans serif cap p"}, "mathspeak": {"default": "sans serif upper P"}}, "key": "1D5AF"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical sans serif capital q", "alternative": "sans serif capital q", "short": "sans serif cap q"}, "mathspeak": {"default": "sans serif upper Q"}}, "key": "1D5B0"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical sans serif capital r", "alternative": "sans serif capital r", "short": "sans serif cap r"}, "mathspeak": {"default": "sans serif upper R"}}, "key": "1D5B1"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical sans serif capital s", "alternative": "sans serif capital s", "short": "sans serif cap s"}, "mathspeak": {"default": "sans serif upper S"}}, "key": "1D5B2"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical sans serif capital t", "alternative": "sans serif capital t", "short": "sans serif cap t"}, "mathspeak": {"default": "sans serif upper T"}}, "key": "1D5B3"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical sans serif capital u", "alternative": "sans serif capital u", "short": "sans serif cap u"}, "mathspeak": {"default": "sans serif upper U"}}, "key": "1D5B4"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical sans serif capital v", "alternative": "sans serif capital v", "short": "sans serif cap v"}, "mathspeak": {"default": "sans serif upper V"}}, "key": "1D5B5"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical sans serif capital w", "alternative": "sans serif capital w", "short": "sans serif cap w"}, "mathspeak": {"default": "sans serif upper W"}}, "key": "1D5B6"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical sans serif capital x", "alternative": "sans serif capital x", "short": "sans serif cap x"}, "mathspeak": {"default": "sans serif upper X"}}, "key": "1D5B7"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical sans serif capital y", "alternative": "sans serif capital y", "short": "sans serif cap y"}, "mathspeak": {"default": "sans serif upper Y"}}, "key": "1D5B8"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical sans serif capital z", "alternative": "sans serif capital z", "short": "sans serif cap z"}, "mathspeak": {"default": "sans serif upper Z"}}, "key": "1D5B9"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical sans serif small a", "alternative": "sans serif small a", "short": "sans serif a"}}, "key": "1D5BA"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical sans serif small b", "alternative": "sans serif small b", "short": "sans serif b"}}, "key": "1D5BB"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical sans serif small c", "alternative": "sans serif small c", "short": "sans serif c"}}, "key": "1D5BC"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical sans serif small d", "alternative": "sans serif small d", "short": "sans serif d"}}, "key": "1D5BD"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical sans serif small e", "alternative": "sans serif small e", "short": "sans serif e"}}, "key": "1D5BE"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical sans serif small f", "alternative": "sans serif small f", "short": "sans serif f"}}, "key": "1D5BF"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical sans serif small g", "alternative": "sans serif small g", "short": "sans serif g"}}, "key": "1D5C0"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical sans serif small h", "alternative": "sans serif small h", "short": "sans serif h"}}, "key": "1D5C1"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical sans serif small i", "alternative": "sans serif small i", "short": "sans serif i"}}, "key": "1D5C2"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical sans serif small j", "alternative": "sans serif small j", "short": "sans serif j"}}, "key": "1D5C3"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical sans serif small k", "alternative": "sans serif small k", "short": "sans serif k"}}, "key": "1D5C4"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical sans serif small l", "alternative": "sans serif small l", "short": "sans serif l"}}, "key": "1D5C5"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical sans serif small m", "alternative": "sans serif small m", "short": "sans serif m"}}, "key": "1D5C6"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical sans serif small n", "alternative": "sans serif small n", "short": "sans serif n"}}, "key": "1D5C7"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical sans serif small o", "alternative": "sans serif small o", "short": "sans serif o"}}, "key": "1D5C8"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical sans serif small p", "alternative": "sans serif small p", "short": "sans serif p"}}, "key": "1D5C9"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical sans serif small q", "alternative": "sans serif small q", "short": "sans serif q"}}, "key": "1D5CA"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical sans serif small r", "alternative": "sans serif small r", "short": "sans serif r"}}, "key": "1D5CB"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical sans serif small s", "alternative": "sans serif small s", "short": "sans serif s"}}, "key": "1D5CC"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical sans serif small t", "alternative": "sans serif small t", "short": "sans serif t"}}, "key": "1D5CD"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical sans serif small u", "alternative": "sans serif small u", "short": "sans serif u"}}, "key": "1D5CE"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical sans serif small v", "alternative": "sans serif small v", "short": "sans serif v"}}, "key": "1D5CF"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical sans serif small w", "alternative": "sans serif small w", "short": "sans serif w"}}, "key": "1D5D0"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical sans serif small x", "alternative": "sans serif small x", "short": "sans serif x"}}, "key": "1D5D1"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical sans serif small y", "alternative": "sans serif small y", "short": "sans serif y"}}, "key": "1D5D2"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical sans serif small z", "alternative": "sans serif small z", "short": "sans serif z"}}, "key": "1D5D3"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical sans serif bold capital a", "alternative": "sans serif bold capital a", "short": "sans serif bold cap a"}, "mathspeak": {"default": "sans serif bold upper A"}}, "key": "1D5D4"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical sans serif bold capital b", "alternative": "sans serif bold capital b", "short": "sans serif bold cap b"}, "mathspeak": {"default": "sans serif bold upper B"}}, "key": "1D5D5"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical sans serif bold capital c", "alternative": "sans serif bold capital c", "short": "sans serif bold cap c"}, "mathspeak": {"default": "sans serif bold upper C"}}, "key": "1D5D6"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical sans serif bold capital d", "alternative": "sans serif bold capital d", "short": "sans serif bold cap d"}, "mathspeak": {"default": "sans serif bold upper D"}}, "key": "1D5D7"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical sans serif bold capital e", "alternative": "sans serif bold capital e", "short": "sans serif bold cap e"}, "mathspeak": {"default": "sans serif bold upper E"}}, "key": "1D5D8"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical sans serif bold capital f", "alternative": "sans serif bold capital f", "short": "sans serif bold cap f"}, "mathspeak": {"default": "sans serif bold upper F"}}, "key": "1D5D9"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical sans serif bold capital g", "alternative": "sans serif bold capital g", "short": "sans serif bold cap g"}, "mathspeak": {"default": "sans serif bold upper G"}}, "key": "1D5DA"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical sans serif bold capital h", "alternative": "sans serif bold capital h", "short": "sans serif bold cap h"}, "mathspeak": {"default": "sans serif bold upper H"}}, "key": "1D5DB"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical sans serif bold capital i", "alternative": "sans serif bold capital i", "short": "sans serif bold cap i"}, "mathspeak": {"default": "sans serif bold upper I"}}, "key": "1D5DC"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical sans serif bold capital j", "alternative": "sans serif bold capital j", "short": "sans serif bold cap j"}, "mathspeak": {"default": "sans serif bold upper J"}}, "key": "1D5DD"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical sans serif bold capital k", "alternative": "sans serif bold capital k", "short": "sans serif bold cap k"}, "mathspeak": {"default": "sans serif bold upper K"}}, "key": "1D5DE"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical sans serif bold capital l", "alternative": "sans serif bold capital l", "short": "sans serif bold cap l"}, "mathspeak": {"default": "sans serif bold upper L"}}, "key": "1D5DF"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical sans serif bold capital m", "alternative": "sans serif bold capital m", "short": "sans serif bold cap m"}, "mathspeak": {"default": "sans serif bold upper M"}}, "key": "1D5E0"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical sans serif bold capital n", "alternative": "sans serif bold capital n", "short": "sans serif bold cap n"}, "mathspeak": {"default": "sans serif bold upper N"}}, "key": "1D5E1"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical sans serif bold capital o", "alternative": "sans serif bold capital o", "short": "sans serif bold cap o"}, "mathspeak": {"default": "sans serif bold upper O"}}, "key": "1D5E2"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical sans serif bold capital p", "alternative": "sans serif bold capital p", "short": "sans serif bold cap p"}, "mathspeak": {"default": "sans serif bold upper P"}}, "key": "1D5E3"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical sans serif bold capital q", "alternative": "sans serif bold capital q", "short": "sans serif bold cap q"}, "mathspeak": {"default": "sans serif bold upper Q"}}, "key": "1D5E4"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical sans serif bold capital r", "alternative": "sans serif bold capital r", "short": "sans serif bold cap r"}, "mathspeak": {"default": "sans serif bold upper R"}}, "key": "1D5E5"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical sans serif bold capital s", "alternative": "sans serif bold capital s", "short": "sans serif bold cap s"}, "mathspeak": {"default": "sans serif bold upper S"}}, "key": "1D5E6"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical sans serif bold capital t", "alternative": "sans serif bold capital t", "short": "sans serif bold cap t"}, "mathspeak": {"default": "sans serif bold upper T"}}, "key": "1D5E7"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical sans serif bold capital u", "alternative": "sans serif bold capital u", "short": "sans serif bold cap u"}, "mathspeak": {"default": "sans serif bold upper U"}}, "key": "1D5E8"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical sans serif bold capital v", "alternative": "sans serif bold capital v", "short": "sans serif bold cap v"}, "mathspeak": {"default": "sans serif bold upper V"}}, "key": "1D5E9"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical sans serif bold capital w", "alternative": "sans serif bold capital w", "short": "sans serif bold cap w"}, "mathspeak": {"default": "sans serif bold upper W"}}, "key": "1D5EA"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical sans serif bold capital x", "alternative": "sans serif bold capital x", "short": "sans serif bold cap x"}, "mathspeak": {"default": "sans serif bold upper X"}}, "key": "1D5EB"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical sans serif bold capital y", "alternative": "sans serif bold capital y", "short": "sans serif bold cap y"}, "mathspeak": {"default": "sans serif bold upper Y"}}, "key": "1D5EC"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical sans serif bold capital z", "alternative": "sans serif bold capital z", "short": "sans serif bold cap z"}, "mathspeak": {"default": "sans serif bold upper Z"}}, "key": "1D5ED"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical sans serif bold small a", "alternative": "sans serif bold small a", "short": "sans serif bold a"}}, "key": "1D5EE"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical sans serif bold small b", "alternative": "sans serif bold small b", "short": "sans serif bold b"}}, "key": "1D5EF"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical sans serif bold small c", "alternative": "sans serif bold small c", "short": "sans serif bold c"}}, "key": "1D5F0"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical sans serif bold small d", "alternative": "sans serif bold small d", "short": "sans serif bold d"}}, "key": "1D5F1"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical sans serif bold small e", "alternative": "sans serif bold small e", "short": "sans serif bold e"}}, "key": "1D5F2"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical sans serif bold small f", "alternative": "sans serif bold small f", "short": "sans serif bold f"}}, "key": "1D5F3"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical sans serif bold small g", "alternative": "sans serif bold small g", "short": "sans serif bold g"}}, "key": "1D5F4"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical sans serif bold small h", "alternative": "sans serif bold small h", "short": "sans serif bold h"}}, "key": "1D5F5"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical sans serif bold small i", "alternative": "sans serif bold small i", "short": "sans serif bold i"}}, "key": "1D5F6"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical sans serif bold small j", "alternative": "sans serif bold small j", "short": "sans serif bold j"}}, "key": "1D5F7"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical sans serif bold small k", "alternative": "sans serif bold small k", "short": "sans serif bold k"}}, "key": "1D5F8"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical sans serif bold small l", "alternative": "sans serif bold small l", "short": "sans serif bold l"}}, "key": "1D5F9"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical sans serif bold small m", "alternative": "sans serif bold small m", "short": "sans serif bold m"}}, "key": "1D5FA"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical sans serif bold small n", "alternative": "sans serif bold small n", "short": "sans serif bold n"}}, "key": "1D5FB"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical sans serif bold small o", "alternative": "sans serif bold small o", "short": "sans serif bold o"}}, "key": "1D5FC"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical sans serif bold small p", "alternative": "sans serif bold small p", "short": "sans serif bold p"}}, "key": "1D5FD"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical sans serif bold small q", "alternative": "sans serif bold small q", "short": "sans serif bold q"}}, "key": "1D5FE"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical sans serif bold small r", "alternative": "sans serif bold small r", "short": "sans serif bold r"}}, "key": "1D5FF"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical sans serif bold small s", "alternative": "sans serif bold small s", "short": "sans serif bold s"}}, "key": "1D600"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical sans serif bold small t", "alternative": "sans serif bold small t", "short": "sans serif bold t"}}, "key": "1D601"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical sans serif bold small u", "alternative": "sans serif bold small u", "short": "sans serif bold u"}}, "key": "1D602"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical sans serif bold small v", "alternative": "sans serif bold small v", "short": "sans serif bold v"}}, "key": "1D603"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical sans serif bold small w", "alternative": "sans serif bold small w", "short": "sans serif bold w"}}, "key": "1D604"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical sans serif bold small x", "alternative": "sans serif bold small x", "short": "sans serif bold x"}}, "key": "1D605"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical sans serif bold small y", "alternative": "sans serif bold small y", "short": "sans serif bold y"}}, "key": "1D606"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical sans serif bold small z", "alternative": "sans serif bold small z", "short": "sans serif bold z"}}, "key": "1D607"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical sans serif italic capital a", "alternative": "sans serif italic capital a", "short": "sans serif italic cap a"}, "mathspeak": {"default": "sans serif italic upper A"}}, "key": "1D608"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical sans serif italic capital b", "alternative": "sans serif italic capital b", "short": "sans serif italic cap b"}, "mathspeak": {"default": "sans serif italic upper B"}}, "key": "1D609"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical sans serif italic capital c", "alternative": "sans serif italic capital c", "short": "sans serif italic cap c"}, "mathspeak": {"default": "sans serif italic upper C"}}, "key": "1D60A"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical sans serif italic capital d", "alternative": "sans serif italic capital d", "short": "sans serif italic cap d"}, "mathspeak": {"default": "sans serif italic upper D"}}, "key": "1D60B"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical sans serif italic capital e", "alternative": "sans serif italic capital e", "short": "sans serif italic cap e"}, "mathspeak": {"default": "sans serif italic upper E"}}, "key": "1D60C"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical sans serif italic capital f", "alternative": "sans serif italic capital f", "short": "sans serif italic cap f"}, "mathspeak": {"default": "sans serif italic upper F"}}, "key": "1D60D"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical sans serif italic capital g", "alternative": "sans serif italic capital g", "short": "sans serif italic cap g"}, "mathspeak": {"default": "sans serif italic upper G"}}, "key": "1D60E"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical sans serif italic capital h", "alternative": "sans serif italic capital h", "short": "sans serif italic cap h"}, "mathspeak": {"default": "sans serif italic upper H"}}, "key": "1D60F"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical sans serif italic capital i", "alternative": "sans serif italic capital i", "short": "sans serif italic cap i"}, "mathspeak": {"default": "sans serif italic upper I"}}, "key": "1D610"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical sans serif italic capital j", "alternative": "sans serif italic capital j", "short": "sans serif italic cap j"}, "mathspeak": {"default": "sans serif italic upper J"}}, "key": "1D611"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical sans serif italic capital k", "alternative": "sans serif italic capital k", "short": "sans serif italic cap k"}, "mathspeak": {"default": "sans serif italic upper K"}}, "key": "1D612"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical sans serif italic capital l", "alternative": "sans serif italic capital l", "short": "sans serif italic cap l"}, "mathspeak": {"default": "sans serif italic upper L"}}, "key": "1D613"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical sans serif italic capital m", "alternative": "sans serif italic capital m", "short": "sans serif italic cap m"}, "mathspeak": {"default": "sans serif italic upper M"}}, "key": "1D614"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical sans serif italic capital n", "alternative": "sans serif italic capital n", "short": "sans serif italic cap n"}, "mathspeak": {"default": "sans serif italic upper N"}}, "key": "1D615"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical sans serif italic capital o", "alternative": "sans serif italic capital o", "short": "sans serif italic cap o"}, "mathspeak": {"default": "sans serif italic upper O"}}, "key": "1D616"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical sans serif italic capital p", "alternative": "sans serif italic capital p", "short": "sans serif italic cap p"}, "mathspeak": {"default": "sans serif italic upper P"}}, "key": "1D617"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical sans serif italic capital q", "alternative": "sans serif italic capital q", "short": "sans serif italic cap q"}, "mathspeak": {"default": "sans serif italic upper Q"}}, "key": "1D618"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical sans serif italic capital r", "alternative": "sans serif italic capital r", "short": "sans serif italic cap r"}, "mathspeak": {"default": "sans serif italic upper R"}}, "key": "1D619"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical sans serif italic capital s", "alternative": "sans serif italic capital s", "short": "sans serif italic cap s"}, "mathspeak": {"default": "sans serif italic upper S"}}, "key": "1D61A"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical sans serif italic capital t", "alternative": "sans serif italic capital t", "short": "sans serif italic cap t"}, "mathspeak": {"default": "sans serif italic upper T"}}, "key": "1D61B"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical sans serif italic capital u", "alternative": "sans serif italic capital u", "short": "sans serif italic cap u"}, "mathspeak": {"default": "sans serif italic upper U"}}, "key": "1D61C"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical sans serif italic capital v", "alternative": "sans serif italic capital v", "short": "sans serif italic cap v"}, "mathspeak": {"default": "sans serif italic upper V"}}, "key": "1D61D"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical sans serif italic capital w", "alternative": "sans serif italic capital w", "short": "sans serif italic cap w"}, "mathspeak": {"default": "sans serif italic upper W"}}, "key": "1D61E"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical sans serif italic capital x", "alternative": "sans serif italic capital x", "short": "sans serif italic cap x"}, "mathspeak": {"default": "sans serif italic upper X"}}, "key": "1D61F"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical sans serif italic capital y", "alternative": "sans serif italic capital y", "short": "sans serif italic cap y"}, "mathspeak": {"default": "sans serif italic upper Y"}}, "key": "1D620"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical sans serif italic capital z", "alternative": "sans serif italic capital z", "short": "sans serif italic cap z"}, "mathspeak": {"default": "sans serif italic upper Z"}}, "key": "1D621"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical sans serif italic small a", "alternative": "sans serif italic small a", "short": "sans serif italic a"}}, "key": "1D622"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical sans serif italic small b", "alternative": "sans serif italic small b", "short": "sans serif italic b"}}, "key": "1D623"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical sans serif italic small c", "alternative": "sans serif italic small c", "short": "sans serif italic c"}}, "key": "1D624"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical sans serif italic small d", "alternative": "sans serif italic small d", "short": "sans serif italic d"}}, "key": "1D625"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical sans serif italic small e", "alternative": "sans serif italic small e", "short": "sans serif italic e"}}, "key": "1D626"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical sans serif italic small f", "alternative": "sans serif italic small f", "short": "sans serif italic f"}}, "key": "1D627"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical sans serif italic small g", "alternative": "sans serif italic small g", "short": "sans serif italic g"}}, "key": "1D628"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical sans serif italic small h", "alternative": "sans serif italic small h", "short": "sans serif italic h"}}, "key": "1D629"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical sans serif italic small i", "alternative": "sans serif italic small i", "short": "sans serif italic i"}}, "key": "1D62A"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical sans serif italic small j", "alternative": "sans serif italic small j", "short": "sans serif italic j"}}, "key": "1D62B"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical sans serif italic small k", "alternative": "sans serif italic small k", "short": "sans serif italic k"}}, "key": "1D62C"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical sans serif italic small l", "alternative": "sans serif italic small l", "short": "sans serif italic l"}}, "key": "1D62D"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical sans serif italic small m", "alternative": "sans serif italic small m", "short": "sans serif italic m"}}, "key": "1D62E"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical sans serif italic small n", "alternative": "sans serif italic small n", "short": "sans serif italic n"}}, "key": "1D62F"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical sans serif italic small o", "alternative": "sans serif italic small o", "short": "sans serif italic o"}}, "key": "1D630"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical sans serif italic small p", "alternative": "sans serif italic small p", "short": "sans serif italic p"}}, "key": "1D631"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical sans serif italic small q", "alternative": "sans serif italic small q", "short": "sans serif italic q"}}, "key": "1D632"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical sans serif italic small r", "alternative": "sans serif italic small r", "short": "sans serif italic r"}}, "key": "1D633"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical sans serif italic small s", "alternative": "sans serif italic small s", "short": "sans serif italic s"}}, "key": "1D634"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical sans serif italic small t", "alternative": "sans serif italic small t", "short": "sans serif italic t"}}, "key": "1D635"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical sans serif italic small u", "alternative": "sans serif italic small u", "short": "sans serif italic u"}}, "key": "1D636"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical sans serif italic small v", "alternative": "sans serif italic small v", "short": "sans serif italic v"}}, "key": "1D637"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical sans serif italic small w", "alternative": "sans serif italic small w", "short": "sans serif italic w"}}, "key": "1D638"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical sans serif italic small x", "alternative": "sans serif italic small x", "short": "sans serif italic x"}}, "key": "1D639"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical sans serif italic small y", "alternative": "sans serif italic small y", "short": "sans serif italic y"}}, "key": "1D63A"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical sans serif italic small z", "alternative": "sans serif italic small z", "short": "sans serif italic z"}}, "key": "1D63B"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical monospace capital a", "alternative": "monospace capital a", "short": "monospace cap a"}, "mathspeak": {"default": "monospace upper A"}}, "key": "1D670"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical monospace capital b", "alternative": "monospace capital b", "short": "monospace cap b"}, "mathspeak": {"default": "monospace upper B"}}, "key": "1D671"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical monospace capital c", "alternative": "monospace capital c", "short": "monospace cap c"}, "mathspeak": {"default": "monospace upper C"}}, "key": "1D672"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical monospace capital d", "alternative": "monospace capital d", "short": "monospace cap d"}, "mathspeak": {"default": "monospace upper D"}}, "key": "1D673"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical monospace capital e", "alternative": "monospace capital e", "short": "monospace cap e"}, "mathspeak": {"default": "monospace upper E"}}, "key": "1D674"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical monospace capital f", "alternative": "monospace capital f", "short": "monospace cap f"}, "mathspeak": {"default": "monospace upper F"}}, "key": "1D675"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical monospace capital g", "alternative": "monospace capital g", "short": "monospace cap g"}, "mathspeak": {"default": "monospace upper G"}}, "key": "1D676"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical monospace capital h", "alternative": "monospace capital h", "short": "monospace cap h"}, "mathspeak": {"default": "monospace upper H"}}, "key": "1D677"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical monospace capital i", "alternative": "monospace capital i", "short": "monospace cap i"}, "mathspeak": {"default": "monospace upper I"}}, "key": "1D678"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical monospace capital j", "alternative": "monospace capital j", "short": "monospace cap j"}, "mathspeak": {"default": "monospace upper J"}}, "key": "1D679"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical monospace capital k", "alternative": "monospace capital k", "short": "monospace cap k"}, "mathspeak": {"default": "monospace upper K"}}, "key": "1D67A"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical monospace capital l", "alternative": "monospace capital l", "short": "monospace cap l"}, "mathspeak": {"default": "monospace upper L"}}, "key": "1D67B"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical monospace capital m", "alternative": "monospace capital m", "short": "monospace cap m"}, "mathspeak": {"default": "monospace upper M"}}, "key": "1D67C"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical monospace capital n", "alternative": "monospace capital n", "short": "monospace cap n"}, "mathspeak": {"default": "monospace upper N"}}, "key": "1D67D"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical monospace capital o", "alternative": "monospace capital o", "short": "monospace cap o"}, "mathspeak": {"default": "monospace upper O"}}, "key": "1D67E"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical monospace capital p", "alternative": "monospace capital p", "short": "monospace cap p"}, "mathspeak": {"default": "monospace upper P"}}, "key": "1D67F"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical monospace capital q", "alternative": "monospace capital q", "short": "monospace cap q"}, "mathspeak": {"default": "monospace upper Q"}}, "key": "1D680"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical monospace capital r", "alternative": "monospace capital r", "short": "monospace cap r"}, "mathspeak": {"default": "monospace upper R"}}, "key": "1D681"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical monospace capital s", "alternative": "monospace capital s", "short": "monospace cap s"}, "mathspeak": {"default": "monospace upper S"}}, "key": "1D682"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical monospace capital t", "alternative": "monospace capital t", "short": "monospace cap t"}, "mathspeak": {"default": "monospace upper T"}}, "key": "1D683"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical monospace capital u", "alternative": "monospace capital u", "short": "monospace cap u"}, "mathspeak": {"default": "monospace upper U"}}, "key": "1D684"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical monospace capital v", "alternative": "monospace capital v", "short": "monospace cap v"}, "mathspeak": {"default": "monospace upper V"}}, "key": "1D685"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical monospace capital w", "alternative": "monospace capital w", "short": "monospace cap w"}, "mathspeak": {"default": "monospace upper W"}}, "key": "1D686"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical monospace capital x", "alternative": "monospace capital x", "short": "monospace cap x"}, "mathspeak": {"default": "monospace upper X"}}, "key": "1D687"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical monospace capital y", "alternative": "monospace capital y", "short": "monospace cap y"}, "mathspeak": {"default": "monospace upper Y"}}, "key": "1D688"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical monospace capital z", "alternative": "monospace capital z", "short": "monospace cap z"}, "mathspeak": {"default": "monospace upper Z"}}, "key": "1D689"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical monospace small a", "alternative": "monospace small a", "short": "monospace a"}}, "key": "1D68A"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical monospace small b", "alternative": "monospace small b", "short": "monospace b"}}, "key": "1D68B"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical monospace small c", "alternative": "monospace small c", "short": "monospace c"}}, "key": "1D68C"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical monospace small d", "alternative": "monospace small d", "short": "monospace d"}}, "key": "1D68D"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical monospace small e", "alternative": "monospace small e", "short": "monospace e"}}, "key": "1D68E"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical monospace small f", "alternative": "monospace small f", "short": "monospace f"}}, "key": "1D68F"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical monospace small g", "alternative": "monospace small g", "short": "monospace g"}}, "key": "1D690"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical monospace small h", "alternative": "monospace small h", "short": "monospace h"}}, "key": "1D691"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical monospace small i", "alternative": "monospace small i", "short": "monospace i"}}, "key": "1D692"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical monospace small j", "alternative": "monospace small j", "short": "monospace j"}}, "key": "1D693"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical monospace small k", "alternative": "monospace small k", "short": "monospace k"}}, "key": "1D694"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical monospace small l", "alternative": "monospace small l", "short": "monospace l"}}, "key": "1D695"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical monospace small m", "alternative": "monospace small m", "short": "monospace m"}}, "key": "1D696"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical monospace small n", "alternative": "monospace small n", "short": "monospace n"}}, "key": "1D697"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical monospace small o", "alternative": "monospace small o", "short": "monospace o"}}, "key": "1D698"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical monospace small p", "alternative": "monospace small p", "short": "monospace p"}}, "key": "1D699"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical monospace small q", "alternative": "monospace small q", "short": "monospace q"}}, "key": "1D69A"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical monospace small r", "alternative": "monospace small r", "short": "monospace r"}}, "key": "1D69B"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical monospace small s", "alternative": "monospace small s", "short": "monospace s"}}, "key": "1D69C"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical monospace small t", "alternative": "monospace small t", "short": "monospace t"}}, "key": "1D69D"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical monospace small u", "alternative": "monospace small u", "short": "monospace u"}}, "key": "1D69E"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical monospace small v", "alternative": "monospace small v", "short": "monospace v"}}, "key": "1D69F"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical monospace small w", "alternative": "monospace small w", "short": "monospace w"}}, "key": "1D6A0"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical monospace small x", "alternative": "monospace small x", "short": "monospace x"}}, "key": "1D6A1"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical monospace small y", "alternative": "monospace small y", "short": "monospace y"}}, "key": "1D6A2"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical monospace small z", "alternative": "monospace small z", "short": "monospace z"}}, "key": "1D6A3"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical italic small dotless i", "alternative": "italic small dotless i", "short": "italic dotless i"}}, "key": "1D6A4"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical italic small dotless j", "alternative": "italic small dotless j", "short": "italic dotless j"}}, "key": "1D6A5"}]