/*
 *  /MathJax/jax/output/HTML-CSS/fonts/Asana-Math/Marks/Regular/Main.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.OutputJax["HTML-CSS"].FONTDATA.FONTS.AsanaMathJax_Marks={directory:"Marks/Regular",family:"AsanaMathJax_Marks",testString:"\u02DB\u0305\u0332\u0333\u033F\u0342\u2015\u2017\u201A\u201B\u201E\u2022\u2024\u2025\u2030",32:[0,0,249,0,0],731:[0,188,333,63,269],773:[587,-542,0,-433,0],818:[-130,175,0,-433,0],819:[-130,283,0,-433,0],831:[695,-542,0,-433,0],834:[729,-653,0,-324,-31],8213:[271,-213,1000,0,1000],8215:[-75,225,499,0,500],8218:[100,165,403,141,334],8219:[709,-446,277,45,233],8222:[100,165,605,141,536],8226:[466,-75,522,65,458],8228:[111,5,315,100,216],8229:[111,5,530,100,431],8240:[709,20,1143,123,1021],8246:[495,-47,599,53,548],8247:[495,-47,834,53,783],8251:[547,5,668,59,614],8252:[694,5,973,81,394],8255:[96,58,834,36,798],8256:[642,-488,834,36,798],8261:[726,184,332,79,288],8262:[726,184,332,45,254],8272:[688,0,980,43,937],8285:[623,3,226,55,171],8286:[630,3,226,55,171],8400:[791,-636,557,0,558],8401:[791,-636,557,0,558],8402:[813,31,416,252,312],8403:[1014,0,987,679,738],8404:[780,-492,680,0,681],8405:[780,-492,680,0,681],8406:[790,-519,557,0,558],8408:[417,-124,388,47,342],8409:[542,-236,447,47,401],8410:[541,-235,447,47,401],8411:[694,-578,519,27,493],8412:[694,-578,694,27,668],8413:[825,218,0,-1045,0],8414:[705,164,0,-870,1],8415:[1114,117,0,-1230,0],8416:[705,164,0,-872,-1],8417:[790,-519,556,0,557],8418:[655,55,0,-1255,0],8419:[960,259,0,-1219,0],8420:[896,62,0,-849,0],8421:[714,169,0,-333,0],8422:[713,172,0,-345,-76],8423:[710,15,0,-283,283],8424:[-142,258,519,27,493],8425:[723,-514,629,0,630],8426:[486,-55,1013,65,949],8427:[714,169,0,-609,-65],8428:[673,-518,557,0,558],8429:[673,-518,557,0,558],8430:[-83,354,556,0,557],8431:[-83,354,556,0,557],12308:[709,191,384,87,298],12309:[709,191,384,87,298],12312:[709,191,384,87,298],12313:[730,212,384,77,308]};MathJax.Callback.Queue(["initFont",MathJax.OutputJax["HTML-CSS"],"AsanaMathJax_Marks"],["loadComplete",MathJax.Ajax,MathJax.OutputJax["HTML-CSS"].fontDir+"/Marks/Regular/Main.js"]);
