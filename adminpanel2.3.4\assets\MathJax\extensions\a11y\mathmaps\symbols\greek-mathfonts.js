[{"locale": "en"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical bold capital alpha", "alternative": "bold capital alpha", "short": "bold cap alpha"}, "mathspeak": {"default": "bold upper Alpha"}}, "key": "1D6A8"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical bold capital beta", "alternative": "bold capital beta", "short": "bold cap beta"}, "mathspeak": {"default": "bold upper Beta"}}, "key": "1D6A9"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical bold capital gamma", "alternative": "bold capital gamma", "short": "bold cap gamma"}, "mathspeak": {"default": "bold upper Gamma"}}, "key": "1D6AA"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical bold capital delta", "alternative": "bold capital delta", "short": "bold cap delta"}, "mathspeak": {"default": "bold upper Delta"}}, "key": "1D6AB"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical bold capital epsilon", "alternative": "bold capital epsilon", "short": "bold cap epsilon"}, "mathspeak": {"default": "bold upper Epsilon"}}, "key": "1D6AC"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical bold capital zeta", "alternative": "bold capital zeta", "short": "bold cap zeta"}, "mathspeak": {"default": "bold upper Zeta"}}, "key": "1D6AD"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical bold capital eta", "alternative": "bold capital eta", "short": "bold cap eta"}, "mathspeak": {"default": "bold upper Eta"}}, "key": "1D6AE"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical bold capital theta", "alternative": "bold capital theta", "short": "bold cap theta"}, "mathspeak": {"default": "bold upper Theta"}}, "key": "1D6AF"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical bold capital iota", "alternative": "bold capital iota", "short": "bold cap iota"}, "mathspeak": {"default": "bold upper Iota"}}, "key": "1D6B0"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical bold capital kappa", "alternative": "bold capital kappa", "short": "bold cap kappa"}, "mathspeak": {"default": "bold upper Kappa"}}, "key": "1D6B1"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical bold capital lamda", "alternative": "bold capital lamda", "short": "bold cap lamda"}, "mathspeak": {"default": "bold upper Lamda"}}, "key": "1D6B2"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical bold capital mu", "alternative": "bold capital mu", "short": "bold cap mu"}, "mathspeak": {"default": "bold upper Mu"}}, "key": "1D6B3"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical bold capital nu", "alternative": "bold capital nu", "short": "bold cap nu"}, "mathspeak": {"default": "bold upper Nu"}}, "key": "1D6B4"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical bold capital xi", "alternative": "bold capital xi", "short": "bold cap xi"}, "mathspeak": {"default": "bold upper Xi"}}, "key": "1D6B5"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical bold capital omicron", "alternative": "bold capital omicron", "short": "bold cap omicron"}, "mathspeak": {"default": "bold upper Omicron"}}, "key": "1D6B6"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical bold capital pi", "alternative": "bold capital pi", "short": "bold cap pi"}, "mathspeak": {"default": "bold upper Pi"}}, "key": "1D6B7"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical bold capital rho", "alternative": "bold capital rho", "short": "bold cap rho"}, "mathspeak": {"default": "bold upper Rho"}}, "key": "1D6B8"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical bold capital theta symbol", "alternative": "bold capital theta", "short": "bold cap theta"}, "mathspeak": {"default": "bold upper Theta"}}, "key": "1D6B9"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical bold capital sigma", "alternative": "bold capital sigma", "short": "bold cap sigma"}, "mathspeak": {"default": "bold upper Sigma"}}, "key": "1D6BA"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical bold capital tau", "alternative": "bold capital tau", "short": "bold cap tau"}, "mathspeak": {"default": "bold upper Tau"}}, "key": "1D6BB"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical bold capital upsilon", "alternative": "bold capital upsilon", "short": "bold cap upsilon"}, "mathspeak": {"default": "bold upper Upsilon"}}, "key": "1D6BC"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical bold capital phi", "alternative": "bold capital phi", "short": "bold cap phi"}, "mathspeak": {"default": "bold upper Phi"}}, "key": "1D6BD"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical bold capital chi", "alternative": "bold capital chi", "short": "bold cap chi"}, "mathspeak": {"default": "bold upper Chi"}}, "key": "1D6BE"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical bold capital psi", "alternative": "bold capital psi", "short": "bold cap psi"}, "mathspeak": {"default": "bold upper Psi"}}, "key": "1D6BF"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical bold capital omega", "alternative": "bold capital omega", "short": "bold cap omega"}, "mathspeak": {"default": "bold upper Omega"}}, "key": "1D6C0"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical bold small alpha", "alternative": "bold small alpha", "short": "bold alpha"}}, "key": "1D6C2"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical bold small beta", "alternative": "bold small beta", "short": "bold beta"}}, "key": "1D6C3"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical bold small gamma", "alternative": "bold small gamma", "short": "bold gamma"}}, "key": "1D6C4"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical bold small delta", "alternative": "bold small delta", "short": "bold delta"}}, "key": "1D6C5"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical bold small epsilon", "alternative": "bold small epsilon", "short": "bold epsilon"}}, "key": "1D6C6"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical bold small zeta", "alternative": "bold small zeta", "short": "bold zeta"}}, "key": "1D6C7"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical bold small eta", "alternative": "bold small eta", "short": "bold eta"}}, "key": "1D6C8"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical bold small theta", "alternative": "bold small theta", "short": "bold theta"}}, "key": "1D6C9"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical bold small iota", "alternative": "bold small iota", "short": "bold iota"}}, "key": "1D6CA"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical bold small kappa", "alternative": "bold small kappa", "short": "bold kappa"}}, "key": "1D6CB"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical bold small lamda", "alternative": "bold small lamda", "short": "bold lamda"}}, "key": "1D6CC"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical bold small mu", "alternative": "bold small mu", "short": "bold mu"}}, "key": "1D6CD"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical bold small nu", "alternative": "bold small nu", "short": "bold nu"}}, "key": "1D6CE"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical bold small xi", "alternative": "bold small xi", "short": "bold xi"}}, "key": "1D6CF"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical bold small omicron", "alternative": "bold small omicron", "short": "bold omicron"}}, "key": "1D6D0"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical bold small pi", "alternative": "bold small pi", "short": "bold pi"}}, "key": "1D6D1"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical bold small rho", "alternative": "bold small rho", "short": "bold rho"}}, "key": "1D6D2"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical bold small final sigma", "alternative": "bold small final sigma", "short": "bold final sigma"}}, "key": "1D6D3"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical bold small sigma", "alternative": "bold small sigma", "short": "bold sigma"}}, "key": "1D6D4"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical bold small tau", "alternative": "bold small tau", "short": "bold tau"}}, "key": "1D6D5"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical bold small upsilon", "alternative": "bold small upsilon", "short": "bold upsilon"}}, "key": "1D6D6"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical bold small phi", "alternative": "bold small phi", "short": "bold phi"}}, "key": "1D6D7"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical bold small chi", "alternative": "bold small chi", "short": "bold chi"}}, "key": "1D6D8"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical bold small psi", "alternative": "bold small psi", "short": "bold psi"}}, "key": "1D6D9"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical bold small omega", "alternative": "bold small omega", "short": "bold omega"}}, "key": "1D6DA"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical italic capital alpha", "alternative": "italic capital alpha", "short": "italic cap alpha"}, "mathspeak": {"default": "italic upper Alpha"}}, "key": "1D6E2"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical italic capital beta", "alternative": "italic capital beta", "short": "italic cap beta"}, "mathspeak": {"default": "italic upper Beta"}}, "key": "1D6E3"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical italic capital gamma", "alternative": "italic capital gamma", "short": "italic cap gamma"}, "mathspeak": {"default": "italic upper Gamma"}}, "key": "1D6E4"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical italic capital delta", "alternative": "italic capital delta", "short": "italic cap delta"}, "mathspeak": {"default": "italic upper Delta"}}, "key": "1D6E5"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical italic capital epsilon", "alternative": "italic capital epsilon", "short": "italic cap epsilon"}, "mathspeak": {"default": "italic upper Epsilon"}}, "key": "1D6E6"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical italic capital zeta", "alternative": "italic capital zeta", "short": "italic cap zeta"}, "mathspeak": {"default": "italic upper Zeta"}}, "key": "1D6E7"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical italic capital eta", "alternative": "italic capital eta", "short": "italic cap eta"}, "mathspeak": {"default": "italic upper Eta"}}, "key": "1D6E8"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical italic capital theta", "alternative": "italic capital theta", "short": "italic cap theta"}, "mathspeak": {"default": "italic upper Theta"}}, "key": "1D6E9"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical italic capital iota", "alternative": "italic capital iota", "short": "italic cap iota"}, "mathspeak": {"default": "italic upper Iota"}}, "key": "1D6EA"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical italic capital kappa", "alternative": "italic capital kappa", "short": "italic cap kappa"}, "mathspeak": {"default": "italic upper Kappa"}}, "key": "1D6EB"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical italic capital lamda", "alternative": "italic capital lamda", "short": "italic cap lamda"}, "mathspeak": {"default": "italic upper Lamda"}}, "key": "1D6EC"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical italic capital mu", "alternative": "italic capital mu", "short": "italic cap mu"}, "mathspeak": {"default": "italic upper Mu"}}, "key": "1D6ED"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical italic capital nu", "alternative": "italic capital nu", "short": "italic cap nu"}, "mathspeak": {"default": "italic upper Nu"}}, "key": "1D6EE"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical italic capital xi", "alternative": "italic capital xi", "short": "italic cap xi"}, "mathspeak": {"default": "italic upper Xi"}}, "key": "1D6EF"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical italic capital omicron", "alternative": "italic capital omicron", "short": "italic cap omicron"}, "mathspeak": {"default": "italic upper Omicron"}}, "key": "1D6F0"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical italic capital pi", "alternative": "italic capital pi", "short": "italic cap pi"}, "mathspeak": {"default": "italic upper Pi"}}, "key": "1D6F1"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical italic capital rho", "alternative": "italic capital rho", "short": "italic cap rho"}, "mathspeak": {"default": "italic upper Rho"}}, "key": "1D6F2"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical italic capital theta symbol", "alternative": "italic capital theta", "short": "italic cap theta"}, "mathspeak": {"default": "italic upper Theta"}}, "key": "1D6F3"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical italic capital sigma", "alternative": "italic capital sigma", "short": "italic cap sigma"}, "mathspeak": {"default": "italic upper Sigma"}}, "key": "1D6F4"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical italic capital tau", "alternative": "italic capital tau", "short": "italic cap tau"}, "mathspeak": {"default": "italic upper Tau"}}, "key": "1D6F5"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical italic capital upsilon", "alternative": "italic capital upsilon", "short": "italic cap upsilon"}, "mathspeak": {"default": "italic upper Upsilon"}}, "key": "1D6F6"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical italic capital phi", "alternative": "italic capital phi", "short": "italic cap phi"}, "mathspeak": {"default": "italic upper Phi"}}, "key": "1D6F7"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical italic capital chi", "alternative": "italic capital chi", "short": "italic cap chi"}, "mathspeak": {"default": "italic upper Chi"}}, "key": "1D6F8"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical italic capital psi", "alternative": "italic capital psi", "short": "italic cap psi"}, "mathspeak": {"default": "italic upper Psi"}}, "key": "1D6F9"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical italic capital omega", "alternative": "italic capital omega", "short": "italic cap omega"}, "mathspeak": {"default": "italic upper Omega"}}, "key": "1D6FA"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical italic small alpha", "alternative": "italic small alpha", "short": "italic alpha"}}, "key": "1D6FC"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical italic small beta", "alternative": "italic small beta", "short": "italic beta"}}, "key": "1D6FD"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical italic small gamma", "alternative": "italic small gamma", "short": "italic gamma"}}, "key": "1D6FE"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical italic small delta", "alternative": "italic small delta", "short": "italic delta"}}, "key": "1D6FF"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical italic small epsilon", "alternative": "italic small epsilon", "short": "italic epsilon"}}, "key": "1D700"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical italic small zeta", "alternative": "italic small zeta", "short": "italic zeta"}}, "key": "1D701"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical italic small eta", "alternative": "italic small eta", "short": "italic eta"}}, "key": "1D702"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical italic small theta", "alternative": "italic small theta", "short": "italic theta"}}, "key": "1D703"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical italic small iota", "alternative": "italic small iota", "short": "italic iota"}}, "key": "1D704"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical italic small kappa", "alternative": "italic small kappa", "short": "italic kappa"}}, "key": "1D705"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical italic small lamda", "alternative": "italic small lamda", "short": "italic lamda"}}, "key": "1D706"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical italic small mu", "alternative": "italic small mu", "short": "italic mu"}}, "key": "1D707"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical italic small nu", "alternative": "italic small nu", "short": "italic nu"}}, "key": "1D708"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical italic small xi", "alternative": "italic small xi", "short": "italic xi"}}, "key": "1D709"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical italic small omicron", "alternative": "italic small omicron", "short": "italic omicron"}}, "key": "1D70A"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical italic small pi", "alternative": "italic small pi", "short": "italic pi"}}, "key": "1D70B"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical italic small rho", "alternative": "italic small rho", "short": "italic rho"}}, "key": "1D70C"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical italic small final sigma", "alternative": "italic small final sigma", "short": "italic final sigma"}}, "key": "1D70D"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical italic small sigma", "alternative": "italic small sigma", "short": "italic sigma"}}, "key": "1D70E"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical italic small tau", "alternative": "italic small tau", "short": "italic tau"}}, "key": "1D70F"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical italic small upsilon", "alternative": "italic small upsilon", "short": "italic upsilon"}}, "key": "1D710"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical italic small phi", "alternative": "italic small phi", "short": "italic phi"}}, "key": "1D711"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical italic small chi", "alternative": "italic small chi", "short": "italic chi"}}, "key": "1D712"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical italic small psi", "alternative": "italic small psi", "short": "italic psi"}}, "key": "1D713"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical italic small omega", "alternative": "italic small omega", "short": "italic omega"}}, "key": "1D714"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical sans serif bold capital alpha", "alternative": "sans serif bold capital alpha", "short": "sans serif bold cap alpha"}, "mathspeak": {"default": "sans serif bold upper Alpha"}}, "key": "1D756"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical sans serif bold capital beta", "alternative": "sans serif bold capital beta", "short": "sans serif bold cap beta"}, "mathspeak": {"default": "sans serif bold upper Beta"}}, "key": "1D757"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical sans serif bold capital gamma", "alternative": "sans serif bold capital gamma", "short": "sans serif bold cap gamma"}, "mathspeak": {"default": "sans serif bold upper Gamma"}}, "key": "1D758"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical sans serif bold capital delta", "alternative": "sans serif bold capital delta", "short": "sans serif bold cap delta"}, "mathspeak": {"default": "sans serif bold upper Delta"}}, "key": "1D759"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical sans serif bold capital epsilon", "alternative": "sans serif bold capital epsilon", "short": "sans serif bold cap epsilon"}, "mathspeak": {"default": "sans serif bold upper Epsilon"}}, "key": "1D75A"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical sans serif bold capital zeta", "alternative": "sans serif bold capital zeta", "short": "sans serif bold cap zeta"}, "mathspeak": {"default": "sans serif bold upper Zeta"}}, "key": "1D75B"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical sans serif bold capital eta", "alternative": "sans serif bold capital eta", "short": "sans serif bold cap eta"}, "mathspeak": {"default": "sans serif bold upper Eta"}}, "key": "1D75C"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical sans serif bold capital theta", "alternative": "sans serif bold capital theta", "short": "sans serif bold cap theta"}, "mathspeak": {"default": "sans serif bold upper Theta"}}, "key": "1D75D"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical sans serif bold capital iota", "alternative": "sans serif bold capital iota", "short": "sans serif bold cap iota"}, "mathspeak": {"default": "sans serif bold upper Iota"}}, "key": "1D75E"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical sans serif bold capital kappa", "alternative": "sans serif bold capital kappa", "short": "sans serif bold cap kappa"}, "mathspeak": {"default": "sans serif bold upper Kappa"}}, "key": "1D75F"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical sans serif bold capital lamda", "alternative": "sans serif bold capital lamda", "short": "sans serif bold cap lamda"}, "mathspeak": {"default": "sans serif bold upper Lamda"}}, "key": "1D760"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical sans serif bold capital mu", "alternative": "sans serif bold capital mu", "short": "sans serif bold cap mu"}, "mathspeak": {"default": "sans serif bold upper Mu"}}, "key": "1D761"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical sans serif bold capital nu", "alternative": "sans serif bold capital nu", "short": "sans serif bold cap nu"}, "mathspeak": {"default": "sans serif bold upper Nu"}}, "key": "1D762"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical sans serif bold capital xi", "alternative": "sans serif bold capital xi", "short": "sans serif bold cap xi"}, "mathspeak": {"default": "sans serif bold upper Xi"}}, "key": "1D763"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical sans serif bold capital omicron", "alternative": "sans serif bold capital omicron", "short": "sans serif bold cap omicron"}, "mathspeak": {"default": "sans serif bold upper Omicron"}}, "key": "1D764"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical sans serif bold capital pi", "alternative": "sans serif bold capital pi", "short": "sans serif bold cap pi"}, "mathspeak": {"default": "sans serif bold upper Pi"}}, "key": "1D765"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical sans serif bold capital rho", "alternative": "sans serif bold capital rho", "short": "sans serif bold cap rho"}, "mathspeak": {"default": "sans serif bold upper Rho"}}, "key": "1D766"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical sans serif bold capital theta symbol", "alternative": "sans serif bold capital theta", "short": "sans serif bold cap theta"}, "mathspeak": {"default": "sans serif bold upper Theta"}}, "key": "1D767"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical sans serif bold capital sigma", "alternative": "sans serif bold capital sigma", "short": "sans serif bold cap sigma"}, "mathspeak": {"default": "sans serif bold upper Sigma"}}, "key": "1D768"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical sans serif bold capital tau", "alternative": "sans serif bold capital tau", "short": "sans serif bold cap tau"}, "mathspeak": {"default": "sans serif bold upper Tau"}}, "key": "1D769"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical sans serif bold capital upsilon", "alternative": "sans serif bold capital upsilon", "short": "sans serif bold cap upsilon"}, "mathspeak": {"default": "sans serif bold upper Upsilon"}}, "key": "1D76A"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical sans serif bold capital phi", "alternative": "sans serif bold capital phi", "short": "sans serif bold cap phi"}, "mathspeak": {"default": "sans serif bold upper Phi"}}, "key": "1D76B"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical sans serif bold capital chi", "alternative": "sans serif bold capital chi", "short": "sans serif bold cap chi"}, "mathspeak": {"default": "sans serif bold upper Chi"}}, "key": "1D76C"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical sans serif bold capital psi", "alternative": "sans serif bold capital psi", "short": "sans serif bold cap psi"}, "mathspeak": {"default": "sans serif bold upper Psi"}}, "key": "1D76D"}, {"category": "<PERSON>", "mappings": {"default": {"default": "mathematical sans serif bold capital omega", "alternative": "sans serif bold capital omega", "short": "sans serif bold cap omega"}, "mathspeak": {"default": "sans serif bold upper Omega"}}, "key": "1D76E"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical sans serif bold small alpha", "alternative": "sans serif bold small alpha", "short": "sans serif bold alpha"}}, "key": "1D770"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical sans serif bold small beta", "alternative": "sans serif bold small beta", "short": "sans serif bold beta"}}, "key": "1D771"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical sans serif bold small gamma", "alternative": "sans serif bold small gamma", "short": "sans serif bold gamma"}}, "key": "1D772"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical sans serif bold small delta", "alternative": "sans serif bold small delta", "short": "sans serif bold delta"}}, "key": "1D773"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical sans serif bold small epsilon", "alternative": "sans serif bold small epsilon", "short": "sans serif bold epsilon"}}, "key": "1D774"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical sans serif bold small zeta", "alternative": "sans serif bold small zeta", "short": "sans serif bold zeta"}}, "key": "1D775"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical sans serif bold small eta", "alternative": "sans serif bold small eta", "short": "sans serif bold eta"}}, "key": "1D776"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical sans serif bold small theta", "alternative": "sans serif bold small theta", "short": "sans serif bold theta"}}, "key": "1D777"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical sans serif bold small iota", "alternative": "sans serif bold small iota", "short": "sans serif bold iota"}}, "key": "1D778"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical sans serif bold small kappa", "alternative": "sans serif bold small kappa", "short": "sans serif bold kappa"}}, "key": "1D779"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical sans serif bold small lamda", "alternative": "sans serif bold small lamda", "short": "sans serif bold lamda"}}, "key": "1D77A"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical sans serif bold small mu", "alternative": "sans serif bold small mu", "short": "sans serif bold mu"}}, "key": "1D77B"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical sans serif bold small nu", "alternative": "sans serif bold small nu", "short": "sans serif bold nu"}}, "key": "1D77C"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical sans serif bold small xi", "alternative": "sans serif bold small xi", "short": "sans serif bold xi"}}, "key": "1D77D"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical sans serif bold small omicron", "alternative": "sans serif bold small omicron", "short": "sans serif bold omicron"}}, "key": "1D77E"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical sans serif bold small pi", "alternative": "sans serif bold small pi", "short": "sans serif bold pi"}}, "key": "1D77F"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical sans serif bold small rho", "alternative": "sans serif bold small rho", "short": "sans serif bold rho"}}, "key": "1D780"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical sans serif bold small final sigma", "alternative": "sans serif bold small final sigma", "short": "sans serif bold final sigma"}}, "key": "1D781"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical sans serif bold small sigma", "alternative": "sans serif bold small sigma", "short": "sans serif bold sigma"}}, "key": "1D782"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical sans serif bold small tau", "alternative": "sans serif bold small tau", "short": "sans serif bold tau"}}, "key": "1D783"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical sans serif bold small upsilon", "alternative": "sans serif bold small upsilon", "short": "sans serif bold upsilon"}}, "key": "1D784"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical sans serif bold small phi", "alternative": "sans serif bold small phi", "short": "sans serif bold phi"}}, "key": "1D785"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical sans serif bold small chi", "alternative": "sans serif bold small chi", "short": "sans serif bold chi"}}, "key": "1D786"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical sans serif bold small psi", "alternative": "sans serif bold small psi", "short": "sans serif bold psi"}}, "key": "1D787"}, {"category": "Ll", "mappings": {"default": {"default": "mathematical sans serif bold small omega", "alternative": "sans serif bold small omega", "short": "sans serif bold omega"}}, "key": "1D788"}]