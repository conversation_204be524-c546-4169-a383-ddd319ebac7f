/*
 *  /MathJax/jax/output/HTML-CSS/fonts/Asana-Math/Size2/Regular/Main.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.OutputJax["HTML-CSS"].FONTDATA.FONTS.AsanaMathJax_Size2={directory:"Size2/Regular",family:"AsanaMathJax_Size2",testString:"\u0302\u0303\u0305\u0306\u030C\u0332\u0333\u033F\u2016\u2044\u2045\u2046\u20D6\u20D7\u220F",32:[0,0,249,0,0],40:[1266,775,427,84,388],41:[1266,775,427,40,344],91:[1363,682,371,84,342],93:[1363,682,371,84,342],123:[1357,684,468,51,419],124:[1048,507,258,86,173],125:[1357,684,468,50,418],770:[783,-627,633,0,633],771:[772,-642,1052,0,1053],773:[587,-542,674,0,675],774:[664,-506,541,0,542],780:[787,-627,1104,0,1105],818:[-130,175,674,0,675],819:[-130,283,674,0,675],831:[695,-542,674,0,675],8214:[1048,507,495,86,410],8260:[875,596,431,-119,433],8261:[1202,660,369,53,314],8262:[1202,660,377,19,280],8406:[790,-519,1127,0,1127],8407:[790,-519,1127,0,1127],8719:[1297,645,2061,112,1951],8720:[1297,645,2061,112,1951],8721:[1286,642,1763,128,1634],8730:[1912,0,866,63,899],8745:[1382,863,1705,100,1605],8747:[1808,903,1360,54,1361],8748:[1808,903,2254,54,2105],8749:[1808,903,2998,54,2849],8750:[1808,903,1510,54,1361],8751:[1808,903,2254,54,2105],8752:[1808,903,2998,54,2849],8753:[1808,903,1509,54,1360],8754:[1808,903,1566,110,1417],8755:[1808,903,1510,54,1361],8896:[1726,862,1677,85,1592],8897:[1726,862,1677,85,1592],8898:[1725,863,1860,178,1684],8899:[1382,863,1705,100,1605],8968:[1361,680,390,84,346],8969:[1361,680,390,84,346],8970:[1361,680,390,84,346],8971:[1361,680,390,84,346],9140:[755,-497,1352,0,1353],9141:[-217,475,1352,0,1353],9180:[835,-531,1348,0,1349],9181:[-531,835,1348,0,1349],9182:[908,-540,2142,51,2092],9183:[-540,908,2142,51,2092],9184:[755,-545,2055,0,2056],9185:[-545,755,2055,0,2056],10181:[1036,495,450,53,397],10182:[1003,528,450,53,397],10214:[1023,512,513,84,483],10215:[1023,512,513,84,483],10216:[1362,680,455,53,403],10217:[1362,680,455,53,403],10218:[1362,680,645,53,593],10219:[1362,680,645,53,593],10748:[1262,631,554,50,505],10749:[1262,631,554,49,505],10752:[1584,792,2737,179,2560],10753:[1584,792,2737,179,2560],10754:[1584,792,2737,179,2560],10755:[1725,863,1860,179,1683],10756:[1725,863,1860,178,1684],10757:[1700,852,1860,179,1683],10758:[1700,852,1860,179,1683],10759:[1725,863,1962,86,1877],10760:[1725,863,1962,86,1877],10761:[1279,641,2277,179,2101],10764:[1808,903,3760,54,3611],10765:[1808,903,1510,54,1361],10766:[1808,903,1510,54,1361],10767:[1808,903,1510,54,1361],10768:[1808,903,1510,54,1361],10769:[1808,903,1596,54,1447],10770:[1808,903,1510,54,1361],10771:[1808,903,1510,54,1361],10772:[1808,903,1510,54,1361],10773:[1808,903,1510,54,1361],10774:[1808,903,1510,54,1361],10775:[1808,903,1958,54,1859],10776:[1808,903,1510,54,1361],10777:[1808,903,1510,54,1361],10778:[1808,903,1510,54,1361],10779:[2030,903,1524,54,1375],10780:[2030,903,1524,54,1375]};MathJax.Callback.Queue(["initFont",MathJax.OutputJax["HTML-CSS"],"AsanaMathJax_Size2"],["loadComplete",MathJax.Ajax,MathJax.OutputJax["HTML-CSS"].fontDir+"/Size2/Regular/Main.js"]);
