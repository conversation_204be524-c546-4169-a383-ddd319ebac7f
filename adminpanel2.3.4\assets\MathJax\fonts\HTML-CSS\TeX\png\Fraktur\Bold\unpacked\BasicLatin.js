/*************************************************************
 *
 *  MathJax/fonts/HTML-CSS/TeX/png/Fraktur/Bold/BasicLatin.js
 *  
 *  Defines the image size data needed for the HTML-CSS OutputJax
 *  to display mathematics using fallback images when the fonts
 *  are not available to the client browser.
 *
 *  ---------------------------------------------------------------------
 *
 *  Copyright (c) 2009-2013 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 *
 */

MathJax.OutputJax["HTML-CSS"].defineImageData({
  "MathJax_Fraktur-bold": {
    0x20: [  // SPACE
      [1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],
      [1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0]
    ],
    0x21: [  // EXCLAMATION MARK
      [2,4,0],[3,6,0],[3,7,0],[3,9,0],[4,10,0],[5,12,0],[5,13,0],[6,17,1],
      [7,20,1],[8,24,1],[10,29,1],[12,33,1],[14,39,1],[16,46,1]
    ],
    0x22: [  // QUOTATION MARK
      [2,2,-2],[2,3,-3],[3,3,-4],[3,4,-5],[4,4,-6],[4,5,-7],[5,6,-7],[6,7,-9],
      [7,8,-11],[8,9,-14],[10,11,-17],[11,13,-19],[13,16,-23],[16,18,-28]
    ],
    0x26: [  // AMPERSAND
      [6,4,0],[7,6,0],[9,7,0],[10,9,0],[12,10,0],[14,12,0],[17,13,0],[20,17,1],
      [24,20,1],[28,24,1],[33,29,1],[39,33,1],[47,39,1],[55,47,1]
    ],
    0x27: [  // APOSTROPHE
      [2,2,-2],[2,3,-3],[2,3,-4],[2,4,-5],[3,4,-6],[3,5,-7],[4,5,-8],[4,7,-9],
      [5,8,-11],[6,9,-14],[7,11,-17],[8,13,-19],[9,15,-24],[11,18,-29]
    ],
    0x28: [  // LEFT PARENTHESIS
      [3,6,1],[3,8,2],[4,10,2],[5,12,3],[5,14,3],[6,16,4],[7,18,4],[9,22,4],
      [10,26,5],[12,32,6],[14,37,7],[17,43,9],[20,51,10],[23,61,12]
    ],
    0x29: [  // RIGHT PARENTHESIS
      [3,6,1],[3,8,2],[4,10,2],[4,12,3],[5,14,3],[6,16,4],[7,18,4],[8,22,5],
      [10,27,6],[11,32,6],[13,38,8],[16,44,9],[19,52,11],[22,61,12]
    ],
    0x2A: [  // ASTERISK
      [2,2,-2],[3,3,-3],[3,3,-4],[4,4,-5],[4,4,-6],[5,5,-7],[6,5,-8],[7,6,-10],
      [8,7,-12],[10,9,-14],[11,10,-18],[13,12,-20],[16,14,-24],[19,16,-29]
    ],
    0x2B: [  // PLUS SIGN
      [6,5,1],[7,7,1],[9,9,2],[10,9,1],[12,11,2],[14,13,2],[17,14,1],[20,17,2],
      [24,20,3],[28,24,4],[33,28,3],[39,32,4],[47,38,5],[56,46,6]
    ],
    0x2C: [  // COMMA
      [2,2,1],[3,3,2],[3,3,2],[3,4,3],[4,5,3],[5,5,4],[5,6,4],[6,7,5],
      [7,8,5],[9,10,6],[10,12,8],[12,14,9],[14,17,11],[17,20,13]
    ],
    0x2D: [  // HYPHEN-MINUS
      [6,1,-1],[7,1,-2],[9,1,-2],[10,1,-3],[12,1,-3],[14,1,-4],[17,1,-4],[20,1,-5],
      [24,2,-6],[28,3,-7],[33,2,-10],[39,2,-11],[47,3,-13],[55,3,-16]
    ],
    0x2E: [  // FULL STOP
      [2,1,0],[2,1,0],[3,2,0],[3,2,0],[4,2,0],[4,2,0],[5,3,0],[6,3,1],
      [7,4,1],[8,4,1],[10,5,1],[12,6,1],[14,7,1],[16,8,1]
    ],
    0x2F: [  // SOLIDUS
      [4,6,1],[5,8,2],[6,10,2],[7,12,2],[8,14,3],[10,16,3],[11,17,3],[13,21,4],
      [16,25,5],[19,31,6],[22,37,7],[26,42,8],[31,50,10],[37,60,12]
    ],
    0x30: [  // DIGIT ZERO
      [4,4,0],[5,5,0],[6,6,0],[7,7,0],[8,8,0],[9,9,0],[11,10,0],[13,12,1],
      [15,15,1],[18,18,1],[21,21,0],[25,24,1],[30,29,1],[36,34,1]
    ],
    0x31: [  // DIGIT ONE
      [4,3,0],[5,4,0],[6,5,0],[7,6,0],[8,7,0],[10,8,0],[11,9,0],[13,11,0],
      [16,14,1],[19,17,1],[22,20,1],[26,23,0],[31,27,0],[37,32,0]
    ],
    0x32: [  // DIGIT TWO
      [4,3,0],[5,4,0],[6,6,1],[7,7,1],[8,8,1],[10,8,0],[11,9,0],[14,12,1],
      [16,13,0],[19,17,0],[23,20,0],[27,23,0],[32,27,0],[38,32,0]
    ],
    0x33: [  // DIGIT THREE
      [4,4,1],[5,6,2],[6,7,2],[7,9,3],[8,10,3],[9,12,4],[11,13,4],[13,16,5],
      [15,19,6],[18,23,7],[21,28,8],[25,32,9],[29,38,11],[35,45,13]
    ],
    0x34: [  // DIGIT FOUR
      [4,4,1],[5,6,2],[6,7,2],[7,9,3],[8,10,3],[10,12,4],[12,13,4],[14,16,5],
      [16,19,6],[19,24,7],[23,28,8],[27,33,10],[32,38,11],[38,46,14]
    ],
    0x35: [  // DIGIT FIVE
      [4,4,1],[5,6,2],[6,7,2],[7,9,3],[8,10,3],[9,12,4],[11,13,4],[13,16,5],
      [15,19,6],[18,24,8],[21,27,8],[25,31,9],[29,37,10],[35,44,12]
    ],
    0x36: [  // DIGIT SIX
      [4,4,0],[5,6,0],[6,7,0],[7,9,0],[8,10,0],[10,12,0],[11,13,0],[13,17,1],
      [16,20,1],[19,24,1],[22,29,0],[26,34,1],[31,40,1],[37,47,1]
    ],
    0x37: [  // DIGIT SEVEN
      [5,4,1],[5,6,2],[6,7,2],[7,9,3],[9,10,3],[10,12,4],[12,13,4],[14,16,5],
      [17,19,6],[20,23,7],[24,27,8],[28,32,10],[33,38,12],[39,45,13]
    ],
    0x38: [  // DIGIT EIGHT
      [4,5,0],[5,6,0],[6,8,0],[7,9,0],[8,11,0],[9,12,0],[11,14,0],[13,17,1],
      [15,21,1],[18,24,0],[22,29,0],[26,34,0],[30,40,0],[36,47,0]
    ],
    0x39: [  // DIGIT NINE
      [4,4,1],[5,6,2],[6,7,2],[7,9,3],[8,10,3],[10,12,4],[11,13,4],[13,16,5],
      [16,19,6],[19,23,6],[22,28,8],[26,32,9],[31,38,11],[37,45,13]
    ],
    0x3A: [  // COLON
      [2,3,0],[2,4,0],[2,5,0],[3,6,0],[3,7,0],[4,8,0],[4,9,0],[5,12,1],
      [6,14,1],[7,17,1],[8,19,0],[10,22,1],[11,26,1],[13,31,1]
    ],
    0x3B: [  // SEMICOLON
      [2,5,2],[2,6,2],[3,7,2],[3,9,3],[3,10,3],[4,11,3],[5,13,4],[5,15,4],
      [6,19,6],[7,23,7],[9,27,8],[10,31,9],[12,37,11],[14,43,12]
    ],
    0x3D: [  // EQUALS SIGN
      [4,2,-1],[5,2,-1],[6,2,-1],[7,3,-2],[8,3,-2],[10,3,-3],[11,4,-3],[13,4,-4],
      [16,6,-4],[19,8,-5],[22,7,-7],[26,8,-8],[31,10,-9],[37,12,-11]
    ],
    0x3F: [  // QUESTION MARK
      [3,4,0],[4,6,0],[5,7,0],[5,9,0],[6,10,0],[7,12,0],[9,13,0],[10,17,1],
      [12,20,1],[14,24,1],[17,29,1],[20,33,1],[24,39,1],[28,47,1]
    ],
    0x41: [  // LATIN CAPITAL LETTER A
      [6,5,1],[7,7,1],[9,8,1],[10,10,1],[12,11,1],[14,14,2],[17,15,2],[20,18,2],
      [23,21,2],[28,25,2],[33,30,2],[39,34,2],[46,41,3],[55,48,3]
    ],
    0x42: [  // LATIN CAPITAL LETTER B
      [7,4,0],[8,6,0],[10,7,0],[12,9,0],[14,10,0],[17,13,1],[19,14,1],[23,17,1],
      [27,20,1],[32,24,1],[38,29,1],[45,33,1],[54,40,2],[64,47,2]
    ],
    0x43: [  // LATIN CAPITAL LETTER C
      [6,4,0],[7,6,0],[8,7,0],[9,9,0],[11,10,0],[13,13,1],[15,14,1],[17,17,1],
      [21,20,1],[24,24,1],[29,29,1],[34,33,1],[41,40,2],[48,47,2]
    ],
    0x44: [  // LATIN CAPITAL LETTER D
      [7,4,0],[8,6,0],[9,7,0],[11,9,0],[13,10,0],[15,13,1],[18,14,1],[21,17,1],
      [25,20,1],[30,24,1],[36,29,1],[42,33,1],[50,40,2],[59,47,2]
    ],
    0x45: [  // LATIN CAPITAL LETTER E
      [6,4,0],[7,6,0],[8,7,0],[9,9,0],[11,10,0],[13,13,1],[15,14,1],[17,17,1],
      [21,20,1],[24,24,1],[29,29,1],[34,33,1],[41,40,2],[48,47,2]
    ],
    0x46: [  // LATIN CAPITAL LETTER F
      [5,6,2],[6,7,1],[8,9,2],[9,11,2],[10,12,2],[12,14,2],[15,16,3],[17,19,3],
      [20,23,4],[24,29,6],[29,34,6],[34,39,7],[40,46,8],[48,55,10]
    ],
    0x47: [  // LATIN CAPITAL LETTER G
      [6,4,0],[8,6,0],[9,7,0],[10,9,0],[12,10,0],[15,12,0],[17,13,0],[20,17,1],
      [24,20,1],[28,24,1],[34,29,1],[40,33,1],[47,39,1],[56,47,2]
    ],
    0x48: [  // LATIN CAPITAL LETTER H
      [6,5,1],[7,7,1],[8,9,2],[9,11,2],[11,12,2],[13,14,2],[15,16,3],[18,20,4],
      [21,23,4],[25,27,4],[30,33,5],[35,38,6],[42,45,7],[50,53,8]
    ],
    0x49: [  // LATIN CAPITAL LETTER I
      [5,4,0],[6,6,0],[7,7,0],[8,9,0],[9,10,0],[11,12,0],[13,13,0],[15,17,1],
      [18,20,1],[21,24,1],[25,29,1],[29,33,1],[35,39,1],[41,47,2]
    ],
    0x4A: [  // LATIN CAPITAL LETTER J
      [6,6,2],[7,7,1],[7,9,2],[9,11,2],[10,12,2],[12,14,2],[13,16,3],[16,19,3],
      [18,24,5],[22,29,6],[25,33,5],[30,38,6],[35,46,8],[42,54,9]
    ],
    0x4B: [  // LATIN CAPITAL LETTER K
      [6,4,0],[7,6,0],[8,7,0],[10,9,0],[12,10,0],[14,12,0],[16,13,0],[19,17,1],
      [23,20,1],[27,24,1],[32,29,1],[38,33,1],[45,39,1],[54,47,2]
    ],
    0x4C: [  // LATIN CAPITAL LETTER L
      [6,4,0],[7,6,0],[8,7,0],[9,9,0],[11,10,0],[13,12,0],[15,13,0],[18,17,1],
      [22,20,1],[26,24,1],[30,29,1],[36,33,1],[43,39,1],[51,47,2]
    ],
    0x4D: [  // LATIN CAPITAL LETTER M
      [9,6,1],[11,7,1],[13,7,0],[15,9,0],[17,10,0],[21,13,1],[24,14,1],[29,17,1],
      [34,20,1],[41,24,1],[49,29,1],[58,33,1],[68,40,2],[81,47,2]
    ],
    0x4E: [  // LATIN CAPITAL LETTER N
      [7,5,1],[9,7,1],[10,8,1],[12,10,1],[14,11,1],[17,13,1],[19,14,1],[23,18,2],
      [27,21,2],[32,25,2],[39,30,2],[46,34,2],[54,40,2],[64,48,3]
    ],
    0x4F: [  // LATIN CAPITAL LETTER O
      [7,5,0],[8,7,0],[9,8,0],[11,10,0],[13,11,0],[15,14,1],[18,15,1],[21,19,1],
      [25,21,1],[29,26,1],[35,31,1],[41,35,1],[49,42,2],[58,50,2]
    ],
    0x50: [  // LATIN CAPITAL LETTER P
      [7,6,2],[8,8,2],[10,10,3],[12,12,3],[14,14,4],[16,16,4],[19,18,5],[22,22,6],
      [27,26,7],[31,31,8],[37,38,10],[44,43,11],[53,51,13],[62,61,16]
    ],
    0x51: [  // LATIN CAPITAL LETTER Q
      [7,6,1],[8,8,1],[10,9,1],[11,11,1],[13,12,1],[16,15,2],[18,16,2],[22,21,3],
      [26,23,3],[31,28,3],[36,34,4],[43,38,4],[51,45,5],[61,54,6]
    ],
    0x52: [  // LATIN CAPITAL LETTER R
      [7,5,1],[9,6,0],[10,8,1],[12,10,1],[14,11,1],[17,13,1],[20,14,1],[23,17,1],
      [28,20,1],[33,25,2],[39,29,1],[46,34,2],[55,40,2],[65,47,2]
    ],
    0x53: [  // LATIN CAPITAL LETTER S
      [7,4,0],[8,6,0],[9,7,0],[11,9,0],[13,10,0],[16,13,1],[18,14,1],[22,17,1],
      [26,20,1],[30,24,1],[36,29,1],[43,33,1],[51,40,2],[60,47,2]
    ],
    0x54: [  // LATIN CAPITAL LETTER T
      [6,5,0],[7,7,0],[8,8,0],[10,10,0],[12,11,0],[14,14,1],[16,15,1],[19,18,1],
      [23,21,1],[27,25,1],[32,30,1],[38,34,1],[45,41,2],[53,48,2]
    ],
    0x55: [  // LATIN CAPITAL LETTER U
      [7,5,0],[8,7,0],[9,8,0],[11,10,0],[13,11,0],[15,13,0],[17,14,0],[21,18,1],
      [25,21,1],[29,25,1],[35,30,1],[41,34,1],[49,40,1],[58,49,3]
    ],
    0x56: [  // LATIN CAPITAL LETTER V
      [7,4,0],[8,6,0],[10,7,0],[12,9,0],[14,10,0],[17,13,1],[19,14,1],[23,17,1],
      [27,20,1],[32,24,1],[38,29,1],[45,33,1],[54,40,2],[64,47,2]
    ],
    0x57: [  // LATIN CAPITAL LETTER W
      [9,4,0],[11,7,0],[13,8,0],[15,10,0],[18,11,0],[21,14,1],[25,15,1],[29,18,1],
      [35,21,1],[41,25,1],[49,30,1],[58,34,1],[69,41,2],[82,48,2]
    ],
    0x58: [  // LATIN CAPITAL LETTER X
      [6,4,0],[7,6,0],[9,7,0],[10,9,0],[12,10,0],[14,13,1],[17,14,1],[20,17,1],
      [24,20,1],[28,24,1],[33,29,1],[39,33,1],[47,40,2],[55,48,3]
    ],
    0x59: [  // LATIN CAPITAL LETTER Y
      [7,6,1],[8,8,2],[9,10,2],[11,13,3],[13,14,3],[15,16,4],[18,18,4],[21,22,5],
      [25,26,6],[29,31,7],[35,37,8],[41,43,10],[49,51,12],[58,60,14]
    ],
    0x5A: [  // LATIN CAPITAL LETTER Z
      [6,6,2],[7,7,1],[8,9,2],[9,11,2],[10,12,2],[12,14,2],[14,16,3],[16,20,4],
      [19,23,4],[22,28,5],[26,33,5],[30,39,7],[36,46,8],[43,55,10]
    ],
    0x5B: [  // LEFT SQUARE BRACKET
      [2,6,1],[2,8,1],[3,10,1],[3,12,2],[4,13,2],[4,15,2],[5,17,2],[6,20,3],
      [7,24,4],[8,29,4],[9,35,5],[11,41,6],[13,49,7],[15,57,9]
    ],
    0x5D: [  // RIGHT SQUARE BRACKET
      [2,6,1],[2,8,1],[3,10,2],[3,12,2],[3,14,2],[4,15,2],[5,17,3],[5,21,3],
      [6,26,5],[7,31,5],[9,36,6],[10,42,7],[12,49,8],[14,58,9]
    ],
    0x5E: [  // CIRCUMFLEX ACCENT
      [5,2,-3],[5,3,-4],[6,3,-5],[7,4,-6],[9,4,-7],[10,5,-8],[12,6,-9],[14,7,-11],
      [17,8,-12],[20,10,-15],[23,12,-18],[28,13,-21],[33,16,-25],[39,19,-30]
    ],
    0x61: [  // LATIN SMALL LETTER A
      [5,5,1],[5,6,1],[6,7,1],[7,8,1],[9,9,1],[10,10,1],[12,11,1],[14,13,1],
      [17,15,1],[20,18,1],[23,22,2],[28,25,2],[33,29,2],[39,34,2]
    ],
    0x62: [  // LATIN SMALL LETTER B
      [4,5,1],[5,7,1],[5,8,1],[6,10,1],[7,11,1],[9,13,1],[10,14,1],[12,17,1],
      [14,20,1],[17,25,2],[20,30,2],[24,34,2],[28,40,2],[34,48,3]
    ],
    0x63: [  // LATIN SMALL LETTER C
      [3,4,0],[4,6,1],[5,6,1],[5,7,1],[6,9,1],[7,9,1],[9,11,1],[10,13,1],
      [12,14,1],[14,18,1],[17,21,1],[20,24,2],[24,28,2],[28,34,3]
    ],
    0x64: [  // LATIN SMALL LETTER D
      [4,5,0],[6,6,0],[7,8,1],[7,9,1],[9,11,1],[10,12,1],[11,14,1],[13,16,1],
      [16,20,2],[18,23,1],[21,27,2],[25,32,2],[30,38,2],[35,44,2]
    ],
    0x65: [  // LATIN SMALL LETTER E
      [3,4,0],[4,5,0],[5,6,0],[6,7,0],[6,8,0],[8,9,0],[9,10,0],[10,12,1],
      [12,14,1],[15,17,1],[17,21,1],[20,24,1],[24,28,1],[29,33,2]
    ],
    0x66: [  // LATIN SMALL LETTER F
      [3,5,1],[4,8,2],[4,9,2],[5,12,3],[6,13,3],[7,16,4],[8,17,4],[9,21,5],
      [11,25,6],[13,30,7],[15,37,9],[18,43,11],[21,50,12],[25,60,15]
    ],
    0x67: [  // LATIN SMALL LETTER G
      [4,4,1],[5,6,2],[6,7,2],[7,9,3],[8,10,3],[9,12,4],[11,13,4],[13,16,5],
      [15,19,6],[18,23,7],[22,27,8],[26,32,10],[30,38,12],[36,45,14]
    ],
    0x68: [  // LATIN SMALL LETTER H
      [4,7,2],[5,9,2],[5,10,2],[6,13,3],[8,14,3],[9,17,4],[10,18,4],[12,23,6],
      [15,27,7],[17,32,8],[20,38,9],[24,43,10],[29,51,12],[34,60,15]
    ],
    0x69: [  // LATIN SMALL LETTER I
      [3,5,0],[3,7,0],[4,8,0],[4,9,0],[5,11,0],[6,12,0],[7,14,0],[8,17,1],
      [10,20,1],[11,24,1],[13,29,1],[16,34,1],[19,40,1],[22,47,2]
    ],
    0x6A: [  // LATIN SMALL LETTER J
      [3,6,1],[3,8,2],[4,10,2],[4,12,3],[5,14,3],[5,15,4],[6,17,4],[7,21,5],
      [8,25,6],[9,30,7],[11,36,8],[13,42,10],[16,49,12],[18,58,13]
    ],
    0x6B: [  // LATIN SMALL LETTER K
      [3,6,1],[4,8,1],[5,9,1],[6,10,0],[6,12,1],[8,13,0],[9,15,1],[11,18,1],
      [12,21,1],[15,25,1],[17,29,0],[21,34,2],[24,39,1],[29,47,2]
    ],
    0x6C: [  // LATIN SMALL LETTER L
      [3,6,1],[3,7,0],[4,9,1],[4,10,1],[5,11,1],[6,13,1],[7,14,1],[8,17,1],
      [9,20,1],[11,25,2],[13,29,1],[15,33,1],[18,40,2],[21,47,2]
    ],
    0x6D: [  // LATIN SMALL LETTER M
      [7,5,1],[8,6,1],[9,7,1],[11,8,1],[13,9,1],[15,10,1],[18,11,1],[21,14,2],
      [25,16,2],[30,19,2],[36,22,2],[42,25,2],[50,29,2],[60,35,3]
    ],
    0x6E: [  // LATIN SMALL LETTER N
      [5,4,1],[6,5,1],[6,6,1],[8,7,1],[9,8,1],[11,9,1],[12,10,1],[15,13,2],
      [17,15,2],[21,18,2],[24,21,2],[29,24,2],[34,28,2],[41,34,3]
    ],
    0x6F: [  // LATIN SMALL LETTER O
      [4,4,0],[5,5,0],[6,6,0],[7,7,0],[8,8,0],[9,9,0],[11,10,0],[12,13,1],
      [15,15,1],[17,18,1],[21,21,1],[24,25,1],[29,29,1],[34,35,3]
    ],
    0x70: [  // LATIN SMALL LETTER P
      [4,5,1],[6,7,2],[7,8,2],[8,10,3],[9,12,3],[10,13,4],[12,15,4],[14,18,5],
      [16,21,6],[19,26,7],[22,31,8],[26,36,10],[30,42,12],[36,50,14]
    ],
    0x71: [  // LATIN SMALL LETTER Q
      [4,6,2],[5,7,2],[6,8,2],[7,10,3],[8,11,3],[9,14,5],[11,15,5],[12,18,6],
      [15,20,6],[17,25,8],[21,29,9],[24,33,10],[29,39,12],[34,46,14]
    ],
    0x72: [  // LATIN SMALL LETTER R
      [4,4,1],[4,5,0],[5,7,1],[6,8,1],[7,8,1],[8,9,1],[9,10,1],[11,13,1],
      [13,14,1],[15,18,2],[18,21,1],[22,24,2],[26,29,2],[30,34,2]
    ],
    0x73: [  // LATIN SMALL LETTER S
      [5,5,1],[6,6,1],[6,7,1],[7,7,1],[8,9,1],[10,10,1],[11,11,1],[13,13,1],
      [15,16,2],[18,19,2],[21,22,2],[24,25,2],[29,30,3],[35,35,3]
    ],
    0x74: [  // LATIN SMALL LETTER T
      [3,6,1],[4,6,0],[4,8,0],[5,9,1],[6,11,1],[7,12,1],[8,14,1],[10,17,1],
      [12,19,1],[14,24,2],[16,28,2],[19,32,1],[23,38,2],[27,45,2]
    ],
    0x75: [  // LATIN SMALL LETTER U
      [5,4,1],[5,4,0],[6,6,1],[8,7,1],[9,8,1],[10,9,1],[12,10,1],[14,13,2],
      [17,15,2],[20,18,2],[24,21,2],[29,24,2],[34,28,2],[40,34,3]
    ],
    0x76: [  // LATIN SMALL LETTER V
      [4,4,0],[5,5,0],[5,6,0],[6,8,0],[8,9,0],[9,10,0],[10,11,0],[12,14,1],
      [15,16,1],[17,20,1],[20,23,1],[24,27,1],[29,32,1],[34,38,2]
    ],
    0x77: [  // LATIN SMALL LETTER W
      [6,4,0],[7,5,0],[8,6,0],[10,8,0],[12,9,0],[14,10,1],[16,11,1],[19,14,1],
      [23,16,1],[27,20,1],[32,23,1],[38,27,1],[46,32,2],[54,38,2]
    ],
    0x78: [  // LATIN SMALL LETTER X
      [4,6,2],[4,7,2],[5,8,2],[6,10,3],[7,11,3],[8,12,3],[9,14,4],[11,17,5],
      [13,20,6],[15,24,7],[18,28,8],[21,32,9],[25,37,10],[30,45,13]
    ],
    0x79: [  // LATIN SMALL LETTER Y
      [4,6,1],[5,7,2],[6,9,2],[7,11,3],[8,12,3],[9,14,4],[10,16,4],[12,19,5],
      [15,22,6],[17,27,7],[21,32,9],[24,37,10],[29,44,12],[34,52,15]
    ],
    0x7A: [  // LATIN SMALL LETTER Z
      [4,5,1],[5,6,2],[5,8,2],[6,9,3],[7,10,3],[8,12,4],[9,13,4],[10,16,5],
      [12,19,6],[14,24,7],[16,28,8],[19,33,10],[22,38,12],[26,46,14]
    ]
  }
});

MathJax.Ajax.loadComplete(MathJax.OutputJax["HTML-CSS"].imgDir+"/Fraktur/Bold"+
                          MathJax.OutputJax["HTML-CSS"].imgPacked+"/BasicLatin.js");
