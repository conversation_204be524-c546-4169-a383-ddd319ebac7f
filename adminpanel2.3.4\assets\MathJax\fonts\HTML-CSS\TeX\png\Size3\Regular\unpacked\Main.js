/*************************************************************
 *
 *  MathJax/fonts/HTML-CSS/TeX/png/Size3/Regular/Main.js
 *  
 *  Defines the image size data needed for the HTML-CSS OutputJax
 *  to display mathematics using fallback images when the fonts
 *  are not available to the client browser.
 *
 *  ---------------------------------------------------------------------
 *
 *  Copyright (c) 2009-2013 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 *
 */

MathJax.OutputJax["HTML-CSS"].defineImageData({
  "MathJax_Size3": {
    0x20: [  // SPACE
      [1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],
      [1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0]
    ],
    0x28: [  // LEFT PARENTHESIS
      [5,17,7],[6,20,8],[7,24,9],[9,29,11],[10,34,13],[12,40,16],[14,47,18],[17,56,22],
      [20,67,26],[24,79,31],[28,94,37],[33,112,44],[39,133,52],[47,158,62]
    ],
    0x29: [  // RIGHT PARENTHESIS
      [4,17,7],[5,20,8],[6,24,9],[7,29,11],[8,34,13],[9,40,16],[11,47,18],[13,56,22],
      [15,67,26],[18,79,31],[21,94,37],[25,112,44],[30,133,52],[35,158,62]
    ],
    0x2F: [  // SOLIDUS
      [7,17,7],[9,20,8],[10,24,9],[12,28,11],[14,33,13],[17,40,16],[20,47,18],[23,56,22],
      [28,67,26],[33,79,31],[39,94,37],[46,112,44],[55,133,52],[66,158,62]
    ],
    0x5B: [  // LEFT SQUARE BRACKET
      [4,17,7],[5,20,8],[6,24,10],[7,28,11],[8,33,13],[9,41,17],[11,48,19],[12,57,23],
      [15,66,26],[17,79,31],[21,94,37],[24,111,44],[29,133,52],[34,158,62]
    ],
    0x5C: [  // REVERSE SOLIDUS
      [7,17,7],[9,20,8],[10,24,9],[12,28,11],[14,33,13],[17,40,16],[20,47,18],[23,56,22],
      [28,67,26],[33,79,31],[39,94,37],[46,112,44],[55,133,52],[65,158,62]
    ],
    0x5D: [  // RIGHT SQUARE BRACKET
      [2,17,7],[3,20,8],[3,24,10],[4,28,11],[4,33,13],[5,41,17],[6,48,19],[7,57,23],
      [8,66,26],[10,79,31],[11,94,37],[13,111,44],[16,133,52],[19,158,62]
    ],
    0x7B: [  // LEFT CURLY BRACKET
      [5,17,7],[6,20,8],[7,24,9],[8,29,11],[9,34,13],[11,40,16],[13,47,18],[15,56,22],
      [18,67,26],[21,79,31],[25,94,37],[29,112,44],[35,133,52],[41,158,62]
    ],
    0x7D: [  // RIGHT CURLY BRACKET
      [5,17,7],[6,20,8],[7,24,9],[8,29,11],[9,34,13],[11,40,16],[13,47,18],[15,56,22],
      [18,67,26],[21,79,31],[25,94,37],[29,112,44],[35,133,52],[41,158,62]
    ],
    0xA0: [  // NO-BREAK SPACE
      [1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],
      [1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0]
    ],
    0x2C6: [  // MODIFIER LETTER CIRCUMFLEX ACCENT
      [11,2,-4],[13,2,-5],[16,2,-6],[18,3,-7],[22,3,-8],[26,4,-9],[30,4,-11],[35,5,-13],
      [41,6,-16],[49,7,-19],[58,8,-22],[69,10,-26],[82,12,-31],[97,14,-37]
    ],
    0x2DC: [  // SMALL TILDE
      [10,2,-4],[12,2,-5],[15,2,-6],[17,2,-7],[20,2,-8],[24,2,-10],[29,4,-11],[34,4,-13],
      [40,5,-16],[48,5,-20],[57,6,-24],[67,7,-28],[80,8,-34],[95,9,-40]
    ],
    0x302: [  // COMBINING CIRCUMFLEX ACCENT
      [11,2,-4],[14,2,-5],[16,2,-6],[19,3,-7],[22,3,-8],[26,4,-9],[30,4,-11],[35,5,-13],
      [42,6,-16],[49,7,-19],[58,8,-22],[69,10,-26],[82,12,-31],[97,14,-37]
    ],
    0x303: [  // COMBINING TILDE
      [10,2,-4],[12,2,-5],[15,2,-6],[17,2,-7],[20,2,-8],[24,2,-10],[29,4,-11],[34,4,-13],
      [40,5,-16],[48,5,-20],[57,6,-24],[68,7,-28],[80,8,-34],[95,9,-40]
    ],
    0x221A: [  // SQUARE ROOT
      [8,17,7],[9,21,8],[10,25,10],[12,30,12],[15,35,14],[17,41,16],[20,48,19],[24,57,23],
      [29,68,27],[34,80,32],[40,95,38],[48,113,45],[57,134,53],[67,159,63]
    ],
    0x2308: [  // LEFT CEILING
      [4,17,7],[5,20,8],[6,25,10],[7,29,12],[8,34,14],[10,41,16],[12,48,19],[14,57,23],
      [16,68,27],[19,80,32],[23,94,37],[27,113,45],[32,134,53],[38,159,63]
    ],
    0x2309: [  // RIGHT CEILING
      [3,17,7],[3,20,8],[5,25,10],[4,29,12],[5,34,14],[6,41,16],[7,48,19],[8,57,23],
      [10,68,27],[12,80,32],[14,94,37],[16,113,45],[19,134,53],[23,159,63]
    ],
    0x230A: [  // LEFT FLOOR
      [4,18,7],[5,21,8],[6,25,10],[7,29,11],[8,34,13],[10,40,16],[12,48,19],[14,57,23],
      [16,68,27],[19,80,32],[23,95,38],[27,113,45],[32,134,53],[38,159,63]
    ],
    0x230B: [  // RIGHT FLOOR
      [3,18,7],[3,21,8],[5,25,10],[4,29,11],[5,34,13],[6,40,16],[7,48,19],[8,57,23],
      [10,68,27],[12,80,32],[14,95,38],[16,113,45],[19,134,53],[23,159,63]
    ],
    0x27E8: [  // MATHEMATICAL LEFT ANGLE BRACKET
      [5,18,7],[6,21,8],[7,25,10],[8,30,12],[10,35,14],[11,41,16],[13,48,19],[16,57,23],
      [19,68,27],[22,80,32],[26,95,38],[31,113,45],[37,134,53],[44,159,63]
    ],
    0x27E9: [  // MATHEMATICAL RIGHT ANGLE BRACKET
      [5,17,7],[6,21,8],[7,25,10],[8,30,12],[9,35,14],[11,41,16],[13,48,19],[15,57,23],
      [18,68,27],[21,80,32],[25,95,38],[29,113,45],[35,134,53],[41,159,63]
    ]
  }
});

MathJax.Ajax.loadComplete(MathJax.OutputJax["HTML-CSS"].imgDir+"/Size3/Regular"+
                          MathJax.OutputJax["HTML-CSS"].imgPacked+"/Main.js");
