[{"locale": "en"}, {"category": "Ll", "key": "1D26", "mappings": {"default": {"default": "greek letter small capital gamma", "alternative": "greek letter gamma", "short": "small cap gamma"}, "mathspeak": {"default": "small upper Gamma"}}}, {"category": "Ll", "key": "1D27", "mappings": {"default": {"default": "greek letter small capital lamda", "alternative": "greek letter lamda", "short": "small cap lamda"}, "mathspeak": {"default": "small upper Lamda"}}}, {"category": "Ll", "key": "1D28", "mappings": {"default": {"default": "greek letter small capital pi", "alternative": "greek letter pi", "short": "small cap pi"}, "mathspeak": {"default": "small upper Pi"}}}, {"category": "Ll", "key": "1D29", "mappings": {"default": {"default": "greek letter small capital rho", "alternative": "greek letter rho", "short": "small cap rho"}, "mathspeak": {"default": "small upper Rho"}}}, {"category": "Ll", "key": "1D2A", "mappings": {"default": {"default": "greek letter small capital psi", "alternative": "greek letter psi", "short": "small cap psi"}, "mathspeak": {"default": "small upper Psi"}}}, {"category": "Lm", "key": "1D5E", "mappings": {"default": {"default": "modifier letter small greek gamma", "alternative": "greek letter superscript gamma", "short": "superscript gamma"}}}, {"category": "Lm", "key": "1D60", "mappings": {"default": {"default": "modifier letter small greek phi", "alternative": "greek letter superscript phi", "short": "superscript phi"}}}, {"category": "Lm", "key": "1D66", "mappings": {"default": {"default": "greek subscript small letter beta", "short": "subscript beta"}}}, {"category": "Lm", "key": "1D67", "mappings": {"default": {"default": "greek subscript small letter gamma", "alternative": "greek letter gamma", "short": "subscript gamma"}}}, {"category": "Lm", "key": "1D68", "mappings": {"default": {"default": "greek subscript small letter rho", "alternative": "greek letter rho", "short": "subscript rho"}}}, {"category": "Lm", "key": "1D69", "mappings": {"default": {"default": "greek subscript small letter phi", "alternative": "greek letter phi", "short": "subscript phi"}}}, {"category": "Lm", "key": "1D6A", "mappings": {"default": {"default": "greek subscript small letter chi", "alternative": "greek letter chi", "short": "subscript chi"}}}]