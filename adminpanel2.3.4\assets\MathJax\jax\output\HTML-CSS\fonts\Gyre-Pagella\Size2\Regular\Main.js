/*
 *  /MathJax/jax/output/HTML-CSS/fonts/Gyre-Pagella/Size2/Regular/Main.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.OutputJax["HTML-CSS"].FONTDATA.FONTS.GyrePagellaMathJax_Size2={directory:"Size2/Regular",family:"GyrePagellaMathJax_Size2",testString:"\u00A0\u0302\u0303\u0306\u030C\u0311\u032C\u032D\u032E\u032F\u0330\u2016\u2044\u20E9\u221A",32:[0,0,250,0,0],40:[840,340,491,127,406],41:[840,340,491,85,364],47:[936,436,689,80,609],91:[846,346,443,127,358],92:[936,436,689,80,609],93:[846,346,443,85,316],123:[845,345,465,85,380],124:[826,326,213,80,133],125:[845,345,465,85,380],160:[0,0,250,0,0],770:[712,-543,731,0,731],771:[700,-544,727,0,727],774:[708,-552,752,0,752],780:[711,-542,731,0,731],785:[721,-564,752,0,752],812:[-60,229,731,0,731],813:[-70,239,731,0,731],814:[-60,217,752,0,752],815:[-78,235,752,0,752],816:[-78,234,727,0,727],8214:[826,326,386,80,306],8260:[936,436,689,80,609],8425:[773,-647,1113,0,1113],8730:[1020,490,717,120,747],8739:[826,326,213,80,133],8741:[826,326,386,80,306],8968:[846,326,443,127,358],8969:[846,326,443,85,316],8970:[826,346,443,127,358],8971:[826,346,443,85,316],9001:[943,443,452,85,367],9002:[943,443,452,85,367],9140:[773,-647,1113,0,1113],9141:[-177,303,1113,0,1113],9180:[779,-570,1528,0,1528],9181:[-100,309,1528,0,1528],9182:[798,-589,1538,0,1538],9183:[-119,328,1538,0,1538],9184:[730,-528,1566,0,1566],9185:[-58,260,1566,0,1566],10214:[846,346,458,127,373],10215:[846,346,458,85,331],10216:[943,443,452,85,367],10217:[943,443,452,85,367],10218:[943,443,701,85,616],10219:[943,443,701,85,616],10222:[840,340,365,127,280],10223:[840,340,365,85,238]};MathJax.Callback.Queue(["initFont",MathJax.OutputJax["HTML-CSS"],"GyrePagellaMathJax_Size2"],["loadComplete",MathJax.Ajax,MathJax.OutputJax["HTML-CSS"].fontDir+"/Size2/Regular/Main.js"]);
