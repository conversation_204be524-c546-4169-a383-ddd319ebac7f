/*
 *  /MathJax/fonts/HTML-CSS/TeX/png/AMS/Regular/BBBold.js
 *  
 *  Copyright (c) 2010-2013 The MathJax Consortium
 *
 *  Part of the MathJax library.
 *  See http://www.mathjax.org for details.
 * 
 *  Licensed under the Apache License, Version 2.0;
 *  you may not use this file except in compliance with the License.
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 */

MathJax.OutputJax["HTML-CSS"].defineImageData({MathJax_AMS:{32:[[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0]],65:[[5,5,0],[6,6,0],[7,7,0],[9,8,0],[10,10,0],[12,12,0],[14,14,0],[17,16,0],[20,19,0],[23,23,0],[28,27,0],[33,33,0],[39,39,0],[46,46,0]],66:[[5,5,0],[6,6,0],[7,7,0],[8,8,0],[9,9,0],[11,11,0],[13,13,0],[15,16,0],[18,19,0],[21,22,0],[25,27,0],[29,32,0],[35,38,0],[41,45,0]],67:[[5,5,0],[6,6,0],[7,7,0],[9,8,0],[10,10,0],[12,12,0],[14,14,0],[16,16,0],[19,21,2],[23,24,1],[27,28,1],[32,34,1],[38,40,1],[45,48,2]],68:[[5,5,0],[6,6,0],[7,7,0],[9,8,0],[10,9,0],[12,11,0],[14,13,0],[16,16,0],[20,19,0],[23,22,0],[27,27,0],[32,32,0],[39,38,0],[46,45,0]],69:[[5,5,0],[6,6,0],[7,7,0],[8,8,0],[9,9,0],[11,11,0],[13,13,0],[15,16,0],[18,19,0],[22,22,0],[26,27,0],[30,32,0],[36,38,0],[43,45,0]],70:[[5,5,0],[5,6,0],[6,7,0],[7,8,0],[9,9,0],[10,11,0],[12,13,0],[14,16,0],[17,19,0],[20,22,0],[23,27,0],[27,32,0],[33,38,0],[39,45,0]],71:[[6,5,0],[7,6,0],[8,7,0],[9,8,0],[11,10,0],[13,12,0],[15,14,0],[18,16,0],[21,19,0],[25,24,1],[30,29,2],[35,33,0],[42,40,1],[50,47,1]],72:[[6,5,0],[7,6,0],[8,7,0],[9,8,0],[11,9,0],[13,11,0],[15,13,0],[18,16,0],[22,19,0],[26,22,0],[30,27,0],[36,32,0],[43,38,0],[51,45,0]],73:[[3,5,0],[4,6,0],[4,7,0],[5,8,0],[6,9,0],[7,11,0],[8,13,0],[9,16,0],[11,19,0],[13,22,0],[15,27,0],[18,32,0],[21,38,0],[25,45,0]],74:[[4,6,1],[4,7,1],[5,8,1],[6,9,1],[7,11,2],[8,13,2],[10,15,2],[12,18,2],[14,21,2],[16,26,4],[19,30,3],[23,35,3],[27,42,4],[32,50,5]],75:[[6,5,0],[7,6,0],[8,7,0],[10,8,0],[11,9,0],[13,11,0],[15,13,0],[18,16,0],[22,19,0],[26,22,0],[31,27,0],[36,32,0],[43,38,0],[51,45,0]],76:[[5,5,0],[6,6,0],[7,7,0],[8,8,0],[9,9,0],[11,11,0],[13,13,0],[15,16,0],[18,19,0],[22,22,0],[26,27,0],[30,32,0],[36,38,0],[43,45,0]],77:[[7,5,0],[8,6,0],[10,7,0],[11,8,0],[13,9,0],[16,11,0],[19,13,0],[22,16,0],[26,19,0],[31,22,0],[37,27,0],[44,32,0],[52,38,0],[61,45,0]],78:[[5,5,0],[6,6,0],[7,7,0],[9,9,0],[10,10,0],[12,12,0],[14,14,0],[17,17,0],[20,21,1],[24,24,1],[28,29,1],[33,34,1],[39,40,1],[47,48,2]],79:[[6,5,0],[7,6,0],[8,7,0],[9,8,0],[11,10,0],[13,12,0],[15,14,0],[18,16,0],[21,21,2],[25,24,1],[30,28,1],[35,34,1],[42,40,1],[49,48,2]],80:[[5,5,0],[5,6,0],[6,7,0],[8,8,0],[9,9,0],[10,11,0],[12,13,0],[14,16,0],[17,19,0],[20,22,0],[24,27,0],[28,32,0],[34,38,0],[40,45,0]],81:[[6,6,1],[7,8,2],[8,9,2],[9,10,2],[11,12,2],[13,15,3],[15,17,3],[18,20,4],[21,24,5],[25,30,7],[30,35,8],[35,41,8],[42,49,10],[49,58,12]],82:[[5,5,0],[6,6,0],[7,7,0],[9,8,0],[10,9,0],[12,11,0],[14,13,0],[17,16,0],[20,19,0],[24,22,0],[28,27,0],[33,32,0],[39,38,0],[47,45,0]],83:[[4,5,0],[5,6,0],[6,7,0],[7,8,0],[8,10,0],[9,12,0],[11,14,0],[13,16,0],[15,19,0],[18,23,0],[21,27,0],[25,33,0],[30,40,1],[35,47,1]],84:[[5,5,0],[6,6,0],[7,7,0],[8,8,0],[9,9,0],[11,11,0],[13,13,0],[15,16,0],[18,19,0],[21,22,0],[25,27,0],[30,32,0],[36,38,0],[42,45,0]],85:[[5,5,0],[6,6,0],[7,7,0],[9,8,0],[10,9,0],[12,11,0],[14,13,0],[17,16,0],[20,19,0],[24,22,0],[28,28,1],[33,33,1],[40,39,1],[47,46,1]],86:[[5,5,0],[6,6,0],[8,7,0],[9,8,0],[10,9,0],[12,11,0],[15,13,0],[17,16,0],[20,20,1],[24,24,2],[29,28,1],[34,33,1],[40,39,1],[48,46,1]],87:[[7,5,0],[9,6,0],[10,7,0],[12,8,0],[14,9,0],[17,11,0],[20,13,0],[24,16,0],[28,20,1],[33,24,2],[39,28,1],[47,33,1],[55,39,1],[66,47,2]],88:[[5,5,0],[6,6,0],[7,7,0],[9,8,0],[10,9,0],[12,11,0],[14,13,0],[17,16,0],[20,19,0],[24,22,0],[28,27,0],[33,32,0],[39,38,0],[47,45,0]],89:[[5,5,0],[6,6,0],[7,7,0],[9,8,0],[10,9,0],[12,11,0],[14,13,0],[17,16,0],[20,19,0],[24,22,0],[28,27,0],[33,32,0],[39,38,0],[47,45,0]],90:[[5,5,0],[6,6,0],[7,7,0],[8,8,0],[9,9,0],[11,11,0],[13,13,0],[15,16,0],[18,19,0],[21,22,0],[25,27,0],[30,32,0],[36,38,0],[42,45,0]],107:[[4,5,0],[5,6,0],[6,7,0],[7,8,0],[8,10,0],[9,12,0],[11,14,0],[13,16,0],[15,19,0],[18,23,0],[21,27,0],[25,32,0],[30,38,0],[36,45,0]]}});MathJax.Ajax.loadComplete(MathJax.OutputJax["HTML-CSS"].imgDir+"/AMS/Regular"+MathJax.OutputJax["HTML-CSS"].imgPacked+"/BBBold.js");

