/*
 *  /MathJax/fonts/HTML-CSS/TeX/png/Main/Bold/Latin1Supplement.js
 *  
 *  Copyright (c) 2010-2013 The MathJax Consortium
 *
 *  Part of the MathJax library.
 *  See http://www.mathjax.org for details.
 * 
 *  Licensed under the Apache License, Version 2.0;
 *  you may not use this file except in compliance with the License.
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 */

MathJax.OutputJax["HTML-CSS"].defineImageData({"MathJax_Main-bold":{160:[[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0]],168:[[4,2,-3],[4,2,-4],[5,2,-6],[6,2,-6],[7,3,-6],[8,3,-9],[10,4,-10],[12,4,-13],[14,5,-15],[16,6,-17],[19,7,-21],[23,8,-24],[27,9,-29],[32,11,-35]],172:[[5,3,0],[6,3,-1],[7,4,0],[9,4,0],[10,5,0],[12,6,-1],[14,8,0],[17,9,-1],[20,9,-2],[24,11,-2],[28,13,-2],[33,15,-2],[39,18,-3],[47,22,-4]],175:[[4,1,-3],[5,1,-5],[5,1,-6],[6,1,-6],[7,1,-7],[9,2,-9],[10,3,-10],[12,2,-13],[14,2,-16],[17,3,-18],[20,3,-22],[23,4,-25],[28,4,-30],[33,5,-36]],176:[[3,1,-4],[4,2,-4],[5,2,-6],[5,2,-6],[6,2,-7],[7,3,-9],[9,3,-11],[10,4,-13],[12,6,-14],[14,6,-17],[17,7,-21],[20,8,-24],[23,9,-29],[28,11,-35]],177:[[6,6,1],[7,8,1],[9,9,1],[10,9,1],[12,11,1],[14,15,1],[17,16,2],[20,20,2],[23,22,1],[28,26,1],[33,31,1],[39,36,2],[46,42,2],[55,51,2]],180:[[4,2,-3],[4,2,-4],[5,3,-5],[6,3,-5],[7,3,-6],[8,4,-8],[9,5,-9],[11,5,-12],[13,6,-14],[16,7,-16],[18,9,-19],[22,10,-23],[26,12,-27],[31,14,-33]],215:[[6,4,0],[7,5,0],[8,7,0],[9,7,0],[11,8,0],[13,10,0],[15,12,0],[17,14,1],[21,17,1],[24,19,1],[29,23,1],[34,26,1],[41,31,2],[48,37,2]],247:[[6,6,1],[7,6,1],[9,8,1],[10,8,1],[12,9,1],[14,12,2],[17,14,2],[20,17,2],[23,20,3],[28,23,3],[33,28,4],[39,32,4],[46,39,5],[55,46,6]]}});MathJax.Ajax.loadComplete(MathJax.OutputJax["HTML-CSS"].imgDir+"/Main/Bold"+MathJax.OutputJax["HTML-CSS"].imgPacked+"/Latin1Supplement.js");

