/*
 *  /MathJax/fonts/HTML-CSS/TeX/png/Main/Regular/GreekAndCoptic.js
 *  
 *  Copyright (c) 2010-2013 The MathJax Consortium
 *
 *  Part of the MathJax library.
 *  See http://www.mathjax.org for details.
 * 
 *  Licensed under the Apache License, Version 2.0;
 *  you may not use this file except in compliance with the License.
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 */

MathJax.OutputJax["HTML-CSS"].defineImageData({MathJax_Main:{915:[[4,5,0],[5,6,0],[6,8,0],[7,8,0],[8,9,0],[10,12,0],[12,14,0],[14,17,0],[16,20,0],[20,23,0],[23,27,0],[27,32,0],[32,38,0],[39,46,0]],916:[[6,5,0],[7,6,0],[8,8,0],[10,8,0],[11,9,0],[14,12,0],[16,14,0],[19,18,0],[22,21,0],[26,24,0],[31,29,0],[37,34,0],[44,40,0],[52,48,0]],920:[[5,5,0],[6,6,0],[8,8,0],[9,8,0],[10,9,0],[12,12,0],[15,14,0],[17,19,1],[20,22,1],[24,25,1],[29,29,0],[34,34,1],[40,41,1],[48,49,1]],923:[[5,5,0],[6,6,0],[7,8,0],[8,8,0],[10,9,0],[11,12,0],[13,14,0],[16,18,0],[19,21,0],[22,24,0],[26,29,0],[31,34,0],[37,40,0],[44,48,0]],926:[[5,5,0],[6,6,0],[7,8,0],[8,8,0],[9,9,0],[11,12,0],[13,14,0],[15,17,0],[18,20,0],[21,23,0],[25,27,0],[29,32,0],[35,38,0],[42,46,0]],928:[[5,5,0],[6,6,0],[8,8,0],[9,8,0],[11,9,0],[13,12,0],[15,14,0],[17,17,0],[21,20,0],[24,23,0],[29,27,0],[34,32,0],[41,38,0],[48,46,0]],931:[[5,5,0],[6,6,0],[7,8,0],[8,8,0],[10,9,0],[12,12,0],[13,14,0],[16,17,0],[19,20,0],[22,23,0],[27,27,0],[31,32,0],[37,38,0],[44,46,0]],933:[[5,5,0],[6,6,0],[8,8,0],[9,8,0],[10,9,0],[12,12,0],[14,14,0],[17,18,0],[20,21,0],[24,24,0],[29,28,0],[34,33,0],[40,40,0],[48,47,0]],934:[[5,5,0],[6,6,0],[7,8,0],[8,8,0],[10,9,0],[12,12,0],[13,14,0],[16,17,0],[19,20,0],[22,23,0],[27,27,0],[31,32,0],[37,38,0],[44,46,0]],936:[[6,5,0],[7,6,0],[8,8,0],[9,8,0],[11,9,0],[13,12,0],[15,14,0],[17,17,0],[21,20,0],[24,23,0],[29,27,0],[34,32,0],[41,38,0],[48,46,0]],937:[[5,5,0],[6,6,0],[7,8,0],[8,8,0],[10,9,0],[12,12,0],[14,14,0],[16,18,0],[19,21,0],[23,24,0],[27,29,0],[32,33,0],[38,40,0],[45,47,0]]}});MathJax.Ajax.loadComplete(MathJax.OutputJax["HTML-CSS"].imgDir+"/Main/Regular"+MathJax.OutputJax["HTML-CSS"].imgPacked+"/GreekAndCoptic.js");

