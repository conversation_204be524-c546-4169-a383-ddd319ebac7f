/*
 *  /MathJax/fonts/HTML-CSS/TeX/png/Size3/Regular/Main.js
 *  
 *  Copyright (c) 2010-2013 The MathJax Consortium
 *
 *  Part of the MathJax library.
 *  See http://www.mathjax.org for details.
 * 
 *  Licensed under the Apache License, Version 2.0;
 *  you may not use this file except in compliance with the License.
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 */

MathJax.OutputJax["HTML-CSS"].defineImageData({MathJax_Size3:{32:[[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0]],40:[[5,17,7],[6,20,8],[7,24,9],[9,29,11],[10,34,13],[12,40,16],[14,47,18],[17,56,22],[20,67,26],[24,79,31],[28,94,37],[33,112,44],[39,133,52],[47,158,62]],41:[[4,17,7],[5,20,8],[6,24,9],[7,29,11],[8,34,13],[9,40,16],[11,47,18],[13,56,22],[15,67,26],[18,79,31],[21,94,37],[25,112,44],[30,133,52],[35,158,62]],47:[[7,17,7],[9,20,8],[10,24,9],[12,28,11],[14,33,13],[17,40,16],[20,47,18],[23,56,22],[28,67,26],[33,79,31],[39,94,37],[46,112,44],[55,133,52],[66,158,62]],91:[[4,17,7],[5,20,8],[6,24,10],[7,28,11],[8,33,13],[9,41,17],[11,48,19],[12,57,23],[15,66,26],[17,79,31],[21,94,37],[24,111,44],[29,133,52],[34,158,62]],92:[[7,17,7],[9,20,8],[10,24,9],[12,28,11],[14,33,13],[17,40,16],[20,47,18],[23,56,22],[28,67,26],[33,79,31],[39,94,37],[46,112,44],[55,133,52],[65,158,62]],93:[[2,17,7],[3,20,8],[3,24,10],[4,28,11],[4,33,13],[5,41,17],[6,48,19],[7,57,23],[8,66,26],[10,79,31],[11,94,37],[13,111,44],[16,133,52],[19,158,62]],123:[[5,17,7],[6,20,8],[7,24,9],[8,29,11],[9,34,13],[11,40,16],[13,47,18],[15,56,22],[18,67,26],[21,79,31],[25,94,37],[29,112,44],[35,133,52],[41,158,62]],125:[[5,17,7],[6,20,8],[7,24,9],[8,29,11],[9,34,13],[11,40,16],[13,47,18],[15,56,22],[18,67,26],[21,79,31],[25,94,37],[29,112,44],[35,133,52],[41,158,62]],160:[[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0]],710:[[11,2,-4],[13,2,-5],[16,2,-6],[18,3,-7],[22,3,-8],[26,4,-9],[30,4,-11],[35,5,-13],[41,6,-16],[49,7,-19],[58,8,-22],[69,10,-26],[82,12,-31],[97,14,-37]],732:[[10,2,-4],[12,2,-5],[15,2,-6],[17,2,-7],[20,2,-8],[24,2,-10],[29,4,-11],[34,4,-13],[40,5,-16],[48,5,-20],[57,6,-24],[67,7,-28],[80,8,-34],[95,9,-40]],770:[[11,2,-4],[14,2,-5],[16,2,-6],[19,3,-7],[22,3,-8],[26,4,-9],[30,4,-11],[35,5,-13],[42,6,-16],[49,7,-19],[58,8,-22],[69,10,-26],[82,12,-31],[97,14,-37]],771:[[10,2,-4],[12,2,-5],[15,2,-6],[17,2,-7],[20,2,-8],[24,2,-10],[29,4,-11],[34,4,-13],[40,5,-16],[48,5,-20],[57,6,-24],[68,7,-28],[80,8,-34],[95,9,-40]],8730:[[8,17,7],[9,21,8],[10,25,10],[12,30,12],[15,35,14],[17,41,16],[20,48,19],[24,57,23],[29,68,27],[34,80,32],[40,95,38],[48,113,45],[57,134,53],[67,159,63]],8968:[[4,17,7],[5,20,8],[6,25,10],[7,29,12],[8,34,14],[10,41,16],[12,48,19],[14,57,23],[16,68,27],[19,80,32],[23,94,37],[27,113,45],[32,134,53],[38,159,63]],8969:[[3,17,7],[3,20,8],[5,25,10],[4,29,12],[5,34,14],[6,41,16],[7,48,19],[8,57,23],[10,68,27],[12,80,32],[14,94,37],[16,113,45],[19,134,53],[23,159,63]],8970:[[4,18,7],[5,21,8],[6,25,10],[7,29,11],[8,34,13],[10,40,16],[12,48,19],[14,57,23],[16,68,27],[19,80,32],[23,95,38],[27,113,45],[32,134,53],[38,159,63]],8971:[[3,18,7],[3,21,8],[5,25,10],[4,29,11],[5,34,13],[6,40,16],[7,48,19],[8,57,23],[10,68,27],[12,80,32],[14,95,38],[16,113,45],[19,134,53],[23,159,63]],10216:[[5,18,7],[6,21,8],[7,25,10],[8,30,12],[10,35,14],[11,41,16],[13,48,19],[16,57,23],[19,68,27],[22,80,32],[26,95,38],[31,113,45],[37,134,53],[44,159,63]],10217:[[5,17,7],[6,21,8],[7,25,10],[8,30,12],[9,35,14],[11,41,16],[13,48,19],[15,57,23],[18,68,27],[21,80,32],[25,95,38],[29,113,45],[35,134,53],[41,159,63]]}});MathJax.Ajax.loadComplete(MathJax.OutputJax["HTML-CSS"].imgDir+"/Size3/Regular"+MathJax.OutputJax["HTML-CSS"].imgPacked+"/Main.js");

