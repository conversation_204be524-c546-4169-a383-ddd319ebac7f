/*
 *  /MathJax/fonts/HTML-CSS/TeX/png/AMS/Regular/SpacingModLetters.js
 *  
 *  Copyright (c) 2010-2013 The MathJax Consortium
 *
 *  Part of the MathJax library.
 *  See http://www.mathjax.org for details.
 * 
 *  Licensed under the Apache License, Version 2.0;
 *  you may not use this file except in compliance with the License.
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 */

MathJax.OutputJax["HTML-CSS"].defineImageData({MathJax_AMS:{710:[[18,3,-3],[21,3,-4],[25,3,-5],[29,4,-6],[34,5,-8],[40,5,-9],[47,6,-11],[56,8,-12],[66,8,-16],[79,10,-19],[93,12,-21],[110,14,-26],[131,17,-30],[156,20,-36]],732:[[17,2,-4],[21,2,-5],[24,4,-5],[29,5,-7],[34,4,-9],[40,5,-10],[47,6,-12],[56,6,-15],[66,8,-17],[78,9,-21],[93,11,-25],[110,13,-29],[130,15,-35],[155,18,-41]]}});MathJax.Ajax.loadComplete(MathJax.OutputJax["HTML-CSS"].imgDir+"/AMS/Regular"+MathJax.OutputJax["HTML-CSS"].imgPacked+"/SpacingModLetters.js");

