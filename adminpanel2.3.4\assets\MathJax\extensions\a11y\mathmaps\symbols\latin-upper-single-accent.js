[{"locale": "en"}, {"category": "<PERSON>", "key": "00C0", "mappings": {"default": {"default": "latin capital letter a with grave", "alternative": "latin capital letter a grave", "short": "cap a grave"}, "mathspeak": {"default": "modifying above upper A with grave", "brief": "mod above upper A with grave", "sbrief": "mod above upper A with grave"}}}, {"category": "<PERSON>", "key": "00C1", "mappings": {"default": {"default": "latin capital letter a with acute", "alternative": "latin capital letter a acute", "short": "cap a acute"}, "mathspeak": {"default": "modifying above upper A with acute", "brief": "mod above upper A with acute", "sbrief": "mod above upper A with acute"}}}, {"category": "<PERSON>", "key": "00C2", "mappings": {"default": {"default": "latin capital letter a with circumflex", "alternative": "latin capital letter a hat", "short": "cap a hat"}, "mathspeak": {"default": "modifying above upper A with caret", "brief": "mod above upper A with caret", "sbrief": "mod above upper A with caret"}}}, {"category": "<PERSON>", "key": "00C3", "mappings": {"default": {"default": "latin capital letter a with tilde", "alternative": "latin capital letter a tilde", "short": "cap a tilde"}, "mathspeak": {"default": "upper A overtilde", "brief": "upper A overtilde", "sbrief": "upper A overtilde"}}}, {"category": "<PERSON>", "key": "00C4", "mappings": {"default": {"default": "latin capital letter a with diaeresis", "alternative": "latin capital letter a double overdot", "short": "cap a double overdot"}, "mathspeak": {"default": "modifying above upper A with double dot", "brief": "mod above upper A with double dot", "sbrief": "mod above upper A with double dot"}}}, {"category": "<PERSON>", "key": "00C5", "mappings": {"default": {"default": "latin capital letter a with ring above", "alternative": "latin capital letter a ring", "short": "cap a ring"}, "mathspeak": {"default": "modifying above upper A with ring", "brief": "mod above upper A with ring", "sbrief": "mod above upper A with ring"}}}, {"category": "<PERSON>", "key": "00C7", "mappings": {"default": {"default": "latin capital letter c cedilla", "alternative": "latin capital letter c cedilla", "short": "cap c cedilla"}, "mathspeak": {"default": "modifying above upper C with cedilla", "brief": "mod above upper C with cedilla", "sbrief": "mod above upper C with cedilla"}}}, {"category": "<PERSON>", "key": "00C8", "mappings": {"default": {"default": "latin capital letter e with grave", "alternative": "latin capital letter e grave", "short": "cap e grave"}, "mathspeak": {"default": "modifying above upper E with grave", "brief": "mod above upper E with grave", "sbrief": "mod above upper E with grave"}}}, {"category": "<PERSON>", "key": "00C9", "mappings": {"default": {"default": "latin capital letter e with acute", "alternative": "latin capital letter e acute", "short": "cap e acute"}, "mathspeak": {"default": "modifying above upper E with acute", "brief": "mod above upper E with acute", "sbrief": "mod above upper E with acute"}}}, {"category": "<PERSON>", "key": "00CA", "mappings": {"default": {"default": "latin capital letter e with circumflex", "alternative": "latin capital letter e hat", "short": "cap e hat"}, "mathspeak": {"default": "modifying above upper E with caret", "brief": "mod above upper E with caret", "sbrief": "mod above upper E with caret"}}}, {"category": "<PERSON>", "key": "00CB", "mappings": {"default": {"default": "latin capital letter e with diaeresis", "alternative": "latin capital letter e double overdot", "short": "cap e double overdot"}, "mathspeak": {"default": "modifying above upper E with double dot", "brief": "mod above upper E with double dot", "sbrief": "mod above upper E with double dot"}}}, {"category": "<PERSON>", "key": "00CC", "mappings": {"default": {"default": "latin capital letter i with grave", "alternative": "latin capital letter i grave", "short": "cap i grave"}, "mathspeak": {"default": "modifying above upper I with grave", "brief": "mod above upper I with grave", "sbrief": "mod above upper I with grave"}}}, {"category": "<PERSON>", "key": "00CD", "mappings": {"default": {"default": "latin capital letter i with acute", "alternative": "latin capital letter i acute", "short": "cap i acute"}, "mathspeak": {"default": "modifying above upper I with acute", "brief": "mod above upper I with acute", "sbrief": "mod above upper I with acute"}}}, {"category": "<PERSON>", "key": "00CE", "mappings": {"default": {"default": "latin capital letter i with circumflex", "alternative": "latin capital letter i hat", "short": "cap i hat"}, "mathspeak": {"default": "modifying above upper I with caret", "brief": "mod above upper I with caret", "sbrief": "mod above upper I with caret"}}}, {"category": "<PERSON>", "key": "00CF", "mappings": {"default": {"default": "latin capital letter i with diaeresis", "alternative": "latin capital letter i double overdot", "short": "cap i double overdot"}, "mathspeak": {"default": "modifying above upper I with double dot", "brief": "mod above upper I with double dot", "sbrief": "mod above upper I with double dot"}}}, {"category": "<PERSON>", "key": "00D1", "mappings": {"default": {"default": "latin capital letter n tilde", "alternative": "latin capital letter n tilde", "short": "cap n tilde"}, "mathspeak": {"default": "upper N overtilde", "brief": "upper N overtilde", "sbrief": "upper N overtilde"}}}, {"category": "<PERSON>", "key": "00D2", "mappings": {"default": {"default": "latin capital letter o with grave", "alternative": "latin capital letter o grave", "short": "cap o grave"}, "mathspeak": {"default": "modifying above upper O with grave", "brief": "mod above upper O with grave", "sbrief": "mod above upper O with grave"}}}, {"category": "<PERSON>", "key": "00D3", "mappings": {"default": {"default": "latin capital letter o with acute", "alternative": "latin capital letter o acute", "short": "cap o acute"}, "mathspeak": {"default": "modifying above upper O with acute", "brief": "mod above upper O with acute", "sbrief": "mod above upper O with acute"}}}, {"category": "<PERSON>", "key": "00D4", "mappings": {"default": {"default": "latin capital letter o with circumflex", "alternative": "latin capital letter o hat", "short": "cap o hat"}, "mathspeak": {"default": "modifying above upper O with caret", "brief": "mod above upper O with caret", "sbrief": "mod above upper O with caret"}}}, {"category": "<PERSON>", "key": "00D5", "mappings": {"default": {"default": "latin capital letter o with tilde", "alternative": "latin capital letter o tilde", "short": "cap o tilde"}, "mathspeak": {"default": "upper O overtilde", "brief": "upper O overtilde", "sbrief": "upper O overtilde"}}}, {"category": "<PERSON>", "key": "00D6", "mappings": {"default": {"default": "latin capital letter o with diaeresis", "alternative": "latin capital letter o double overdot", "short": "cap o double overdot"}, "mathspeak": {"default": "modifying above upper O with double dot", "brief": "mod above upper O with double dot", "sbrief": "mod above upper O with double dot"}}}, {"category": "<PERSON>", "key": "00D9", "mappings": {"default": {"default": "latin capital letter u with grave", "alternative": "latin capital letter u grave", "short": "cap u grave"}, "mathspeak": {"default": "modifying above upper U with grave", "brief": "mod above upper U with grave", "sbrief": "mod above upper U with grave"}}}, {"category": "<PERSON>", "key": "00DA", "mappings": {"default": {"default": "latin capital letter u with acute", "alternative": "latin capital letter u acute", "short": "cap u acute"}, "mathspeak": {"default": "modifying above upper U with acute", "brief": "mod above upper U with acute", "sbrief": "mod above upper U with acute"}}}, {"category": "<PERSON>", "key": "00DB", "mappings": {"default": {"default": "latin capital letter u with circumflex", "alternative": "latin capital letter u hat", "short": "cap u hat"}, "mathspeak": {"default": "modifying above upper U with caret", "brief": "mod above upper U with caret", "sbrief": "mod above upper U with caret"}}}, {"category": "<PERSON>", "key": "00DC", "mappings": {"default": {"default": "latin capital letter u with diaeresis", "alternative": "latin capital letter u double overdot", "short": "cap u double overdot"}, "mathspeak": {"default": "modifying above upper U with double dot", "brief": "mod above upper U with double dot", "sbrief": "mod above upper U with double dot"}}}, {"category": "<PERSON>", "key": "00DD", "mappings": {"default": {"default": "latin capital letter y with acute", "alternative": "latin capital letter y acute", "short": "cap y acute"}, "mathspeak": {"default": "modifying above upper Y with acute", "brief": "mod above upper Y with acute", "sbrief": "mod above upper Y with acute"}}}, {"category": "<PERSON>", "key": "0100", "mappings": {"default": {"default": "latin capital letter a macron", "alternative": "latin capital letter a overbar", "short": "cap a overbar"}, "mathspeak": {"default": "upper A overbar", "brief": "upper A overbar", "sbrief": "upper A overbar"}}}, {"category": "<PERSON>", "key": "0102", "mappings": {"default": {"default": "latin capital letter a with breve", "alternative": "latin capital letter a breve", "short": "cap a breve"}, "mathspeak": {"default": "modifying above upper A with breve", "brief": "mod above upper A with breve", "sbrief": "mod above upper A with breve"}}}, {"category": "<PERSON>", "key": "0104", "mappings": {"default": {"default": "latin capital letter a with ogonek", "alternative": "latin capital letter a ogonek", "short": "cap a ogonek"}, "mathspeak": {"default": "modifying above upper A with ogonek", "brief": "mod above upper A with ogonek", "sbrief": "mod above upper A with ogonek"}}}, {"category": "<PERSON>", "key": "0106", "mappings": {"default": {"default": "latin capital letter c with acute", "alternative": "latin capital letter c acute", "short": "cap c acute"}, "mathspeak": {"default": "modifying above upper C with acute", "brief": "mod above upper C with acute", "sbrief": "mod above upper C with acute"}}}, {"category": "<PERSON>", "key": "0108", "mappings": {"default": {"default": "latin capital letter c with circumflex", "alternative": "latin capital letter c hat", "short": "cap c hat"}, "mathspeak": {"default": "modifying above upper C with caret", "brief": "mod above upper C with caret", "sbrief": "mod above upper C with caret"}}}, {"category": "<PERSON>", "key": "010A", "mappings": {"default": {"default": "latin capital letter c with dot above", "alternative": "latin capital letter c overdot", "short": "cap c overdot"}, "mathspeak": {"default": "modifying above upper C with dot", "brief": "mod above upper C with dot", "sbrief": "mod above upper C with dot"}}}, {"category": "<PERSON>", "key": "010C", "mappings": {"default": {"default": "latin capital letter c with caron", "alternative": "latin capital letter c hacek", "short": "cap c caron"}, "mathspeak": {"default": "modifying above upper C with caron", "brief": "mod above upper C with caron", "sbrief": "mod above upper C with caron"}}}, {"category": "<PERSON>", "key": "010E", "mappings": {"default": {"default": "latin capital letter d with caron", "alternative": "latin capital letter d hacek", "short": "cap d caron"}, "mathspeak": {"default": "modifying above upper D with caron", "brief": "mod above upper D with caron", "sbrief": "mod above upper D with caron"}}}, {"category": "<PERSON>", "key": "0112", "mappings": {"default": {"default": "latin capital letter e with macron", "alternative": "latin capital letter e overbar", "short": "cap e overbar"}, "mathspeak": {"default": "upper E overbar", "brief": "upper E overbar", "sbrief": "upper E overbar"}}}, {"category": "<PERSON>", "key": "0114", "mappings": {"default": {"default": "latin capital letter e with breve", "alternative": "latin capital letter e breve", "short": "cap e breve"}, "mathspeak": {"default": "modifying above upper E with breve", "brief": "mod above upper E with breve", "sbrief": "mod above upper E with breve"}}}, {"category": "<PERSON>", "key": "0116", "mappings": {"default": {"default": "latin capital letter e with dot above", "alternative": "latin capital letter e overdot", "short": "cap e overdot"}, "mathspeak": {"default": "modifying above upper E with dot", "brief": "mod above upper E with dot", "sbrief": "mod above upper E with dot"}}}, {"category": "<PERSON>", "key": "0118", "mappings": {"default": {"default": "latin capital letter e with ogonek", "alternative": "latin capital letter e ogonek", "short": "cap e ogonek"}, "mathspeak": {"default": "modifying above upper E with ogonek", "brief": "mod above upper E with ogonek", "sbrief": "mod above upper E with ogonek"}}}, {"category": "<PERSON>", "key": "011A", "mappings": {"default": {"default": "latin capital letter e with caron", "alternative": "latin capital letter e hacek", "short": "cap e caron"}, "mathspeak": {"default": "modifying above upper E with caron", "brief": "mod above upper E with caron", "sbrief": "mod above upper E with caron"}}}, {"category": "<PERSON>", "key": "011C", "mappings": {"default": {"default": "latin capital letter g with circumflex", "alternative": "latin capital letter g hat", "short": "cap g hat"}, "mathspeak": {"default": "modifying above upper G with caret", "brief": "mod above upper G with caret", "sbrief": "mod above upper G with caret"}}}, {"category": "<PERSON>", "key": "011E", "mappings": {"default": {"default": "latin capital letter g with breve", "alternative": "latin capital letter g breve", "short": "cap g breve"}, "mathspeak": {"default": "modifying above upper G with breve", "brief": "mod above upper G with breve", "sbrief": "mod above upper G with breve"}}}, {"category": "<PERSON>", "key": "0120", "mappings": {"default": {"default": "latin capital letter g with dot above", "alternative": "latin capital letter g overdot", "short": "cap g overdot"}, "mathspeak": {"default": "modifying above upper G with dot", "brief": "mod above upper G with dot", "sbrief": "mod above upper G with dot"}}}, {"category": "<PERSON>", "key": "0122", "mappings": {"default": {"default": "latin capital letter g with cedilla", "alternative": "latin capital letter g cedilla", "short": "cap g cedilla"}, "mathspeak": {"default": "modifying above upper G with cedilla", "brief": "mod above upper G with cedilla", "sbrief": "mod above upper G with cedilla"}}}, {"category": "<PERSON>", "key": "0124", "mappings": {"default": {"default": "latin capital letter h with circumflex", "alternative": "latin capital letter h hat", "short": "cap h hat"}, "mathspeak": {"default": "modifying above upper H with caret", "brief": "mod above upper H with caret", "sbrief": "mod above upper H with caret"}}}, {"category": "<PERSON>", "key": "0128", "mappings": {"default": {"default": "latin capital letter i with tilde", "alternative": "latin capital letter i tilde", "short": "cap i tilde"}, "mathspeak": {"default": "upper I overtilde", "brief": "upper I overtilde", "sbrief": "upper I overtilde"}}}, {"category": "<PERSON>", "key": "012A", "mappings": {"default": {"default": "latin capital letter i with macron", "alternative": "latin capital letter i overbar", "short": "cap i overbar"}, "mathspeak": {"default": "upper I overbar", "brief": "upper I overbar", "sbrief": "upper I overbar"}}}, {"category": "<PERSON>", "key": "012C", "mappings": {"default": {"default": "latin capital letter i with breve", "alternative": "latin capital letter i breve", "short": "cap i breve"}, "mathspeak": {"default": "modifying above upper I with breve", "brief": "mod above upper I with breve", "sbrief": "mod above upper I with breve"}}}, {"category": "<PERSON>", "key": "012E", "mappings": {"default": {"default": "latin capital letter i with ogonek", "alternative": "latin capital letter i ogonek", "short": "cap i ogonek"}, "mathspeak": {"default": "modifying above upper I with ogonek", "brief": "mod above upper I with ogonek", "sbrief": "mod above upper I with ogonek"}}}, {"category": "<PERSON>", "key": "0130", "mappings": {"default": {"default": "latin capital letter i with dot above", "alternative": "latin capital letter i overdot", "short": "cap i overdot"}, "mathspeak": {"default": "modifying above upper I with dot", "brief": "mod above upper I with dot", "sbrief": "mod above upper I with dot"}}}, {"category": "<PERSON>", "key": "0134", "mappings": {"default": {"default": "latin capital letter j circumflex", "alternative": "latin capital letter j hat", "short": "cap j hat"}, "mathspeak": {"default": "modifying above upper J with caret", "brief": "mod above upper J with caret", "sbrief": "mod above upper J with caret"}}}, {"category": "<PERSON>", "key": "0136", "mappings": {"default": {"default": "latin capital letter k with cedilla", "alternative": "latin capital letter k cedilla", "short": "cap k cedilla"}, "mathspeak": {"default": "modifying above upper K with cedilla", "brief": "mod above upper K with cedilla", "sbrief": "mod above upper K with cedilla"}}}, {"category": "<PERSON>", "key": "0139", "mappings": {"default": {"default": "latin capital letter l with acute", "alternative": "latin capital letter l acute", "short": "cap l acute"}, "mathspeak": {"default": "modifying above upper L with acute", "brief": "mod above upper L with acute", "sbrief": "mod above upper L with acute"}}}, {"category": "<PERSON>", "key": "013B", "mappings": {"default": {"default": "latin capital letter l with cedilla", "alternative": "latin capital letter l cedilla", "short": "cap l cedilla"}, "mathspeak": {"default": "modifying above upper L with cedilla", "brief": "mod above upper L with cedilla", "sbrief": "mod above upper L with cedilla"}}}, {"category": "<PERSON>", "key": "013D", "mappings": {"default": {"default": "latin capital letter l with caron", "alternative": "latin capital letter l hacek", "short": "cap l caron"}, "mathspeak": {"default": "modifying above upper L with caron", "brief": "mod above upper L with caron", "sbrief": "mod above upper L with caron"}}}, {"category": "<PERSON>", "key": "013F", "mappings": {"default": {"default": "latin capital letter l with middle dot", "alternative": "latin capital letter l middle dot", "short": "cap l middle dot"}, "mathspeak": {"default": "modifying above upper L with middle dot", "brief": "mod above upper L with middle dot", "sbrief": "mod above upper L with middle dot"}}}, {"category": "<PERSON>", "key": "0143", "mappings": {"default": {"default": "latin capital letter n with acute", "alternative": "latin capital letter n acute", "short": "cap n acute"}, "mathspeak": {"default": "modifying above upper N with acute", "brief": "mod above upper N with acute", "sbrief": "mod above upper N with acute"}}}, {"category": "<PERSON>", "key": "0145", "mappings": {"default": {"default": "latin capital letter n with cedilla", "alternative": "latin capital letter n cedilla", "short": "cap n cedilla"}, "mathspeak": {"default": "modifying above upper N with cedilla", "brief": "mod above upper N with cedilla", "sbrief": "mod above upper N with cedilla"}}}, {"category": "<PERSON>", "key": "0147", "mappings": {"default": {"default": "latin capital letter n with caron", "alternative": "latin capital letter n hacek", "short": "cap n caron"}, "mathspeak": {"default": "modifying above upper N with caron", "brief": "mod above upper N with caron", "sbrief": "mod above upper N with caron"}}}, {"category": "<PERSON>", "key": "014C", "mappings": {"default": {"default": "latin capital letter o macron", "alternative": "latin capital letter o overbar", "short": "cap o overbar"}, "mathspeak": {"default": "upper O overbar", "brief": "upper O overbar", "sbrief": "upper O overbar"}}}, {"category": "<PERSON>", "key": "014E", "mappings": {"default": {"default": "latin capital letter o with breve", "alternative": "latin capital letter o breve", "short": "cap o breve"}, "mathspeak": {"default": "modifying above upper O with breve", "brief": "mod above upper O with breve", "sbrief": "mod above upper O with breve"}}}, {"category": "<PERSON>", "key": "0150", "mappings": {"default": {"default": "latin capital letter o with double acute", "alternative": "latin capital letter o double acute", "short": "cap o double acute"}, "mathspeak": {"default": "modifying above upper O with double acute", "brief": "mod above upper O with double acute", "sbrief": "mod above upper O with double acute"}}}, {"category": "<PERSON>", "key": "0154", "mappings": {"default": {"default": "latin capital letter r acute", "alternative": "latin capital letter r acute", "short": "cap r acute"}, "mathspeak": {"default": "modifying above upper R with acute", "brief": "mod above upper R with acute", "sbrief": "mod above upper R with acute"}}}, {"category": "<PERSON>", "key": "0156", "mappings": {"default": {"default": "latin capital letter r with cedilla", "alternative": "latin capital letter r cedilla", "short": "cap r cedilla"}, "mathspeak": {"default": "modifying above upper R with cedilla", "brief": "mod above upper R with cedilla", "sbrief": "mod above upper R with cedilla"}}}, {"category": "<PERSON>", "key": "0158", "mappings": {"default": {"default": "latin capital letter r with caron", "alternative": "latin capital letter r hacek", "short": "cap r caron"}, "mathspeak": {"default": "modifying above upper R with caron", "brief": "mod above upper R with caron", "sbrief": "mod above upper R with caron"}}}, {"category": "<PERSON>", "key": "015A", "mappings": {"default": {"default": "latin capital letter s with acute", "alternative": "latin capital letter s acute", "short": "cap s acute"}, "mathspeak": {"default": "modifying above upper S with acute", "brief": "mod above upper S with acute", "sbrief": "mod above upper S with acute"}}}, {"category": "<PERSON>", "key": "015C", "mappings": {"default": {"default": "latin capital letter s with circumflex", "alternative": "latin capital letter s hat", "short": "cap s hat"}, "mathspeak": {"default": "modifying above upper S with caret", "brief": "mod above upper S with caret", "sbrief": "mod above upper S with caret"}}}, {"category": "<PERSON>", "key": "015E", "mappings": {"default": {"default": "latin capital letter s with cedilla", "alternative": "latin capital letter s cedilla", "short": "cap s cedilla"}, "mathspeak": {"default": "modifying above upper S with cedilla", "brief": "mod above upper S with cedilla", "sbrief": "mod above upper S with cedilla"}}}, {"category": "<PERSON>", "key": "0160", "mappings": {"default": {"default": "latin capital letter s with caron", "alternative": "latin capital letter s hacek", "short": "cap s caron"}, "mathspeak": {"default": "modifying above upper S with caron", "brief": "mod above upper S with caron", "sbrief": "mod above upper S with caron"}}}, {"category": "<PERSON>", "key": "0162", "mappings": {"default": {"default": "latin capital letter t with cedilla", "alternative": "latin capital letter t cedilla", "short": "cap t cedilla"}, "mathspeak": {"default": "modifying above upper T with cedilla", "brief": "mod above upper T with cedilla", "sbrief": "mod above upper T with cedilla"}}}, {"category": "<PERSON>", "key": "0164", "mappings": {"default": {"default": "latin capital letter t with caron", "alternative": "latin capital letter t hacek", "short": "cap t caron"}, "mathspeak": {"default": "modifying above upper T with caron", "brief": "mod above upper T with caron", "sbrief": "mod above upper T with caron"}}}, {"category": "<PERSON>", "key": "0168", "mappings": {"default": {"default": "latin capital letter u with tilde", "alternative": "latin capital letter u tilde", "short": "cap u tilde"}, "mathspeak": {"default": "upper U overtilde", "brief": "upper U overtilde", "sbrief": "upper U overtilde"}}}, {"category": "<PERSON>", "key": "016A", "mappings": {"default": {"default": "latin capital letter u with macron", "alternative": "latin capital letter u overbar", "short": "cap u overbar"}, "mathspeak": {"default": "upper U overbar", "brief": "upper U overbar", "sbrief": "upper U overbar"}}}, {"category": "<PERSON>", "key": "016C", "mappings": {"default": {"default": "latin capital letter u with breve", "alternative": "latin capital letter u breve", "short": "cap u breve"}, "mathspeak": {"default": "modifying above upper U with breve", "brief": "mod above upper U with breve", "sbrief": "mod above upper U with breve"}}}, {"category": "<PERSON>", "key": "016E", "mappings": {"default": {"default": "latin capital letter u with ring above", "alternative": "latin capital letter u ring", "short": "cap u ring"}, "mathspeak": {"default": "modifying above upper U with ring", "brief": "mod above upper U with ring", "sbrief": "mod above upper U with ring"}}}, {"category": "<PERSON>", "key": "0170", "mappings": {"default": {"default": "latin capital letter u with double acute", "alternative": "latin capital letter u double acute", "short": "cap u double acute"}, "mathspeak": {"default": "modifying above upper U with double acute", "brief": "mod above upper U with double acute", "sbrief": "mod above upper U with double acute"}}}, {"category": "<PERSON>", "key": "0172", "mappings": {"default": {"default": "latin capital letter u with ogonek", "alternative": "latin capital letter u ogonek", "short": "cap u ogonek"}, "mathspeak": {"default": "modifying above upper U with ogonek", "brief": "mod above upper U with ogonek", "sbrief": "mod above upper U with ogonek"}}}, {"category": "<PERSON>", "key": "0174", "mappings": {"default": {"default": "latin capital letter w with circumflex", "alternative": "latin capital letter w hat", "short": "cap w hat"}, "mathspeak": {"default": "modifying above upper W with caret", "brief": "mod above upper W with caret", "sbrief": "mod above upper W with caret"}}}, {"category": "<PERSON>", "key": "0176", "mappings": {"default": {"default": "latin capital letter y with circumflex", "alternative": "latin capital letter y hat", "short": "cap y hat"}, "mathspeak": {"default": "modifying above upper Y with caret", "brief": "mod above upper Y with caret", "sbrief": "mod above upper Y with caret"}}}, {"category": "<PERSON>", "key": "0178", "mappings": {"default": {"default": "latin capital letter y with diaeresis", "alternative": "latin capital letter y double overdot", "short": "cap y double overdot"}, "mathspeak": {"default": "modifying above upper Y with double dot", "brief": "mod above upper Y with double dot", "sbrief": "mod above upper Y with double dot"}}}, {"category": "<PERSON>", "key": "0179", "mappings": {"default": {"default": "latin capital letter z with acute", "alternative": "latin capital letter z acute", "short": "cap z acute"}, "mathspeak": {"default": "modifying above upper Z with acute", "brief": "mod above upper Z with acute", "sbrief": "mod above upper Z with acute"}}}, {"category": "<PERSON>", "key": "017B", "mappings": {"default": {"default": "latin capital letter z with dot above", "alternative": "latin capital letter z overdot", "short": "cap z overdot"}, "mathspeak": {"default": "modifying above upper Z with dot", "brief": "mod above upper Z with dot", "sbrief": "mod above upper Z with dot"}}}, {"category": "<PERSON>", "key": "017D", "mappings": {"default": {"default": "latin capital letter z with caron", "alternative": "latin capital letter z hacek", "short": "cap z caron"}, "mathspeak": {"default": "modifying above upper Z with caron", "brief": "mod above upper Z with caron", "sbrief": "mod above upper Z with caron"}}}, {"category": "<PERSON>", "key": "01CD", "mappings": {"default": {"default": "latin capital letter a with caron", "alternative": "latin capital letter a hacek", "short": "cap a caron"}, "mathspeak": {"default": "modifying above upper A with caron", "brief": "mod above upper A with caron", "sbrief": "mod above upper A with caron"}}}, {"category": "<PERSON>", "key": "01CF", "mappings": {"default": {"default": "latin capital letter i with caron", "alternative": "latin capital letter i hacek", "short": "cap i caron"}, "mathspeak": {"default": "modifying above upper I with caron", "brief": "mod above upper I with caron", "sbrief": "mod above upper I with caron"}}}, {"category": "<PERSON>", "key": "01D1", "mappings": {"default": {"default": "latin capital letter o with caron", "alternative": "latin capital letter o hacek", "short": "cap o caron"}, "mathspeak": {"default": "modifying above upper O with caron", "brief": "mod above upper O with caron", "sbrief": "mod above upper O with caron"}}}, {"category": "<PERSON>", "key": "01D3", "mappings": {"default": {"default": "latin capital letter u with caron", "alternative": "latin capital letter u hacek", "short": "cap u caron"}, "mathspeak": {"default": "modifying above upper U with caron", "brief": "mod above upper U with caron", "sbrief": "mod above upper U with caron"}}}, {"category": "<PERSON>", "key": "01E6", "mappings": {"default": {"default": "latin capital letter g with caron", "alternative": "latin capital letter g hacek", "short": "cap g caron"}, "mathspeak": {"default": "modifying above upper G with caron", "brief": "mod above upper G with caron", "sbrief": "mod above upper G with caron"}}}, {"category": "<PERSON>", "key": "01E8", "mappings": {"default": {"default": "latin capital letter k with caron", "alternative": "latin capital letter k hacek", "short": "cap k caron"}, "mathspeak": {"default": "modifying above upper K with caron", "brief": "mod above upper K with caron", "sbrief": "mod above upper K with caron"}}}, {"category": "<PERSON>", "key": "01EA", "mappings": {"default": {"default": "latin capital letter o with ogonek", "alternative": "latin capital letter o ogonek", "short": "cap o ogonek"}, "mathspeak": {"default": "modifying above upper O with ogonek", "brief": "mod above upper O with ogonek", "sbrief": "mod above upper O with ogonek"}}}, {"category": "<PERSON>", "key": "01F4", "mappings": {"default": {"default": "latin capital letter g with acute", "alternative": "latin capital letter g acute", "short": "cap g acute"}, "mathspeak": {"default": "modifying above upper G with acute", "brief": "mod above upper G with acute", "sbrief": "mod above upper G with acute"}}}, {"category": "<PERSON>", "key": "01F8", "mappings": {"default": {"default": "latin capital letter n with grave", "alternative": "latin capital letter n grave", "short": "cap n grave"}, "mathspeak": {"default": "modifying above upper N with grave", "brief": "mod above upper N with grave", "sbrief": "mod above upper N with grave"}}}, {"category": "<PERSON>", "key": "0200", "mappings": {"default": {"default": "latin capital letter a with double grave", "alternative": "latin capital letter a double grave", "short": "cap a double grave"}, "mathspeak": {"default": "modifying above upper A with double grave", "brief": "mod above upper A with double grave", "sbrief": "mod above upper A with double grave"}}}, {"category": "<PERSON>", "key": "0202", "mappings": {"default": {"default": "latin capital letter a with inverted breve", "alternative": "latin capital letter a inverted breve", "short": "cap a inverted breve"}, "mathspeak": {"default": "modifying above upper A with inverted breve", "brief": "mod above upper A with inverted breve", "sbrief": "mod above upper A with inverted breve"}}}, {"category": "<PERSON>", "key": "0204", "mappings": {"default": {"default": "latin capital letter e with double grave", "alternative": "latin capital letter e double grave", "short": "cap e double grave"}, "mathspeak": {"default": "modifying above upper E with double grave", "brief": "mod above upper E with double grave", "sbrief": "mod above upper E with double grave"}}}, {"category": "<PERSON>", "key": "0206", "mappings": {"default": {"default": "latin capital letter e with inverted breve", "alternative": "latin capital letter e inverted breve", "short": "cap e inverted breve"}, "mathspeak": {"default": "modifying above upper E with inverted breve", "brief": "mod above upper E with inverted breve", "sbrief": "mod above upper E with inverted breve"}}}, {"category": "<PERSON>", "key": "0208", "mappings": {"default": {"default": "latin capital letter i with double grave", "alternative": "latin capital letter i double grave", "short": "cap i double grave"}, "mathspeak": {"default": "modifying above upper I with double grave", "brief": "mod above upper I with double grave", "sbrief": "mod above upper I with double grave"}}}, {"category": "<PERSON>", "key": "020A", "mappings": {"default": {"default": "latin capital letter i with inverted breve", "alternative": "latin capital letter i inverted breve", "short": "cap i inverted breve"}, "mathspeak": {"default": "modifying above upper I with inverted breve", "brief": "mod above upper I with inverted breve", "sbrief": "mod above upper I with inverted breve"}}}, {"category": "<PERSON>", "key": "020C", "mappings": {"default": {"default": "latin capital letter o with double grave", "alternative": "latin capital letter o double grave", "short": "cap o double grave"}, "mathspeak": {"default": "modifying above upper O with double grave", "brief": "mod above upper O with double grave", "sbrief": "mod above upper O with double grave"}}}, {"category": "<PERSON>", "key": "020E", "mappings": {"default": {"default": "latin capital letter o with inverted breve", "alternative": "latin capital letter o inverted breve", "short": "cap o inverted breve"}, "mathspeak": {"default": "modifying above upper O with inverted breve", "brief": "mod above upper O with inverted breve", "sbrief": "mod above upper O with inverted breve"}}}, {"category": "<PERSON>", "key": "0210", "mappings": {"default": {"default": "latin capital letter r with double grave", "alternative": "latin capital letter r double grave", "short": "cap r double grave"}, "mathspeak": {"default": "modifying above upper R with double grave", "brief": "mod above upper R with double grave", "sbrief": "mod above upper R with double grave"}}}, {"category": "<PERSON>", "key": "0212", "mappings": {"default": {"default": "latin capital letter r with inverted breve", "alternative": "latin capital letter r inverted breve", "short": "cap r inverted breve"}, "mathspeak": {"default": "modifying above upper R with inverted breve", "brief": "mod above upper R with inverted breve", "sbrief": "mod above upper R with inverted breve"}}}, {"category": "<PERSON>", "key": "0214", "mappings": {"default": {"default": "latin capital letter u with double grave", "alternative": "latin capital letter u double grave", "short": "cap u double grave"}, "mathspeak": {"default": "modifying above upper U with double grave", "brief": "mod above upper U with double grave", "sbrief": "mod above upper U with double grave"}}}, {"category": "<PERSON>", "key": "0216", "mappings": {"default": {"default": "latin capital letter u with inverted breve", "alternative": "latin capital letter u inverted breve", "short": "cap u inverted breve"}, "mathspeak": {"default": "modifying above upper U with inverted breve", "brief": "mod above upper U with inverted breve", "sbrief": "mod above upper U with inverted breve"}}}, {"category": "<PERSON>", "key": "0218", "mappings": {"default": {"default": "latin capital letter s with comma below", "alternative": "latin capital letter s comma below", "short": "cap s comma below"}, "mathspeak": {"default": "modifying below upper S with comma below", "brief": "mod below upper S with comma below", "sbrief": "mod below upper S with comma below"}}}, {"category": "<PERSON>", "key": "021A", "mappings": {"default": {"default": "latin capital letter t with comma below", "alternative": "latin capital letter t comma below", "short": "cap t comma below"}, "mathspeak": {"default": "modifying below upper T with comma below", "brief": "mod below upper T with comma below", "sbrief": "mod below upper T with comma below"}}}, {"category": "<PERSON>", "key": "021E", "mappings": {"default": {"default": "latin capital letter h caron", "short": "cap h caron"}, "mathspeak": {"default": "modifying above upper H with caron", "brief": "mod above upper H with caron", "sbrief": "mod above upper H with caron"}}}, {"category": "<PERSON>", "key": "0226", "mappings": {"default": {"default": "latin capital letter a with dot above", "alternative": "latin capital letter a overdot", "short": "cap a overdot"}, "mathspeak": {"default": "modifying above upper A with dot", "brief": "mod above upper A with dot", "sbrief": "mod above upper A with dot"}}}, {"category": "<PERSON>", "key": "0228", "mappings": {"default": {"default": "latin capital letter e with cedilla", "alternative": "latin capital letter e cedilla", "short": "cap e cedilla"}, "mathspeak": {"default": "modifying above upper E with cedilla", "brief": "mod above upper E with cedilla", "sbrief": "mod above upper E with cedilla"}}}, {"category": "<PERSON>", "key": "022E", "mappings": {"default": {"default": "latin capital letter o with dot above", "alternative": "latin capital letter o overdot", "short": "cap o overdot"}, "mathspeak": {"default": "modifying above upper O with dot", "brief": "mod above upper O with dot", "sbrief": "mod above upper O with dot"}}}, {"category": "<PERSON>", "key": "0232", "mappings": {"default": {"default": "latin capital letter y with macron", "alternative": "latin capital letter y overbar", "short": "cap y overbar"}, "mathspeak": {"default": "upper Y overbar", "brief": "upper Y overbar", "sbrief": "upper Y overbar"}}}, {"category": "<PERSON>", "key": "1E00", "mappings": {"default": {"default": "latin capital letter a with ring below", "alternative": "latin capital letter a ring below", "short": "cap a ring below"}, "mathspeak": {"default": "modifying below upper A with ring below", "brief": "mod below upper A with ring below", "sbrief": "mod below upper A with ring below"}}}, {"category": "<PERSON>", "key": "1E02", "mappings": {"default": {"default": "latin capital letter b with dot above", "alternative": "latin capital letter b overdot", "short": "cap b overdot"}, "mathspeak": {"default": "modifying above upper B with dot", "brief": "mod above upper B with dot", "sbrief": "mod above upper B with dot"}}}, {"category": "<PERSON>", "key": "1E04", "mappings": {"default": {"default": "latin capital letter b with dot below", "alternative": "latin capital letter b underdot", "short": "cap b underdot"}, "mathspeak": {"default": "modifying below upper B with dot", "brief": "mod below upper B with dot", "sbrief": "mod below upper B with dot"}}}, {"category": "<PERSON>", "key": "1E06", "mappings": {"default": {"default": "latin capital letter b with line below", "alternative": "latin capital letter b underbar", "short": "cap b underbar"}, "mathspeak": {"default": "upper B underbar", "brief": "upper B underbar", "sbrief": "upper B underbar"}}}, {"category": "<PERSON>", "key": "1E0A", "mappings": {"default": {"default": "latin capital letter d with dot above", "alternative": "latin capital letter d overdot", "short": "cap d overdot"}, "mathspeak": {"default": "modifying above upper D with dot", "brief": "mod above upper D with dot", "sbrief": "mod above upper D with dot"}}}, {"category": "<PERSON>", "key": "1E0C", "mappings": {"default": {"default": "latin capital letter d with dot below", "alternative": "latin capital letter d underdot", "short": "cap d underdot"}, "mathspeak": {"default": "modifying below upper D with dot", "brief": "mod below upper D with dot", "sbrief": "mod below upper D with dot"}}}, {"category": "<PERSON>", "key": "1E0E", "mappings": {"default": {"default": "latin capital letter d with line below", "alternative": "latin capital letter d underbar", "short": "cap d underbar"}, "mathspeak": {"default": "upper D underbar", "brief": "upper D underbar", "sbrief": "upper D underbar"}}}, {"category": "<PERSON>", "key": "1E10", "mappings": {"default": {"default": "latin capital letter d with cedilla", "alternative": "latin capital letter d cedilla", "short": "cap d cedilla"}, "mathspeak": {"default": "modifying above upper D with cedilla", "brief": "mod above upper D with cedilla", "sbrief": "mod above upper D with cedilla"}}}, {"category": "<PERSON>", "key": "1E12", "mappings": {"default": {"default": "latin capital letter d with circumflex below", "alternative": "latin capital letter d underhat", "short": "cap d underhat"}, "mathspeak": {"default": "modifying below upper D with caret", "brief": "mod below upper D with caret", "sbrief": "mod below upper D with caret"}}}, {"category": "<PERSON>", "key": "1E18", "mappings": {"default": {"default": "latin capital letter e with circumflex below", "alternative": "latin capital letter e underhat", "short": "cap e underhat"}, "mathspeak": {"default": "modifying below upper E with caret", "brief": "mod below upper E with caret", "sbrief": "mod below upper E with caret"}}}, {"category": "<PERSON>", "key": "1E1A", "mappings": {"default": {"default": "latin capital letter e with tilde below", "alternative": "latin capital letter e tilde below", "short": "cap e tilde below"}, "mathspeak": {"default": "upper E undertilde", "brief": "upper E undertilde", "sbrief": "upper E undertilde"}}}, {"category": "<PERSON>", "key": "1E1E", "mappings": {"default": {"default": "latin capital letter f with dot above", "alternative": "latin capital letter f overdot", "short": "cap f overdot"}, "mathspeak": {"default": "modifying above upper F with dot", "brief": "mod above upper F with dot", "sbrief": "mod above upper F with dot"}}}, {"category": "<PERSON>", "key": "1E20", "mappings": {"default": {"default": "latin capital letter g with macron", "alternative": "latin capital letter g overbar", "short": "cap g overbar"}, "mathspeak": {"default": "upper G overbar", "brief": "upper G overbar", "sbrief": "upper G overbar"}}}, {"category": "<PERSON>", "key": "1E22", "mappings": {"default": {"default": "latin capital letter h with dot above", "alternative": "latin capital letter h overdot", "short": "cap h overdot"}, "mathspeak": {"default": "modifying above upper H with dot", "brief": "mod above upper H with dot", "sbrief": "mod above upper H with dot"}}}, {"category": "<PERSON>", "key": "1E24", "mappings": {"default": {"default": "latin capital letter h with dot below", "alternative": "latin capital letter h underdot", "short": "cap h underdot"}, "mathspeak": {"default": "modifying below upper H with dot", "brief": "mod below upper H with dot", "sbrief": "mod below upper H with dot"}}}, {"category": "<PERSON>", "key": "1E26", "mappings": {"default": {"default": "latin capital letter h with diaeresis", "alternative": "latin capital letter h double overdot", "short": "cap h double overdot"}, "mathspeak": {"default": "modifying above upper H with double dot", "brief": "mod above upper H with double dot", "sbrief": "mod above upper H with double dot"}}}, {"category": "<PERSON>", "key": "1E28", "mappings": {"default": {"default": "latin capital letter h with cedilla", "alternative": "latin capital letter h cedilla", "short": "cap h cedilla"}, "mathspeak": {"default": "modifying above upper H with cedilla", "brief": "mod above upper H with cedilla", "sbrief": "mod above upper H with cedilla"}}}, {"category": "<PERSON>", "key": "1E2A", "mappings": {"default": {"default": "latin capital letter h with breve below", "alternative": "latin capital letter h breve below", "short": "cap h breve below"}, "mathspeak": {"default": "modifying below upper H with breve below", "brief": "mod below upper H with breve below", "sbrief": "mod below upper H with breve below"}}}, {"category": "<PERSON>", "key": "1E2C", "mappings": {"default": {"default": "latin capital letter i with tilde below", "alternative": "latin capital letter i tilde below", "short": "cap i tilde below"}, "mathspeak": {"default": "upper I undertilde", "brief": "upper I undertilde", "sbrief": "upper I undertilde"}}}, {"category": "<PERSON>", "key": "1E30", "mappings": {"default": {"default": "latin capital letter k with acute", "alternative": "latin capital letter k acute", "short": "cap k acute"}, "mathspeak": {"default": "modifying above upper K with acute", "brief": "mod above upper K with acute", "sbrief": "mod above upper K with acute"}}}, {"category": "<PERSON>", "key": "1E32", "mappings": {"default": {"default": "latin capital letter k with dot below", "alternative": "latin capital letter k underdot", "short": "cap k underdot"}, "mathspeak": {"default": "modifying below upper K with dot", "brief": "mod below upper K with dot", "sbrief": "mod below upper K with dot"}}}, {"category": "<PERSON>", "key": "1E34", "mappings": {"default": {"default": "latin capital letter k with line below", "alternative": "latin capital letter k underbar", "short": "cap k underbar"}, "mathspeak": {"default": "upper K underbar", "brief": "upper K underbar", "sbrief": "upper K underbar"}}}, {"category": "<PERSON>", "key": "1E36", "mappings": {"default": {"default": "latin capital letter l with dot below", "alternative": "latin capital letter l underdot", "short": "cap l underdot"}, "mathspeak": {"default": "modifying below upper L with dot", "brief": "mod below upper L with dot", "sbrief": "mod below upper L with dot"}}}, {"category": "<PERSON>", "key": "1E3A", "mappings": {"default": {"default": "latin capital letter l with line below", "alternative": "latin capital letter l underbar", "short": "cap l underbar"}, "mathspeak": {"default": "upper L underbar", "brief": "upper L underbar", "sbrief": "upper L underbar"}}}, {"category": "<PERSON>", "key": "1E3C", "mappings": {"default": {"default": "latin capital letter l with circumflex below", "alternative": "latin capital letter l underhat", "short": "cap l underhat"}, "mathspeak": {"default": "modifying below upper L with caret", "brief": "mod below upper L with caret", "sbrief": "mod below upper L with caret"}}}, {"category": "<PERSON>", "key": "1E3E", "mappings": {"default": {"default": "latin capital letter m with acute", "alternative": "latin capital letter m acute", "short": "cap m acute"}, "mathspeak": {"default": "modifying above upper M with acute", "brief": "mod above upper M with acute", "sbrief": "mod above upper M with acute"}}}, {"category": "<PERSON>", "key": "1E40", "mappings": {"default": {"default": "latin capital letter m with dot above", "alternative": "latin capital letter m overdot", "short": "cap m overdot"}, "mathspeak": {"default": "modifying above upper M with dot", "brief": "mod above upper M with dot", "sbrief": "mod above upper M with dot"}}}, {"category": "<PERSON>", "key": "1E42", "mappings": {"default": {"default": "latin capital letter m with dot below", "alternative": "latin capital letter m underdot", "short": "cap m underdot"}, "mathspeak": {"default": "modifying below upper M with dot", "brief": "mod below upper M with dot", "sbrief": "mod below upper M with dot"}}}, {"category": "<PERSON>", "key": "1E44", "mappings": {"default": {"default": "latin capital letter n with dot above", "alternative": "latin capital letter n overdot", "short": "cap n overdot"}, "mathspeak": {"default": "modifying above upper N with dot", "brief": "mod above upper N with dot", "sbrief": "mod above upper N with dot"}}}, {"category": "<PERSON>", "key": "1E46", "mappings": {"default": {"default": "latin capital letter n with dot below", "alternative": "latin capital letter n underdot", "short": "cap n underdot"}, "mathspeak": {"default": "modifying below upper N with dot", "brief": "mod below upper N with dot", "sbrief": "mod below upper N with dot"}}}, {"category": "<PERSON>", "key": "1E48", "mappings": {"default": {"default": "latin capital letter n with line below", "alternative": "latin capital letter n underbar", "short": "cap n underbar"}, "mathspeak": {"default": "upper N underbar", "brief": "upper N underbar", "sbrief": "upper N underbar"}}}, {"category": "<PERSON>", "key": "1E4A", "mappings": {"default": {"default": "latin capital letter n with circumflex below", "alternative": "latin capital letter n underhat", "short": "cap n underhat"}, "mathspeak": {"default": "modifying below upper N with caret", "brief": "mod below upper N with caret", "sbrief": "mod below upper N with caret"}}}, {"category": "<PERSON>", "key": "1E54", "mappings": {"default": {"default": "latin capital letter p with acute", "alternative": "latin capital letter p acute", "short": "cap p acute"}, "mathspeak": {"default": "modifying above upper P with acute", "brief": "mod above upper P with acute", "sbrief": "mod above upper P with acute"}}}, {"category": "<PERSON>", "key": "1E56", "mappings": {"default": {"default": "latin capital letter p with dot above", "alternative": "latin capital letter p overdot", "short": "cap p overdot"}, "mathspeak": {"default": "modifying above upper P with dot", "brief": "mod above upper P with dot", "sbrief": "mod above upper P with dot"}}}, {"category": "<PERSON>", "key": "1E58", "mappings": {"default": {"default": "latin capital letter r with dot above", "alternative": "latin capital letter r overdot", "short": "cap r overdot"}, "mathspeak": {"default": "modifying above upper R with dot", "brief": "mod above upper R with dot", "sbrief": "mod above upper R with dot"}}}, {"category": "<PERSON>", "key": "1E5A", "mappings": {"default": {"default": "latin capital letter r with dot below", "alternative": "latin capital letter r underdot", "short": "cap r underdot"}, "mathspeak": {"default": "modifying below upper R with dot", "brief": "mod below upper R with dot", "sbrief": "mod below upper R with dot"}}}, {"category": "<PERSON>", "key": "1E5E", "mappings": {"default": {"default": "latin capital letter r with line below", "alternative": "latin capital letter r underbar", "short": "cap r underbar"}, "mathspeak": {"default": "upper R underbar", "brief": "upper R underbar", "sbrief": "upper R underbar"}}}, {"category": "<PERSON>", "key": "1E60", "mappings": {"default": {"default": "latin capital letter s with dot above", "alternative": "latin capital letter s overdot", "short": "cap s overdot"}, "mathspeak": {"default": "modifying above upper S with dot", "brief": "mod above upper S with dot", "sbrief": "mod above upper S with dot"}}}, {"category": "<PERSON>", "key": "1E62", "mappings": {"default": {"default": "latin capital letter s with dot below", "alternative": "latin capital letter s underdot", "short": "cap s underdot"}, "mathspeak": {"default": "modifying below upper S with dot", "brief": "mod below upper S with dot", "sbrief": "mod below upper S with dot"}}}, {"category": "<PERSON>", "key": "1E6A", "mappings": {"default": {"default": "latin capital letter t with dot above", "alternative": "latin capital letter t overdot", "short": "cap t overdot"}, "mathspeak": {"default": "modifying above upper T with dot", "brief": "mod above upper T with dot", "sbrief": "mod above upper T with dot"}}}, {"category": "<PERSON>", "key": "1E6C", "mappings": {"default": {"default": "latin capital letter t with dot below", "alternative": "latin capital letter t underdot", "short": "cap t underdot"}, "mathspeak": {"default": "modifying below upper T with dot", "brief": "mod below upper T with dot", "sbrief": "mod below upper T with dot"}}}, {"category": "<PERSON>", "key": "1E6E", "mappings": {"default": {"default": "latin capital letter t with line below", "alternative": "latin capital letter t underbar", "short": "cap t underbar"}, "mathspeak": {"default": "upper T underbar", "brief": "upper T underbar", "sbrief": "upper T underbar"}}}, {"category": "<PERSON>", "key": "1E70", "mappings": {"default": {"default": "latin capital letter t with circumflex below", "alternative": "latin capital letter t underhat", "short": "cap t underhat"}, "mathspeak": {"default": "modifying below upper T with caret", "brief": "mod below upper T with caret", "sbrief": "mod below upper T with caret"}}}, {"category": "<PERSON>", "key": "1E72", "mappings": {"default": {"default": "latin capital letter u with diaeresis below", "alternative": "latin capital letter u double underdot", "short": "cap u double underdot"}, "mathspeak": {"default": "modifying below upper U with double dot", "brief": "mod below upper U with double dot", "sbrief": "mod below upper U with double dot"}}}, {"category": "<PERSON>", "key": "1E74", "mappings": {"default": {"default": "latin capital letter u with tilde below", "alternative": "latin capital letter u tilde below", "short": "cap u tilde below"}, "mathspeak": {"default": "upper U undertilde", "brief": "upper U undertilde", "sbrief": "upper U undertilde"}}}, {"category": "<PERSON>", "key": "1E76", "mappings": {"default": {"default": "latin capital letter u with circumflex below", "alternative": "latin capital letter u underhat", "short": "cap u underhat"}, "mathspeak": {"default": "modifying below upper U with caret", "brief": "mod below upper U with caret", "sbrief": "mod below upper U with caret"}}}, {"category": "<PERSON>", "key": "1E7C", "mappings": {"default": {"default": "latin capital letter v with tilde", "alternative": "latin capital letter v tilde", "short": "cap v tilde"}, "mathspeak": {"default": "upper V overtilde", "brief": "upper V overtilde", "sbrief": "upper V overtilde"}}}, {"category": "<PERSON>", "key": "1E7E", "mappings": {"default": {"default": "latin capital letter v with dot below", "alternative": "latin capital letter v underdot", "short": "cap v underdot"}, "mathspeak": {"default": "modifying below upper V with dot", "brief": "mod below upper V with dot", "sbrief": "mod below upper V with dot"}}}, {"category": "<PERSON>", "key": "1E80", "mappings": {"default": {"default": "latin capital letter w with grave", "alternative": "latin capital letter w grave", "short": "cap w grave"}, "mathspeak": {"default": "modifying above upper W with grave", "brief": "mod above upper W with grave", "sbrief": "mod above upper W with grave"}}}, {"category": "<PERSON>", "key": "1E82", "mappings": {"default": {"default": "latin capital letter w with acute", "alternative": "latin capital letter w acute", "short": "cap w acute"}, "mathspeak": {"default": "modifying above upper W with acute", "brief": "mod above upper W with acute", "sbrief": "mod above upper W with acute"}}}, {"category": "<PERSON>", "key": "1E84", "mappings": {"default": {"default": "latin capital letter w with diaeresis", "alternative": "latin capital letter w double overdot", "short": "cap w double overdot"}, "mathspeak": {"default": "modifying above upper W with double dot", "brief": "mod above upper W with double dot", "sbrief": "mod above upper W with double dot"}}}, {"category": "<PERSON>", "key": "1E86", "mappings": {"default": {"default": "latin capital letter w with dot above", "alternative": "latin capital letter w overdot", "short": "cap w overdot"}, "mathspeak": {"default": "modifying above upper W with dot", "brief": "mod above upper W with dot", "sbrief": "mod above upper W with dot"}}}, {"category": "<PERSON>", "key": "1E88", "mappings": {"default": {"default": "latin capital letter w with dot below", "alternative": "latin capital letter w underdot", "short": "cap w underdot"}, "mathspeak": {"default": "modifying below upper W with dot", "brief": "mod below upper W with dot", "sbrief": "mod below upper W with dot"}}}, {"category": "<PERSON>", "key": "1E8A", "mappings": {"default": {"default": "latin capital letter x with dot above", "alternative": "latin capital letter x overdot", "short": "cap x overdot"}, "mathspeak": {"default": "modifying above upper X with dot", "brief": "mod above upper X with dot", "sbrief": "mod above upper X with dot"}}}, {"category": "<PERSON>", "key": "1E8C", "mappings": {"default": {"default": "latin capital letter x with diaeresis", "alternative": "latin capital letter x double overdot", "short": "cap x double overdot"}, "mathspeak": {"default": "modifying above upper X with double dot", "brief": "mod above upper X with double dot", "sbrief": "mod above upper X with double dot"}}}, {"category": "<PERSON>", "key": "1E8E", "mappings": {"default": {"default": "latin capital letter y with dot above", "alternative": "latin capital letter y overdot", "short": "cap y overdot"}, "mathspeak": {"default": "modifying above upper Y with dot", "brief": "mod above upper Y with dot", "sbrief": "mod above upper Y with dot"}}}, {"category": "<PERSON>", "key": "1E90", "mappings": {"default": {"default": "latin capital letter z with circumflex", "alternative": "latin capital letter z circumflex", "short": "cap z circumflex"}, "mathspeak": {"default": "modifying above upper Z with circumflex", "brief": "mod above upper Z with circumflex", "sbrief": "mod above upper Z with circumflex"}}}, {"category": "<PERSON>", "key": "1E92", "mappings": {"default": {"default": "latin capital letter z with dot below", "alternative": "latin capital letter z underdot", "short": "cap z underdot"}, "mathspeak": {"default": "modifying below upper Z with dot", "brief": "mod below upper Z with dot", "sbrief": "mod below upper Z with dot"}}}, {"category": "<PERSON>", "key": "1E94", "mappings": {"default": {"default": "latin capital letter z with line below", "alternative": "latin capital letter z underbar", "short": "cap z underbar"}, "mathspeak": {"default": "upper Z underbar", "brief": "upper Z underbar", "sbrief": "upper Z underbar"}}}, {"category": "<PERSON>", "key": "1EA0", "mappings": {"default": {"default": "latin capital letter with a dot below", "alternative": "latin capital letter with a underdot", "short": "cap a underdot"}, "mathspeak": {"default": "modifying below upper A with dot", "brief": "mod below upper A with dot", "sbrief": "mod below upper A with dot"}}}, {"category": "<PERSON>", "key": "1EA2", "mappings": {"default": {"default": "latin capital letter a with hook above", "alternative": "latin capital letter a hook", "short": "cap a hook"}, "mathspeak": {"default": "modifying above upper A with hook", "brief": "mod above upper A with hook", "sbrief": "mod above upper A with hook"}}}, {"category": "<PERSON>", "key": "1EB8", "mappings": {"default": {"default": "latin capital letter e with dot below", "alternative": "latin capital letter e underdot", "short": "cap e underdot"}, "mathspeak": {"default": "modifying below upper E with dot", "brief": "mod below upper E with dot", "sbrief": "mod below upper E with dot"}}}, {"category": "<PERSON>", "key": "1EBA", "mappings": {"default": {"default": "latin capital letter e with hook above", "alternative": "latin capital letter e hook", "short": "cap e hook"}, "mathspeak": {"default": "modifying above upper E with hook", "brief": "mod above upper E with hook", "sbrief": "mod above upper E with hook"}}}, {"category": "<PERSON>", "key": "1EBC", "mappings": {"default": {"default": "latin capital letter e with tilde", "alternative": "latin capital letter e tilde", "short": "cap e tilde"}, "mathspeak": {"default": "upper E overtilde", "brief": "upper E overtilde", "sbrief": "upper E overtilde"}}}, {"category": "<PERSON>", "key": "1EC8", "mappings": {"default": {"default": "latin capital letter i with hook above", "alternative": "latin capital letter i hook", "short": "cap i hook"}, "mathspeak": {"default": "modifying above upper I with hook", "brief": "mod above upper I with hook", "sbrief": "mod above upper I with hook"}}}, {"category": "<PERSON>", "key": "1ECA", "mappings": {"default": {"default": "latin capital letter i with dot below", "alternative": "latin capital letter i underdot", "short": "cap i underdot"}, "mathspeak": {"default": "modifying below upper I with dot", "brief": "mod below upper I with dot", "sbrief": "mod below upper I with dot"}}}, {"category": "<PERSON>", "key": "1ECC", "mappings": {"default": {"default": "latin capital letter o with dot below", "alternative": "latin capital letter o underdot", "short": "cap o underdot"}, "mathspeak": {"default": "modifying below upper O with dot", "brief": "mod below upper O with dot", "sbrief": "mod below upper O with dot"}}}, {"category": "<PERSON>", "key": "1ECE", "mappings": {"default": {"default": "latin capital letter o with hook above", "alternative": "latin capital letter o hook", "short": "cap o hook"}, "mathspeak": {"default": "modifying above upper O with hook", "brief": "mod above upper O with hook", "sbrief": "mod above upper O with hook"}}}, {"category": "<PERSON>", "key": "1EE4", "mappings": {"default": {"default": "latin capital letter u with dot below", "alternative": "latin capital letter u underdot", "short": "cap u underdot"}, "mathspeak": {"default": "modifying below upper U with dot", "brief": "mod below upper U with dot", "sbrief": "mod below upper U with dot"}}}, {"category": "<PERSON>", "key": "1EE6", "mappings": {"default": {"default": "latin capital letter u with hook above", "alternative": "latin capital letter u hook", "short": "cap u hook"}, "mathspeak": {"default": "modifying above upper U with hook", "brief": "mod above upper U with hook", "sbrief": "mod above upper U with hook"}}}, {"category": "<PERSON>", "key": "1EF2", "mappings": {"default": {"default": "latin capital letter y with grave", "alternative": "latin capital letter y grave", "short": "cap y grave"}, "mathspeak": {"default": "modifying above upper Y with grave", "brief": "mod above upper Y with grave", "sbrief": "mod above upper Y with grave"}}}, {"category": "<PERSON>", "key": "1EF4", "mappings": {"default": {"default": "latin capital letter y with dot below", "alternative": "latin capital letter y underdot", "short": "cap y underdot"}, "mathspeak": {"default": "modifying below upper Y with dot", "brief": "mod below upper Y with dot", "sbrief": "mod below upper Y with dot"}}}, {"category": "<PERSON>", "key": "1EF6", "mappings": {"default": {"default": "latin capital letter y with hook above", "alternative": "latin capital letter y hook", "short": "cap y hook"}, "mathspeak": {"default": "modifying above upper Y with hook", "brief": "mod above upper Y with hook", "sbrief": "mod above upper Y with hook"}}}, {"category": "<PERSON>", "key": "1EF8", "mappings": {"default": {"default": "latin capital letter y with tilde", "alternative": "latin capital letter y tilde", "short": "cap y tilde"}, "mathspeak": {"default": "upper Y overtilde", "brief": "upper Y overtilde", "sbrief": "upper Y overtilde"}}}]