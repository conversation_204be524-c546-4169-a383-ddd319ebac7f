/*
 *  /MathJax/jax/output/HTML-CSS/fonts/Gyre-<PERSON>lla/DoubleStruck/Regular/Main.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.OutputJax["HTML-CSS"].FONTDATA.FONTS.GyrePagellaMathJax_DoubleStruck={directory:"DoubleStruck/Regular",family:"GyrePagellaMathJax_DoubleStruck",testString:"\u00A0\u2102\u210D\u2115\u2119\u211A\u211D\u2124\u213C\u213D\u213E\u213F\u2140\u2145\u2146",32:[0,0,250,0,0],160:[0,0,250,0,0],8450:[698,12,858,80,778],8461:[686,0,960,80,880],8469:[686,12,887,80,807],8473:[686,0,790,80,710],8474:[698,187,936,80,856],8477:[686,0,907,80,827],8484:[686,0,776,80,696],8508:[493,11,797,80,717],8509:[482,229,723,80,643],8510:[686,0,690,80,610],8511:[686,0,960,80,880],8512:[750,250,1054,80,974],8517:[686,0,903,63,869],8518:[695,12,723,75,694],8519:[481,12,644,77,583],8520:[715,0,442,40,401],8521:[715,272,446,-5,461],120120:[694,0,920,80,840],120121:[686,0,784,80,704],120123:[686,0,903,80,823],120124:[686,0,723,80,643],120125:[686,0,690,80,610],120126:[698,12,925,80,845],120128:[686,0,485,80,405],120129:[686,187,551,80,471],120130:[686,0,896,80,816],120131:[686,0,713,80,633],120132:[690,8,1037,80,957],120134:[698,12,936,80,856],120138:[698,12,693,80,613],120139:[686,0,824,80,744],120140:[686,12,899,80,819],120141:[686,8,902,80,822],120142:[694,8,1135,80,1055],120143:[697,0,831,80,751],120144:[697,0,802,80,722],120146:[481,12,663,80,583],120147:[695,18,728,80,648],120148:[481,12,603,79,523],120149:[695,12,723,80,643],120150:[481,12,644,80,564],120151:[700,0,530,80,450],120152:[481,245,707,80,627],120153:[695,0,765,80,685],120154:[715,0,442,80,362],120155:[715,272,446,80,366],120156:[695,15,749,80,669],120157:[695,0,442,80,362],120158:[489,0,1083,80,1003],120159:[489,0,765,80,685],120160:[481,12,706,80,626],120161:[489,240,728,80,648],120162:[484,240,729,80,649],120163:[489,0,570,80,490],120164:[481,12,598,80,518],120165:[624,12,520,80,440],120166:[489,12,774,80,694],120167:[466,8,672,80,592],120168:[474,8,867,80,787],120169:[486,0,670,80,590],120170:[466,238,711,80,631],120171:[466,0,685,80,605],120792:[681,12,660,80,580],120793:[686,0,560,80,480],120794:[681,0,623,80,543],120795:[681,12,666,80,586],120796:[696,0,676,80,596],120797:[698,12,656,80,576],120798:[686,12,680,80,600],120799:[693,0,616,80,536],120800:[681,12,668,80,588],120801:[681,19,680,80,600]};MathJax.Callback.Queue(["initFont",MathJax.OutputJax["HTML-CSS"],"GyrePagellaMathJax_DoubleStruck"],["loadComplete",MathJax.Ajax,MathJax.OutputJax["HTML-CSS"].fontDir+"/DoubleStruck/Regular/Main.js"]);
