/*
 *  /MathJax/fonts/HTML-CSS/TeX/png/AMS/Regular/LatinExtendedA.js
 *  
 *  Copyright (c) 2010-2013 The MathJax Consortium
 *
 *  Part of the MathJax library.
 *  See http://www.mathjax.org for details.
 * 
 *  Licensed under the Apache License, Version 2.0;
 *  you may not use this file except in compliance with the License.
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 */

MathJax.OutputJax["HTML-CSS"].defineImageData({MathJax_AMS:{295:[[4,5,0],[5,6,0],[6,7,0],[7,8,0],[8,10,0],[10,12,0],[11,14,0],[13,16,0],[16,19,0],[19,23,0],[22,27,0],[26,34,1],[31,40,1],[37,47,1]]}});MathJax.Ajax.loadComplete(MathJax.OutputJax["HTML-CSS"].imgDir+"/AMS/Regular"+MathJax.OutputJax["HTML-CSS"].imgPacked+"/LatinExtendedA.js");

