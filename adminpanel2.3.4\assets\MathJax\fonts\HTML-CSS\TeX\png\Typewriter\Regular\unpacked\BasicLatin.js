/*************************************************************
 *
 *  MathJax/fonts/HTML-CSS/TeX/png/Typewriter/Regular/BasicLatin.js
 *  
 *  Defines the image size data needed for the HTML-CSS OutputJax
 *  to display mathematics using fallback images when the fonts
 *  are not available to the client browser.
 *
 *  ---------------------------------------------------------------------
 *
 *  Copyright (c) 2009-2013 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 *
 */

MathJax.OutputJax["HTML-CSS"].defineImageData({
  "MathJax_Typewriter": {
    0x20: [  // SPACE
      [1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],
      [1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0]
    ],
    0x21: [  // EXCLAMATION MARK
      [3,4,0],[3,6,0],[4,6,0],[4,7,0],[5,8,0],[6,10,0],[7,13,0],[8,14,0],
      [9,17,0],[11,21,0],[13,24,0],[15,28,0],[18,34,0],[21,41,0]
    ],
    0x22: [  // QUOTATION MARK
      [3,2,-2],[4,3,-3],[4,3,-3],[5,4,-3],[6,4,-4],[7,5,-5],[8,6,-7],[10,7,-7],
      [12,8,-9],[14,10,-11],[16,12,-12],[19,14,-14],[23,16,-18],[27,20,-21]
    ],
    0x23: [  // NUMBER SIGN
      [4,4,0],[5,6,0],[5,6,0],[6,7,0],[7,8,0],[9,10,0],[10,13,0],[12,14,0],
      [14,17,0],[16,21,0],[19,24,0],[23,28,0],[27,34,0],[32,41,0]
    ],
    0x24: [  // DOLLAR SIGN
      [4,6,1],[4,8,1],[5,8,1],[6,9,1],[7,11,2],[8,13,1],[10,16,1],[11,18,2],
      [13,22,3],[16,27,3],[19,31,4],[22,36,4],[26,43,4],[31,52,6]
    ],
    0x25: [  // PERCENT SIGN
      [4,5,1],[5,7,1],[5,7,1],[6,9,1],[7,11,1],[9,13,1],[10,16,2],[12,18,2],
      [14,22,2],[17,27,3],[20,31,3],[23,36,4],[28,43,5],[33,52,6]
    ],
    0x26: [  // AMPERSAND
      [4,4,0],[5,6,0],[5,6,0],[6,7,0],[7,8,0],[9,10,0],[10,13,0],[12,14,0],
      [14,17,0],[17,21,0],[20,24,0],[23,28,0],[27,34,0],[33,41,0]
    ],
    0x27: [  // APOSTROPHE
      [3,3,-1],[3,4,-2],[4,4,-2],[5,4,-3],[5,5,-3],[6,6,-4],[7,7,-6],[9,8,-6],
      [10,9,-8],[12,11,-10],[14,13,-11],[17,15,-13],[20,18,-16],[23,22,-19]
    ],
    0x28: [  // LEFT PARENTHESIS
      [4,6,1],[4,8,1],[5,8,1],[6,9,1],[7,11,1],[8,13,1],[9,16,2],[11,18,2],
      [13,22,2],[15,27,3],[18,31,3],[21,36,4],[25,43,4],[29,52,5]
    ],
    0x29: [  // RIGHT PARENTHESIS
      [3,6,1],[3,8,1],[4,8,1],[5,9,1],[5,11,1],[6,13,1],[7,16,2],[9,18,2],
      [10,22,2],[12,27,3],[14,31,3],[17,36,4],[20,43,4],[24,52,5]
    ],
    0x2A: [  // ASTERISK
      [4,3,-1],[4,4,-1],[5,4,-1],[6,5,-1],[7,6,-1],[8,7,-1],[9,9,-2],[11,10,-2],
      [13,12,-2],[15,15,-3],[18,17,-3],[22,20,-4],[26,24,-5],[30,29,-6]
    ],
    0x2B: [  // PLUS SIGN
      [4,4,-1],[5,5,-1],[5,5,-1],[6,6,-1],[7,7,-1],[9,8,-1],[10,10,-2],[12,11,-2],
      [14,13,-2],[17,16,-3],[20,18,-3],[23,21,-4],[27,25,-4],[33,30,-5]
    ],
    0x2C: [  // COMMA
      [3,2,1],[3,4,2],[4,4,2],[5,4,2],[5,4,2],[6,5,2],[7,6,3],[9,7,3],
      [10,8,4],[12,10,5],[14,12,6],[17,14,7],[20,16,8],[24,19,9]
    ],
    0x2D: [  // HYPHEN-MINUS
      [4,1,-2],[4,1,-2],[5,1,-2],[6,1,-3],[7,1,-4],[8,2,-4],[10,3,-5],[11,2,-6],
      [13,2,-7],[16,3,-9],[19,3,-11],[22,4,-12],[26,4,-15],[31,5,-18]
    ],
    0x2E: [  // FULL STOP
      [3,1,0],[3,2,0],[4,2,0],[4,2,0],[5,2,0],[6,3,0],[7,3,0],[8,4,0],
      [10,4,0],[11,5,0],[13,6,0],[16,7,0],[19,8,0],[22,10,0]
    ],
    0x2F: [  // SOLIDUS
      [4,6,1],[4,7,1],[5,7,1],[6,9,1],[7,11,1],[8,13,1],[10,16,2],[11,18,2],
      [13,22,2],[16,27,3],[19,30,3],[22,36,4],[26,43,5],[31,52,6]
    ],
    0x30: [  // DIGIT ZERO
      [4,4,0],[4,6,0],[5,6,0],[6,7,0],[7,8,0],[8,10,0],[10,13,0],[12,14,0],
      [14,17,0],[16,21,0],[19,24,0],[23,28,0],[27,34,0],[32,41,0]
    ],
    0x31: [  // DIGIT ONE
      [4,4,0],[4,6,0],[5,6,0],[6,7,0],[7,8,0],[8,10,0],[9,13,0],[11,14,0],
      [13,17,0],[15,21,0],[18,24,0],[21,28,0],[25,34,0],[30,41,0]
    ],
    0x32: [  // DIGIT TWO
      [4,4,0],[4,6,0],[5,6,0],[6,7,0],[7,8,0],[8,10,0],[10,13,0],[11,14,0],
      [13,17,0],[16,21,0],[19,24,0],[22,29,1],[26,34,0],[31,41,0]
    ],
    0x33: [  // DIGIT THREE
      [4,4,0],[4,6,0],[5,6,0],[6,7,0],[7,8,0],[8,10,0],[10,13,0],[11,14,0],
      [14,17,0],[16,21,0],[19,24,0],[22,28,0],[27,34,0],[32,41,0]
    ],
    0x34: [  // DIGIT FOUR
      [4,4,0],[5,6,0],[5,6,0],[6,7,0],[7,8,0],[9,10,0],[10,13,0],[12,14,0],
      [14,17,0],[17,21,0],[20,24,0],[24,28,0],[28,34,0],[33,41,0]
    ],
    0x35: [  // DIGIT FIVE
      [4,4,0],[4,6,0],[5,6,0],[6,7,0],[7,8,0],[8,10,0],[10,13,0],[11,14,0],
      [14,17,0],[16,21,0],[19,24,0],[22,28,0],[27,34,0],[32,41,0]
    ],
    0x36: [  // DIGIT SIX
      [4,4,0],[4,6,0],[5,6,0],[6,7,0],[7,8,0],[8,10,0],[10,13,0],[12,14,0],
      [14,17,0],[16,21,0],[19,24,0],[23,28,0],[27,35,0],[32,43,0]
    ],
    0x37: [  // DIGIT SEVEN
      [4,4,0],[4,6,0],[5,6,0],[6,7,0],[7,8,0],[8,10,0],[10,13,0],[12,14,0],
      [14,17,0],[16,21,0],[19,24,0],[23,28,0],[27,35,0],[32,42,0]
    ],
    0x38: [  // DIGIT EIGHT
      [4,4,0],[4,6,0],[5,6,0],[6,7,0],[7,8,0],[8,10,0],[10,13,0],[12,14,0],
      [14,17,0],[16,21,0],[19,24,0],[23,28,0],[27,34,0],[32,41,0]
    ],
    0x39: [  // DIGIT NINE
      [4,4,0],[4,6,0],[5,6,0],[6,7,0],[7,8,0],[8,10,0],[10,13,0],[12,14,0],
      [14,17,0],[16,21,0],[19,24,0],[23,28,0],[27,34,0],[32,41,0]
    ],
    0x3A: [  // COLON
      [3,3,0],[3,4,0],[4,4,0],[4,5,0],[5,6,0],[6,7,0],[7,9,0],[8,10,0],
      [10,12,0],[11,15,0],[13,17,0],[16,20,0],[19,24,0],[22,29,0]
    ],
    0x3B: [  // SEMICOLON
      [3,4,1],[3,6,2],[4,6,2],[4,7,2],[5,8,2],[6,10,3],[7,12,3],[8,14,4],
      [10,16,4],[12,20,5],[14,23,6],[16,27,7],[19,32,8],[23,39,10]
    ],
    0x3C: [  // LESS-THAN SIGN
      [4,4,0],[4,5,-1],[5,5,-1],[6,6,-1],[7,7,-1],[8,9,-1],[10,11,-1],[11,12,-1],
      [13,14,-2],[16,18,-2],[19,20,-2],[22,23,-3],[26,28,-3],[31,34,-4]
    ],
    0x3D: [  // EQUALS SIGN
      [4,2,-1],[5,2,-2],[5,2,-2],[6,3,-2],[7,3,-3],[9,4,-3],[10,5,-4],[12,5,-5],
      [14,6,-5],[16,8,-7],[20,9,-8],[23,11,-9],[27,12,-11],[32,15,-13]
    ],
    0x3E: [  // GREATER-THAN SIGN
      [4,4,0],[4,5,-1],[5,5,-1],[6,6,-1],[7,7,-1],[8,9,-1],[10,11,-1],[11,12,-1],
      [13,14,-2],[16,18,-2],[19,20,-2],[22,23,-3],[26,28,-3],[31,34,-4]
    ],
    0x3F: [  // QUESTION MARK
      [4,4,0],[4,6,0],[5,6,0],[6,7,0],[7,8,0],[8,10,0],[10,13,0],[11,14,0],
      [13,17,0],[16,21,0],[19,24,0],[22,28,0],[26,34,0],[31,41,0]
    ],
    0x40: [  // COMMERCIAL AT
      [4,4,0],[5,6,0],[5,6,0],[6,7,0],[7,8,0],[9,10,0],[10,13,0],[12,14,0],
      [14,17,0],[16,21,0],[19,24,0],[23,28,0],[27,34,0],[32,41,0]
    ],
    0x41: [  // LATIN CAPITAL LETTER A
      [4,4,0],[5,6,0],[5,6,0],[6,7,0],[7,8,0],[9,10,0],[10,13,0],[12,14,0],
      [14,17,0],[17,21,0],[20,24,0],[24,28,0],[28,34,0],[33,41,0]
    ],
    0x42: [  // LATIN CAPITAL LETTER B
      [4,5,0],[4,7,0],[5,7,0],[6,8,0],[7,9,0],[8,11,0],[10,14,0],[12,15,0],
      [14,18,0],[16,22,0],[19,25,0],[23,29,0],[27,35,0],[32,42,0]
    ],
    0x43: [  // LATIN CAPITAL LETTER C
      [4,4,0],[5,6,0],[5,6,0],[6,7,0],[7,8,0],[9,10,0],[10,13,0],[12,14,0],
      [14,17,0],[17,21,0],[20,24,0],[23,28,0],[27,34,0],[33,41,0]
    ],
    0x44: [  // LATIN CAPITAL LETTER D
      [4,4,0],[5,6,0],[5,6,0],[6,7,0],[7,8,0],[9,10,0],[10,13,0],[12,14,0],
      [14,17,0],[16,21,0],[19,24,0],[23,28,0],[27,34,0],[32,41,0]
    ],
    0x45: [  // LATIN CAPITAL LETTER E
      [4,4,0],[5,6,0],[5,6,0],[6,7,0],[7,8,0],[9,10,0],[10,13,0],[12,14,0],
      [14,17,0],[17,21,0],[20,24,0],[24,28,0],[28,33,-1],[33,40,0]
    ],
    0x46: [  // LATIN CAPITAL LETTER F
      [4,4,0],[5,6,0],[5,6,0],[6,7,0],[7,8,0],[9,10,0],[10,13,0],[12,14,0],
      [14,17,0],[16,21,0],[20,24,0],[23,28,0],[27,33,-1],[32,40,-1]
    ],
    0x47: [  // LATIN CAPITAL LETTER G
      [4,4,0],[5,6,0],[5,6,0],[6,7,0],[7,8,0],[9,10,0],[10,13,0],[12,14,0],
      [14,17,0],[17,21,0],[20,24,0],[23,28,0],[28,34,0],[33,41,0]
    ],
    0x48: [  // LATIN CAPITAL LETTER H
      [4,4,0],[5,6,0],[6,6,0],[7,7,0],[8,8,0],[9,10,0],[11,13,0],[12,14,0],
      [15,17,0],[17,21,0],[21,24,0],[24,28,0],[29,34,0],[34,41,0]
    ],
    0x49: [  // LATIN CAPITAL LETTER I
      [4,4,0],[4,6,0],[5,6,0],[6,7,0],[7,8,0],[8,10,0],[9,13,0],[11,14,0],
      [13,17,0],[15,21,0],[18,24,0],[22,28,0],[26,34,0],[30,41,0]
    ],
    0x4A: [  // LATIN CAPITAL LETTER J
      [4,4,0],[4,6,0],[5,6,0],[6,7,0],[7,8,0],[8,10,0],[10,13,0],[12,14,0],
      [14,17,0],[16,21,0],[19,24,0],[23,28,0],[27,34,0],[32,41,0]
    ],
    0x4B: [  // LATIN CAPITAL LETTER K
      [4,4,0],[5,6,0],[5,6,0],[6,7,0],[7,8,0],[9,10,0],[10,13,0],[12,14,0],
      [14,17,0],[17,21,0],[20,24,0],[24,28,0],[28,34,0],[33,41,0]
    ],
    0x4C: [  // LATIN CAPITAL LETTER L
      [4,4,0],[4,6,0],[5,6,0],[6,7,0],[7,8,0],[8,10,0],[10,13,0],[12,14,0],
      [14,17,0],[16,21,0],[19,24,0],[23,28,0],[27,34,0],[32,41,0]
    ],
    0x4D: [  // LATIN CAPITAL LETTER M
      [4,4,0],[5,6,0],[5,6,0],[6,7,0],[7,8,0],[9,10,0],[10,13,0],[12,14,0],
      [14,17,0],[17,21,0],[20,24,0],[24,28,0],[28,34,0],[34,41,0]
    ],
    0x4E: [  // LATIN CAPITAL LETTER N
      [4,4,0],[5,6,0],[5,6,0],[6,7,0],[7,8,0],[9,10,0],[10,13,0],[12,14,0],
      [14,17,0],[17,21,0],[20,24,0],[24,28,0],[28,34,0],[34,41,0]
    ],
    0x4F: [  // LATIN CAPITAL LETTER O
      [4,4,0],[4,6,0],[5,6,0],[6,7,0],[7,8,0],[8,10,0],[9,13,0],[11,14,0],
      [13,17,0],[16,21,0],[19,24,0],[22,28,0],[26,34,0],[31,41,0]
    ],
    0x50: [  // LATIN CAPITAL LETTER P
      [4,4,0],[4,6,0],[5,6,0],[6,7,0],[7,8,0],[8,10,0],[10,13,0],[12,14,0],
      [14,17,0],[16,21,0],[19,24,0],[23,28,0],[27,34,0],[32,41,0]
    ],
    0x51: [  // LATIN CAPITAL LETTER Q
      [4,6,2],[4,8,2],[5,8,2],[6,9,2],[7,10,2],[8,13,3],[10,16,3],[11,18,4],
      [13,21,4],[16,26,5],[19,30,6],[22,35,7],[26,42,8],[31,50,9]
    ],
    0x52: [  // LATIN CAPITAL LETTER R
      [4,4,0],[5,6,0],[6,6,0],[7,7,0],[8,8,0],[9,10,0],[11,13,0],[13,14,0],
      [15,17,0],[18,21,0],[21,24,0],[25,28,0],[30,34,0],[35,41,0]
    ],
    0x53: [  // LATIN CAPITAL LETTER S
      [4,4,0],[4,6,0],[5,6,0],[6,7,0],[7,8,0],[8,10,0],[10,13,0],[11,14,0],
      [14,17,0],[16,21,0],[19,24,0],[23,28,0],[27,34,0],[32,41,0]
    ],
    0x54: [  // LATIN CAPITAL LETTER T
      [4,4,0],[5,6,0],[5,6,0],[6,7,0],[7,8,0],[9,10,0],[10,13,0],[12,14,0],
      [14,17,0],[17,21,0],[20,24,0],[24,28,0],[28,34,0],[33,41,0]
    ],
    0x55: [  // LATIN CAPITAL LETTER U
      [5,5,0],[6,7,0],[7,7,0],[8,8,0],[9,9,0],[10,11,0],[12,14,0],[14,15,0],
      [16,18,0],[19,22,0],[22,25,0],[26,29,0],[31,35,0],[36,42,0]
    ],
    0x56: [  // LATIN CAPITAL LETTER V
      [4,5,0],[5,7,0],[5,7,0],[6,8,0],[7,9,0],[9,11,0],[10,14,0],[12,15,0],
      [14,18,0],[17,22,0],[20,25,0],[24,29,0],[28,35,0],[33,42,0]
    ],
    0x57: [  // LATIN CAPITAL LETTER W
      [4,5,1],[5,7,1],[5,7,1],[6,8,1],[8,9,1],[9,11,1],[10,14,1],[12,15,1],
      [15,18,1],[17,22,1],[20,25,1],[24,29,1],[29,35,1],[34,42,1]
    ],
    0x58: [  // LATIN CAPITAL LETTER X
      [4,4,0],[5,6,0],[5,6,0],[6,7,0],[7,8,0],[9,10,0],[10,13,0],[12,14,0],
      [14,17,0],[17,21,0],[20,24,0],[23,28,0],[28,33,-1],[33,41,0]
    ],
    0x59: [  // LATIN CAPITAL LETTER Y
      [4,4,0],[5,6,0],[5,6,0],[6,7,0],[8,8,0],[9,10,0],[10,13,0],[12,14,0],
      [15,17,0],[17,21,0],[20,24,0],[24,28,0],[29,34,0],[34,41,0]
    ],
    0x5A: [  // LATIN CAPITAL LETTER Z
      [4,4,0],[4,6,0],[5,6,0],[6,7,0],[7,8,0],[8,10,0],[10,13,0],[12,14,0],
      [14,17,0],[16,21,0],[19,24,0],[23,28,0],[27,34,0],[32,41,0]
    ],
    0x5B: [  // LEFT SQUARE BRACKET
      [4,6,1],[4,7,1],[5,7,1],[6,9,1],[7,12,2],[9,14,2],[10,16,2],[12,18,2],
      [14,22,2],[16,27,3],[19,30,3],[23,36,4],[27,43,4],[32,52,5]
    ],
    0x5C: [  // REVERSE SOLIDUS
      [4,6,1],[4,7,1],[5,7,1],[6,9,1],[7,11,1],[8,13,1],[10,16,2],[11,18,2],
      [13,22,2],[16,27,3],[19,30,3],[22,36,4],[26,43,5],[31,52,6]
    ],
    0x5D: [  // RIGHT SQUARE BRACKET
      [3,6,1],[3,7,1],[4,7,1],[4,9,1],[5,12,2],[6,14,2],[7,16,2],[8,18,2],
      [9,22,2],[11,27,3],[13,30,3],[15,36,4],[18,43,4],[21,52,5]
    ],
    0x5E: [  // CIRCUMFLEX ACCENT
      [3,1,-3],[4,2,-4],[5,2,-4],[5,2,-5],[6,2,-6],[8,3,-7],[9,3,-10],[10,4,-10],
      [12,4,-13],[14,5,-16],[17,6,-18],[20,7,-21],[24,9,-25],[28,10,-31]
    ],
    0x5F: [  // LOW LINE
      [4,2,1],[4,2,1],[5,2,1],[6,2,1],[7,2,1],[8,3,2],[10,4,3],[11,3,2],
      [13,4,3],[16,4,3],[19,5,4],[22,5,4],[26,6,5],[31,7,6]
    ],
    0x60: [  // GRAVE ACCENT
      [3,3,-2],[3,4,-2],[4,4,-2],[5,5,-3],[5,5,-5],[6,5,-6],[7,7,-7],[9,8,-8],
      [10,9,-10],[12,11,-12],[14,13,-14],[17,15,-16],[20,18,-20],[24,22,-24]
    ],
    0x61: [  // LATIN SMALL LETTER A
      [4,3,0],[5,4,0],[6,4,0],[7,5,0],[8,6,0],[9,7,0],[11,9,0],[13,10,0],
      [15,12,0],[18,15,0],[21,17,0],[25,20,0],[30,24,0],[35,29,0]
    ],
    0x62: [  // LATIN SMALL LETTER B
      [4,4,0],[5,6,0],[5,6,0],[6,7,0],[7,8,0],[9,10,0],[10,13,0],[12,14,0],
      [14,17,0],[17,21,0],[20,24,0],[23,28,0],[28,34,0],[33,41,0]
    ],
    0x63: [  // LATIN SMALL LETTER C
      [4,3,0],[4,4,0],[5,4,0],[6,5,0],[7,6,0],[8,7,0],[10,9,0],[11,10,0],
      [13,12,0],[16,15,0],[19,17,0],[22,20,0],[26,24,0],[31,29,0]
    ],
    0x64: [  // LATIN SMALL LETTER D
      [4,4,0],[5,6,0],[6,6,0],[7,7,0],[8,8,0],[9,10,0],[11,13,0],[13,14,0],
      [15,17,0],[18,21,0],[21,24,0],[25,28,0],[29,34,0],[35,41,0]
    ],
    0x65: [  // LATIN SMALL LETTER E
      [4,3,0],[4,4,0],[5,4,0],[6,5,0],[7,6,0],[8,7,0],[10,9,0],[11,10,0],
      [13,12,0],[16,15,0],[19,17,0],[22,20,0],[26,24,0],[31,29,0]
    ],
    0x66: [  // LATIN SMALL LETTER F
      [3,4,0],[4,6,0],[5,6,0],[6,7,0],[6,8,0],[8,10,0],[9,13,0],[11,14,0],
      [12,17,0],[15,21,0],[17,24,0],[21,28,0],[24,34,0],[29,41,0]
    ],
    0x67: [  // LATIN SMALL LETTER G
      [4,5,2],[5,6,2],[5,6,2],[6,8,3],[7,9,3],[9,11,4],[10,14,5],[12,15,5],
      [14,18,6],[17,23,8],[20,26,9],[24,30,10],[28,36,12],[34,44,15]
    ],
    0x68: [  // LATIN SMALL LETTER H
      [4,4,0],[5,6,0],[6,6,0],[7,7,0],[8,8,0],[9,10,0],[11,13,0],[13,14,0],
      [15,17,0],[18,21,0],[21,24,0],[25,28,0],[29,34,0],[35,41,0]
    ],
    0x69: [  // LATIN SMALL LETTER I
      [4,4,0],[4,6,0],[5,6,0],[6,7,0],[7,8,0],[8,10,0],[9,13,0],[11,14,0],
      [13,17,0],[16,21,0],[19,24,0],[22,28,0],[26,34,0],[31,40,-1]
    ],
    0x6A: [  // LATIN SMALL LETTER J
      [3,6,2],[4,8,2],[4,8,2],[5,10,3],[6,11,3],[7,14,4],[8,18,5],[9,19,5],
      [11,23,6],[13,29,8],[15,33,9],[18,38,10],[22,46,12],[25,56,15]
    ],
    0x6B: [  // LATIN SMALL LETTER K
      [4,4,0],[5,6,0],[5,6,0],[6,7,0],[8,8,0],[9,10,0],[10,13,0],[12,14,0],
      [15,17,0],[17,21,0],[20,24,0],[24,28,0],[29,34,0],[34,41,0]
    ],
    0x6C: [  // LATIN SMALL LETTER L
      [4,4,0],[4,6,0],[5,6,0],[6,7,0],[7,8,0],[8,10,0],[10,13,0],[11,14,0],
      [14,17,0],[16,21,0],[19,24,0],[22,28,0],[27,33,-1],[32,41,0]
    ],
    0x6D: [  // LATIN SMALL LETTER M
      [5,3,0],[6,4,0],[7,4,0],[8,5,0],[9,6,0],[10,7,0],[12,9,0],[14,10,0],
      [16,12,0],[19,15,0],[22,17,0],[26,20,0],[31,24,0],[37,29,0]
    ],
    0x6E: [  // LATIN SMALL LETTER N
      [4,3,0],[5,4,0],[6,4,0],[7,5,0],[8,6,0],[9,7,0],[11,9,0],[13,10,0],
      [15,12,0],[18,15,0],[21,17,0],[25,20,0],[29,24,0],[35,29,0]
    ],
    0x6F: [  // LATIN SMALL LETTER O
      [4,3,0],[4,4,0],[5,4,0],[6,5,0],[7,6,0],[8,7,0],[10,9,0],[11,10,0],
      [14,12,0],[16,15,0],[19,17,0],[22,20,0],[27,24,0],[32,29,0]
    ],
    0x70: [  // LATIN SMALL LETTER P
      [4,5,2],[5,6,2],[5,6,2],[6,8,3],[7,9,3],[9,11,4],[10,14,5],[12,15,5],
      [14,18,6],[17,23,8],[20,26,9],[23,30,10],[28,36,12],[33,44,15]
    ],
    0x71: [  // LATIN SMALL LETTER Q
      [4,5,2],[5,6,2],[6,6,2],[7,8,3],[8,9,3],[10,11,4],[11,14,5],[13,15,5],
      [16,18,6],[18,23,8],[22,26,9],[26,30,10],[31,36,12],[36,44,15]
    ],
    0x72: [  // LATIN SMALL LETTER R
      [4,3,0],[5,4,0],[5,4,0],[6,5,0],[7,6,0],[9,7,0],[10,9,0],[12,10,0],
      [14,12,0],[17,15,0],[20,17,0],[23,20,0],[28,24,0],[33,29,0]
    ],
    0x73: [  // LATIN SMALL LETTER S
      [4,3,0],[4,4,0],[5,4,0],[6,5,0],[7,6,0],[8,7,0],[9,9,0],[11,10,0],
      [13,12,0],[16,15,0],[18,17,0],[22,20,0],[26,24,0],[31,29,0]
    ],
    0x74: [  // LATIN SMALL LETTER T
      [4,4,0],[4,5,0],[5,5,0],[6,7,0],[7,8,0],[8,9,0],[9,12,0],[11,13,0],
      [13,16,0],[15,19,0],[18,22,0],[21,26,0],[25,31,0],[30,37,0]
    ],
    0x75: [  // LATIN SMALL LETTER U
      [4,3,0],[5,4,0],[6,4,0],[7,5,0],[8,6,0],[9,7,0],[11,9,0],[13,10,0],
      [15,12,0],[18,15,0],[21,17,0],[25,20,0],[29,24,0],[35,29,0]
    ],
    0x76: [  // LATIN SMALL LETTER V
      [4,3,0],[5,4,0],[5,4,0],[6,5,0],[7,6,0],[9,7,0],[10,9,0],[12,10,0],
      [14,12,0],[17,15,0],[20,17,0],[23,20,0],[28,24,0],[33,29,0]
    ],
    0x77: [  // LATIN SMALL LETTER W
      [4,3,0],[5,4,0],[5,4,0],[6,5,0],[7,6,0],[9,7,0],[10,9,0],[12,10,0],
      [14,12,0],[17,15,0],[20,17,0],[24,20,0],[28,24,0],[34,29,0]
    ],
    0x78: [  // LATIN SMALL LETTER X
      [4,3,0],[5,4,0],[5,4,0],[6,5,0],[7,6,0],[9,7,0],[10,9,0],[12,10,0],
      [14,12,0],[17,15,0],[20,17,0],[23,20,0],[28,24,0],[33,29,0]
    ],
    0x79: [  // LATIN SMALL LETTER Y
      [4,5,2],[5,6,2],[5,6,2],[6,8,3],[7,9,3],[9,11,4],[10,14,5],[12,15,5],
      [14,18,6],[17,23,8],[20,26,9],[24,30,10],[28,36,12],[33,44,15]
    ],
    0x7A: [  // LATIN SMALL LETTER Z
      [4,3,0],[4,5,1],[5,5,1],[6,6,1],[7,7,1],[8,8,1],[10,10,1],[11,11,1],
      [14,13,1],[16,16,1],[19,18,1],[22,21,1],[27,25,1],[32,29,1]
    ],
    0x7B: [  // LEFT CURLY BRACKET
      [4,6,1],[4,7,1],[5,7,1],[6,9,1],[7,11,1],[8,14,2],[10,17,2],[11,18,2],
      [14,22,2],[16,27,3],[19,30,3],[22,36,4],[27,43,4],[32,52,5]
    ],
    0x7C: [  // VERTICAL LINE
      [3,6,1],[3,8,1],[3,8,1],[4,9,1],[5,11,1],[5,13,1],[6,16,2],[7,18,2],
      [9,22,2],[10,27,3],[12,31,3],[14,36,4],[17,43,4],[20,52,5]
    ],
    0x7D: [  // RIGHT CURLY BRACKET
      [4,6,1],[4,7,1],[5,7,1],[6,9,1],[7,11,1],[8,13,2],[10,17,2],[12,18,2],
      [14,22,2],[16,27,3],[19,30,3],[23,36,4],[27,43,4],[32,51,5]
    ],
    0x7E: [  // TILDE
      [4,1,-3],[4,1,-5],[5,1,-5],[6,2,-5],[7,3,-5],[8,3,-7],[9,3,-10],[11,3,-11],
      [13,4,-13],[15,5,-16],[18,6,-18],[21,7,-21],[25,8,-26],[29,10,-31]
    ],
    0x7F: [  // ??
      [3,1,-3],[4,1,-5],[5,1,-5],[5,2,-5],[6,2,-6],[7,2,-8],[9,2,-11],[10,3,-11],
      [12,3,-14],[14,4,-17],[17,4,-20],[20,5,-23],[24,6,-28],[28,7,-34]
    ]
  }
});

MathJax.Ajax.loadComplete(MathJax.OutputJax["HTML-CSS"].imgDir+"/Typewriter/Regular"+
                          MathJax.OutputJax["HTML-CSS"].imgPacked+"/BasicLatin.js");
