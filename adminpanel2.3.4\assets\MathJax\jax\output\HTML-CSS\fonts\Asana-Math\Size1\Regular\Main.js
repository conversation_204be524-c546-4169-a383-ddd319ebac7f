/*
 *  /MathJax/jax/output/HTML-CSS/fonts/Asana-Math/Size1/Regular/Main.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.OutputJax["HTML-CSS"].FONTDATA.FONTS.AsanaMathJax_Size1={directory:"Size1/Regular",family:"AsanaMathJax_Size1",testString:"\u0302\u0303\u0305\u0306\u030C\u0332\u0333\u033F\u2016\u2044\u2045\u2046\u20D6\u20D7\u220F",32:[0,0,249,0,0],40:[981,490,399,84,360],41:[981,490,399,40,316],91:[984,492,350,84,321],93:[984,492,350,84,321],123:[981,490,362,84,328],124:[908,367,241,86,156],125:[981,490,362,84,328],770:[783,-627,453,0,453],771:[763,-654,700,0,701],773:[587,-542,510,0,511],774:[664,-506,383,0,384],780:[783,-627,736,0,737],818:[-130,175,510,0,511],819:[-130,283,510,0,511],831:[695,-542,510,0,511],8214:[908,367,436,86,351],8260:[742,463,382,-69,383],8261:[943,401,353,64,303],8262:[943,401,358,30,269],8406:[790,-519,807,0,807],8407:[790,-519,807,0,807],8719:[901,448,1431,78,1355],8720:[901,448,1431,78,1355],8721:[893,446,1224,89,1135],8730:[1280,0,770,63,803],8745:[1039,520,1292,124,1169],8747:[1310,654,1000,54,1001],8748:[1310,654,1659,54,1540],8749:[1310,654,2198,54,2079],8750:[1310,654,1120,54,1001],8751:[1310,654,1659,54,1540],8752:[1310,654,2198,54,2079],8753:[1310,654,1120,54,1001],8754:[1310,654,1146,80,1027],8755:[1310,654,1120,54,1001],8896:[1040,519,1217,85,1132],8897:[1040,519,1217,85,1132],8898:[1039,520,1292,124,1169],8899:[1039,520,1292,124,1169],8968:[980,490,390,84,346],8969:[980,490,390,84,346],8970:[980,490,390,84,346],8971:[980,490,390,84,346],9140:[755,-518,977,0,978],9141:[-238,475,977,0,978],9180:[821,-545,972,0,973],9181:[-545,821,972,0,973],9182:[789,-545,1572,51,1522],9183:[-545,789,1572,51,1522],9184:[755,-545,1359,0,1360],9185:[-545,755,1359,0,1360],10181:[781,240,450,53,397],10182:[781,240,450,53,397],10214:[684,341,502,84,473],10215:[684,341,502,84,473],10216:[681,340,422,53,371],10217:[681,340,422,53,371],10218:[681,340,605,53,554],10219:[681,340,605,53,554],10748:[915,457,518,50,469],10749:[915,457,518,49,469],10752:[1100,550,1901,124,1778],10753:[1100,550,1901,124,1778],10754:[1100,550,1901,124,1778],10755:[1039,520,1292,124,1169],10756:[1039,520,1292,124,1169],10757:[1024,513,1292,124,1169],10758:[1024,513,1292,124,1169],10759:[1039,520,1415,86,1330],10760:[1039,520,1415,86,1330],10761:[888,445,1581,124,1459],10764:[1310,654,2736,54,2617],10765:[1310,654,1120,54,1001],10766:[1310,654,1120,54,1001],10767:[1310,654,1120,54,1001],10768:[1310,654,1120,54,1001],10769:[1310,654,1182,54,1063],10770:[1310,654,1120,54,1001],10771:[1310,654,1120,54,1001],10772:[1310,654,1120,54,1001],10773:[1310,654,1120,54,1001],10774:[1310,654,1120,54,1001],10775:[1310,654,1431,54,1362],10776:[1310,654,1120,54,1001],10777:[1310,654,1120,54,1001],10778:[1310,654,1120,54,1001],10779:[1471,654,1130,54,1011],10780:[1471,654,1156,80,1037]};MathJax.Callback.Queue(["initFont",MathJax.OutputJax["HTML-CSS"],"AsanaMathJax_Size1"],["loadComplete",MathJax.Ajax,MathJax.OutputJax["HTML-CSS"].fontDir+"/Size1/Regular/Main.js"]);
