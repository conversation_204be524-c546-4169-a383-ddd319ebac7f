/*
 *  /MathJax/fonts/HTML-CSS/TeX/png/SansSerif/Italic/Other.js
 *  
 *  Copyright (c) 2010-2013 The MathJax Consortium
 *
 *  Part of the MathJax library.
 *  See http://www.mathjax.org for details.
 * 
 *  Licensed under the Apache License, Version 2.0;
 *  you may not use this file except in compliance with the License.
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 */

MathJax.OutputJax["HTML-CSS"].defineImageData({"MathJax_SansSerif-italic":{160:[[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0]],305:[[2,3,0],[3,4,0],[3,5,0],[4,6,0],[4,6,0],[5,8,0],[6,9,0],[6,11,0],[8,13,0],[9,15,0],[11,18,0],[12,21,0],[15,25,0],[17,29,0]],567:[[3,4,1],[4,6,2],[4,7,2],[6,9,3],[6,9,3],[7,11,3],[8,13,4],[10,16,5],[11,19,6],[14,21,6],[16,26,8],[19,30,9],[22,37,12],[26,43,14]],768:[[2,1,-4],[3,2,-4],[3,2,-6],[3,2,-7],[3,2,-7],[4,3,-9],[5,4,-10],[5,4,-13],[6,5,-15],[7,6,-17],[8,7,-21],[9,8,-24],[11,10,-30],[13,11,-34]],769:[[3,1,-4],[3,2,-4],[3,2,-6],[4,2,-7],[4,2,-7],[6,3,-9],[6,4,-10],[7,4,-13],[8,5,-15],[10,6,-17],[11,7,-21],[12,8,-24],[15,10,-30],[18,11,-34]],770:[[4,1,-4],[4,2,-4],[5,2,-6],[5,2,-7],[6,2,-7],[7,3,-9],[8,4,-10],[9,4,-13],[10,5,-15],[13,6,-17],[15,7,-21],[17,8,-24],[20,10,-30],[24,11,-34]],771:[[4,1,-3],[4,1,-5],[4,2,-5],[5,3,-6],[6,2,-7],[6,3,-9],[8,3,-10],[9,3,-13],[11,4,-15],[12,5,-17],[15,6,-21],[17,6,-25],[21,8,-31],[24,9,-35]],772:[[4,1,-3],[4,1,-5],[5,1,-6],[5,2,-6],[6,2,-6],[8,3,-9],[9,2,-11],[10,2,-13],[11,3,-16],[14,3,-18],[16,4,-22],[18,4,-25],[22,5,-31],[26,6,-36]],774:[[3,1,-4],[4,2,-4],[4,2,-6],[5,3,-6],[6,3,-6],[7,4,-8],[8,4,-10],[9,4,-13],[11,6,-14],[13,6,-17],[15,8,-20],[18,9,-23],[21,11,-29],[24,12,-33]],775:[[2,1,-3],[2,1,-5],[2,2,-5],[3,2,-7],[3,2,-7],[3,2,-10],[3,3,-10],[4,3,-13],[4,3,-16],[5,4,-18],[6,5,-22],[7,5,-26],[8,6,-33],[9,7,-38]],776:[[3,1,-3],[4,1,-5],[4,2,-5],[5,2,-7],[5,2,-7],[6,2,-10],[7,2,-11],[8,3,-13],[10,3,-16],[11,4,-18],[13,4,-23],[15,5,-26],[19,6,-33],[21,7,-38]],778:[[2,1,-3],[2,2,-4],[3,2,-5],[3,2,-7],[4,2,-7],[4,3,-9],[5,3,-10],[6,5,-11],[7,5,-14],[8,6,-16],[9,7,-21],[11,8,-24],[13,10,-30],[15,11,-34]],779:[[3,2,-2],[4,2,-4],[4,2,-5],[5,3,-6],[5,3,-6],[7,3,-9],[8,4,-9],[9,5,-11],[10,5,-14],[13,6,-16],[15,8,-20],[17,9,-23],[20,10,-30],[24,11,-34]],780:[[3,1,-3],[4,2,-4],[4,2,-5],[5,2,-6],[5,2,-6],[6,3,-9],[8,4,-10],[9,4,-12],[10,5,-14],[12,6,-16],[15,7,-19],[17,8,-22],[20,10,-28],[23,11,-32]],915:[[5,4,0],[6,6,0],[7,7,0],[8,9,0],[9,9,0],[11,12,0],[13,13,0],[15,16,0],[18,19,0],[22,22,0],[26,28,0],[31,32,0],[36,39,0],[43,46,0]],916:[[6,5,0],[7,6,0],[8,8,0],[10,9,0],[11,9,0],[14,12,0],[16,14,0],[19,17,0],[22,20,0],[26,23,0],[31,28,0],[37,32,0],[44,40,0],[52,46,0]],920:[[6,5,0],[7,6,0],[8,8,0],[10,9,0],[12,9,0],[14,12,0],[16,14,0],[19,17,0],[23,20,0],[27,23,0],[32,28,0],[38,34,2],[45,42,1],[53,48,1]],923:[[5,5,0],[5,6,0],[6,8,0],[7,9,0],[9,9,0],[10,12,0],[12,14,0],[14,17,0],[17,20,0],[20,23,0],[23,28,0],[28,32,0],[33,40,0],[39,46,0]],926:[[6,4,0],[7,6,0],[8,7,0],[9,9,0],[11,9,0],[13,12,0],[15,13,0],[18,16,0],[22,19,0],[26,22,0],[30,28,0],[36,32,0],[43,39,0],[51,45,0]],928:[[6,4,0],[7,6,0],[8,7,0],[10,9,0],[11,9,0],[13,12,0],[15,13,0],[18,16,0],[22,19,0],[26,22,0],[31,28,0],[36,32,0],[43,39,0],[51,45,0]],931:[[6,4,0],[7,6,0],[8,7,0],[10,9,0],[12,9,0],[14,12,0],[16,13,0],[19,16,0],[23,19,0],[27,22,0],[32,28,0],[38,32,0],[45,39,0],[54,46,0]],933:[[6,5,0],[7,6,0],[9,8,0],[10,9,0],[12,9,0],[14,12,0],[17,14,0],[20,17,0],[24,20,0],[28,23,0],[33,28,0],[40,32,0],[47,41,0],[56,48,0]],934:[[6,4,0],[7,6,0],[8,7,0],[9,9,0],[11,9,0],[13,12,0],[15,13,0],[18,16,0],[21,19,0],[25,22,0],[30,28,0],[35,32,0],[42,40,0],[49,46,0]],936:[[6,5,0],[8,6,0],[9,8,0],[11,9,0],[12,9,0],[15,12,0],[17,14,0],[20,17,0],[24,20,0],[29,23,0],[34,28,0],[40,32,0],[48,39,0],[57,46,0]],937:[[6,5,0],[7,6,0],[8,8,0],[10,9,0],[11,9,0],[13,12,0],[15,14,0],[18,17,0],[22,20,0],[26,23,0],[31,28,0],[36,33,0],[43,41,0],[51,47,0]],8211:[[4,1,-1],[5,1,-2],[6,1,-2],[7,1,-3],[8,1,-3],[10,3,-3],[12,2,-5],[14,2,-6],[16,3,-7],[19,3,-8],[23,3,-9],[27,4,-11],[32,5,-13],[38,5,-15]],8212:[[8,1,-1],[9,1,-2],[11,1,-2],[13,1,-3],[15,1,-3],[18,3,-3],[21,2,-5],[25,2,-6],[30,3,-7],[36,3,-8],[42,3,-9],[50,4,-11],[59,5,-13],[71,5,-15]],8216:[[3,2,-2],[3,2,-4],[4,3,-4],[4,4,-5],[5,4,-5],[6,4,-8],[7,4,-9],[8,6,-10],[10,6,-13],[12,8,-14],[14,9,-19],[16,11,-21],[19,13,-27],[23,15,-31]],8217:[[3,2,-2],[3,2,-4],[4,3,-4],[4,3,-6],[5,3,-6],[6,4,-8],[7,5,-8],[8,6,-10],[10,7,-12],[12,8,-14],[14,10,-18],[16,11,-21],[19,13,-27],[23,15,-31]],8220:[[5,2,-2],[6,2,-4],[7,3,-4],[8,4,-5],[9,4,-5],[11,4,-8],[12,4,-9],[15,6,-10],[17,6,-13],[21,8,-14],[25,9,-19],[29,11,-21],[34,13,-27],[41,15,-31]],8221:[[4,2,-2],[4,2,-4],[5,3,-4],[6,3,-6],[7,3,-6],[8,4,-8],[10,5,-8],[11,6,-10],[14,7,-12],[16,8,-14],[19,10,-18],[22,11,-21],[27,13,-27],[32,15,-31]]}});MathJax.Ajax.loadComplete(MathJax.OutputJax["HTML-CSS"].imgDir+"/SansSerif/Italic"+MathJax.OutputJax["HTML-CSS"].imgPacked+"/Other.js");

