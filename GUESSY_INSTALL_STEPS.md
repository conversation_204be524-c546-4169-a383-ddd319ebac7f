# 🚀 Guessy Admin Panel Installation Steps

## ✅ Current Status
- ✅ Admin panel copied to: `C:\xampp\htdocs\guessy_admin`
- ✅ Installation page opened: http://localhost/guessy_admin/install
- 🔄 Ready for database installation

## 📋 Step 1: Complete Installation Form

The installation page should now be open in your browser. Fill in the following details:

### **Database Configuration:**
```
Database Host: localhost
Database Username: root
Database Password: (leave empty if no password)
Database Name: guessy_db
```

### **Admin Account Setup:**
```
Admin Username: admin
Admin Password: admin123
Admin Email: <EMAIL>
```

## 📋 Step 2: Click Install

1. **Fill the form** with the above details
2. **Click "Install"** button
3. **Wait for installation** to complete
4. **Installation will**:
   - Create database tables
   - Import sample data
   - Configure admin panel
   - Set up initial settings

## 📋 Step 3: Access Admin Panel

After successful installation:

1. **URL**: http://localhost/guessy_admin
2. **Login**: admin / admin123
3. **Dashboard**: Should show Guessy admin panel

## 📋 Step 4: Verify Installation

### **Check Dashboard:**
- ✅ Admin panel loads without errors
- ✅ Dashboard shows statistics
- ✅ Navigation menu works

### **Check Categories:**
- ✅ Go to Categories section
- ✅ Should show 10 Guessy categories
- ✅ Categories should be active

### **Check Questions:**
- ✅ Go to Questions section
- ✅ Should show sample questions
- ✅ Questions should be properly formatted

### **Check Settings:**
- ✅ Go to Settings section
- ✅ App name should be "Guessy"
- ✅ All settings should be configured

## 📋 Step 5: Test API

### **Test Categories API:**
Open in browser: http://localhost/guessy_admin/Api/get_categories

**Expected Response:**
```json
{
  "error": false,
  "message": "Categories retrieved successfully",
  "data": [
    {
      "id": "1",
      "category_name": "General Knowledge",
      "image": "general_knowledge.png",
      "maxlevel": "10",
      "no_of_que": "15"
    },
    ...
  ]
}
```

## 🆘 Troubleshooting

### **Issue 1: Database Connection Error**
**Error**: "Could not connect to database"

**Solutions:**
1. Check if MySQL is running in XAMPP
2. Verify database credentials
3. Make sure `guessy_db` database exists
4. Check if port 3306 is available

### **Issue 2: Permission Error**
**Error**: "Permission denied"

**Solutions:**
1. Run XAMPP as Administrator
2. Check folder permissions for `C:\xampp\htdocs\guessy_admin`
3. Ensure Apache has write permissions

### **Issue 3: Installation Fails**
**Error**: Installation process stops

**Solutions:**
1. Check PHP error logs in XAMPP
2. Verify all required PHP extensions are enabled
3. Check if database user has CREATE privileges
4. Clear browser cache and try again

### **Issue 4: Admin Panel Not Loading**
**Error**: "This site can't be reached"

**Solutions:**
1. Check if Apache is running
2. Verify URL: http://localhost/guessy_admin
3. Check if .htaccess file exists
4. Verify folder structure is correct

## 🔧 Manual Database Setup (If Needed)

If automatic installation fails, you can manually setup the database:

### **Step 1: Create Database**
```sql
CREATE DATABASE guessy_db;
USE guessy_db;
```

### **Step 2: Import Schema**
1. Go to phpMyAdmin: http://localhost/phpmyadmin
2. Select `guessy_db` database
3. Import file: `C:\xampp\htdocs\guessy_admin\install\assets\quiz.php`

### **Step 3: Import Guessy Data**
1. Import file: `GUESSY_MANUAL_SETUP.sql` (from project root)
2. This will add Guessy-specific categories and questions

### **Step 4: Update Config**
Edit: `C:\xampp\htdocs\guessy_admin\application\config\database.php`
```php
$db['default']['database'] = 'guessy_db';
```

## 🎯 Expected Final Result

After successful installation:

### **Admin Panel Features:**
- ✅ **Dashboard** - Statistics and overview
- ✅ **Categories** - 10 Guessy categories
- ✅ **Questions** - Sample questions for testing
- ✅ **Users** - User management system
- ✅ **Settings** - App configuration
- ✅ **Contests** - Contest management
- ✅ **Reports** - Analytics and reports

### **API Endpoints Working:**
- ✅ `get_categories` - Returns Guessy categories
- ✅ `get_questions` - Returns questions by category
- ✅ `user_signup` - User registration
- ✅ `get_system_configurations` - App settings

### **Database Tables:**
- ✅ 30+ tables created
- ✅ Sample data imported
- ✅ Admin user created
- ✅ Guessy categories and questions

## 🚀 Next Steps After Installation

1. **Test Flutter App Connection**
   - The Flutter app should now connect to admin panel
   - Categories should load in the app

2. **Add More Content**
   - Use admin panel to add more questions
   - Upload category images
   - Configure app settings

3. **Customize Branding**
   - Update app logo and colors
   - Modify welcome messages
   - Set coin rewards

4. **Test Complete System**
   - User registration in Flutter app
   - Quiz gameplay
   - Coin system
   - Leaderboards

## 🎉 Success Indicators

### **Installation Successful When:**
- ✅ Admin panel loads at http://localhost/guessy_admin
- ✅ Login works with admin/admin123
- ✅ Dashboard shows Guessy branding
- ✅ Categories section shows 10 categories
- ✅ API endpoints return JSON data
- ✅ No database connection errors

### **Ready for Flutter App When:**
- ✅ API returns categories successfully
- ✅ Questions are available for each category
- ✅ System configurations are set
- ✅ Admin panel is fully functional

Your Guessy admin panel installation is now ready! 🎯🎉
