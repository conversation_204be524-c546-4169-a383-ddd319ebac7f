{"name": "pdfmake", "homepage": "https://bpampuch.github.io/pdfmake", "authors": ["<PERSON><PERSON> <<EMAIL>>"], "description": "Client/server side PDF printing in pure JavaScript", "main": ["build/pdfmake.js", "build/vfs_fonts.js"], "moduleType": ["globals"], "keywords": ["pdf", "javascript", "printing", "layout"], "license": "MIT", "ignore": ["**/.*", "dev-playground", "gulpfile.js", "webpack.config.js", "README.md", "bower.json", "examples", "node_modules", "package.json", "src", "tests", "yarn.lock", "composer.json", ".github"]}