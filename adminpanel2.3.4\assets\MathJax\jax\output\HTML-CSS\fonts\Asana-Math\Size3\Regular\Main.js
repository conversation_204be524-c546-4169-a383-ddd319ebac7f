/*
 *  /MathJax/jax/output/HTML-CSS/fonts/Asana-Math/Size3/Regular/Main.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.OutputJax["HTML-CSS"].FONTDATA.FONTS.AsanaMathJax_Size3={directory:"Size3/Regular",family:"AsanaMathJax_Size3",testString:"\u0302\u0303\u0305\u0306\u030C\u0332\u0333\u033F\u2016\u2044\u2045\u2046\u20D6\u20D7\u220F",32:[0,0,249,0,0],40:[1701,851,441,84,402],41:[1701,851,441,40,358],91:[1710,846,381,84,352],93:[1710,846,381,84,352],123:[1697,855,590,51,541],124:[1219,678,270,86,185],125:[1697,855,590,50,540],770:[783,-627,1055,0,1055],771:[772,-642,1402,0,1403],773:[587,-542,1126,0,1127],774:[664,-506,921,0,922],780:[792,-627,1473,0,1474],818:[-130,175,1126,0,1127],819:[-130,283,1126,0,1127],831:[695,-542,1126,0,1127],8214:[1219,678,539,86,454],8260:[1037,758,491,-180,494],8261:[1435,893,375,48,319],8262:[1435,893,385,14,285],8406:[790,-520,1878,0,1878],8407:[790,-520,1878,0,1878],8719:[1868,929,2968,161,2809],8720:[1868,929,2968,161,2809],8721:[1852,924,2539,184,2353],8730:[2543,0,995,63,1027],8745:[1725,863,1860,178,1684],8747:[2314,1156,1726,54,1727],8748:[2314,1156,2828,54,2679],8749:[2314,1156,3780,54,3631],8750:[2314,1156,1876,54,1727],8751:[2314,1156,2828,54,2679],8752:[2314,1156,3780,54,3631],8753:[2314,1156,1875,54,1726],8754:[2314,1156,1963,141,1814],8755:[2314,1156,1876,54,1727],8899:[1725,863,1860,178,1684],8968:[1701,851,390,86,348],8969:[1701,851,390,86,348],8970:[1701,851,390,86,348],8971:[1701,851,390,86,348],9140:[755,-487,1689,0,1690],9141:[-207,475,1689,0,1690],9180:[848,-530,1685,0,1686],9181:[-530,848,1685,0,1686],9182:[1035,-545,2653,51,2603],9183:[-545,1035,2653,51,2603],9184:[755,-545,3107,0,3108],9185:[-545,755,3107,0,3108],10181:[1291,750,450,53,397],10182:[1258,783,450,53,397],10214:[1363,682,523,84,494],10215:[1363,682,523,84,494],10216:[1702,850,471,53,419],10217:[1702,850,471,53,419],10218:[1702,850,665,53,613],10219:[1702,850,665,53,613],10748:[1577,789,589,55,535],10749:[1577,789,589,54,535],10764:[2314,1156,4730,54,4581],10765:[2314,1156,1876,54,1727],10766:[2314,1156,1876,54,1727],10767:[2314,1156,1876,54,1727],10768:[2314,1156,1876,54,1727],10769:[2314,1156,1986,54,1837],10770:[2314,1156,1876,54,1727],10771:[2314,1156,1876,54,1727],10772:[2314,1156,1876,54,1727],10773:[2314,1156,1876,54,1727],10774:[2314,1156,1876,54,1727],10775:[2314,1156,2463,54,2364],10776:[2314,1156,1876,54,1727],10777:[2314,1156,1876,54,1727],10778:[2314,1156,1876,54,1727],10779:[2598,1156,1894,54,1745],10780:[2598,1156,1894,54,1745]};MathJax.Callback.Queue(["initFont",MathJax.OutputJax["HTML-CSS"],"AsanaMathJax_Size3"],["loadComplete",MathJax.Ajax,MathJax.OutputJax["HTML-CSS"].fontDir+"/Size3/Regular/Main.js"]);
