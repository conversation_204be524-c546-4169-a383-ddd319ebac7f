/*
 *  /MathJax/fonts/HTML-CSS/TeX/png/AMS/Regular/MiscSymbols.js
 *  
 *  Copyright (c) 2010-2013 The MathJax Consortium
 *
 *  Part of the MathJax library.
 *  See http://www.mathjax.org for details.
 * 
 *  Licensed under the Apache License, Version 2.0;
 *  you may not use this file except in compliance with the License.
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 */

MathJax.OutputJax["HTML-CSS"].defineImageData({MathJax_AMS:{9733:[[7,6,1],[8,7,1],[9,9,2],[11,11,2],[13,12,2],[15,14,2],[18,17,3],[21,20,3],[25,24,4],[30,27,4],[36,33,5],[42,39,6],[50,46,7],[60,54,8]]}});MathJax.Ajax.loadComplete(MathJax.OutputJax["HTML-CSS"].imgDir+"/AMS/Regular"+MathJax.OutputJax["HTML-CSS"].imgPacked+"/MiscSymbols.js");

