/*************************************************************
 *
 *  MathJax/fonts/HTML-CSS/TeX/png/Main/Bold/GreekAndCoptic.js
 *  
 *  Defines the image size data needed for the HTML-CSS OutputJax
 *  to display mathematics using fallback images when the fonts
 *  are not available to the client browser.
 *
 *  ---------------------------------------------------------------------
 *
 *  Copyright (c) 2009-2013 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 *
 */

MathJax.OutputJax["HTML-CSS"].defineImageData({
  "MathJax_Main-bold": {
    0x393: [  // GREEK CAPITAL LETTER GAMMA
      [5,5,0],[6,6,0],[7,8,0],[8,8,0],[9,9,0],[11,12,0],[13,14,0],[15,17,0],
      [18,20,0],[22,23,0],[26,27,0],[30,32,0],[36,38,0],[43,46,0]
    ],
    0x394: [  // GREEK CAPITAL LETTER DELTA
      [7,5,0],[8,6,0],[9,8,0],[11,8,0],[13,9,0],[15,12,0],[18,14,0],[21,17,0],
      [25,20,0],[30,23,0],[36,28,0],[42,32,0],[50,38,0],[60,46,0]
    ],
    0x398: [  // GREEK CAPITAL LETTER THETA
      [6,5,0],[7,6,0],[9,8,0],[10,8,0],[12,9,0],[14,12,0],[17,14,0],[20,17,0],
      [23,20,0],[28,23,0],[33,28,0],[39,32,0],[46,38,0],[55,46,0]
    ],
    0x39B: [  // GREEK CAPITAL LETTER LAMDA
      [6,5,0],[7,6,0],[8,8,0],[9,8,0],[11,9,0],[13,12,0],[15,14,0],[18,17,0],
      [22,20,0],[26,23,0],[30,28,0],[36,32,0],[43,38,0],[51,46,0]
    ],
    0x39E: [  // GREEK CAPITAL LETTER XI
      [5,5,0],[6,6,0],[8,8,0],[9,8,0],[10,9,0],[12,12,0],[14,14,0],[17,17,0],
      [20,20,0],[24,23,0],[28,27,0],[34,31,0],[40,37,0],[48,45,0]
    ],
    0x3A0: [  // GREEK CAPITAL LETTER PI
      [6,5,0],[8,6,0],[9,8,0],[11,8,0],[12,9,0],[15,12,0],[17,14,0],[20,17,0],
      [24,20,0],[29,23,0],[34,27,0],[40,32,0],[48,38,0],[57,46,0]
    ],
    0x3A3: [  // GREEK CAPITAL LETTER SIGMA
      [6,5,0],[7,6,0],[8,8,0],[9,8,0],[11,9,0],[13,12,0],[15,14,0],[18,17,0],
      [22,20,0],[26,23,0],[30,28,0],[36,32,0],[43,38,0],[51,46,0]
    ],
    0x3A5: [  // GREEK CAPITAL LETTER UPSILON
      [6,5,0],[7,6,0],[9,8,0],[10,8,0],[12,9,0],[14,12,0],[17,14,0],[20,17,0],
      [23,20,0],[28,23,0],[33,28,0],[39,32,0],[46,38,0],[55,46,0]
    ],
    0x3A6: [  // GREEK CAPITAL LETTER PHI
      [6,5,0],[7,6,0],[8,8,0],[9,8,0],[11,9,0],[13,12,0],[15,14,0],[18,17,0],
      [22,20,0],[26,23,0],[30,28,0],[36,32,0],[43,38,0],[51,46,0]
    ],
    0x3A8: [  // GREEK CAPITAL LETTER PSI
      [6,5,0],[7,6,0],[9,8,0],[10,8,0],[12,9,0],[14,12,0],[17,14,0],[20,17,0],
      [23,20,0],[28,23,0],[33,28,0],[39,32,0],[46,38,0],[55,46,0]
    ],
    0x3A9: [  // GREEK CAPITAL LETTER OMEGA
      [6,5,0],[7,6,0],[8,8,0],[10,8,0],[11,9,0],[13,12,0],[16,14,0],[18,17,0],
      [22,20,0],[26,23,0],[31,28,0],[36,32,0],[43,38,0],[51,46,0]
    ]
  }
});

MathJax.Ajax.loadComplete(MathJax.OutputJax["HTML-CSS"].imgDir+"/Main/Bold"+
                          MathJax.OutputJax["HTML-CSS"].imgPacked+"/GreekAndCoptic.js");
