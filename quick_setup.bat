@echo off
echo ========================================
echo Elite Quiz Local Development Setup
echo ========================================
echo.

:: Check if running as administrator
net session >nul 2>&1
if %errorLevel% == 0 (
    echo Running as Administrator... Good!
) else (
    echo Please run this script as Administrator for proper file permissions
    pause
    exit /b 1
)

:: Set variables
set XAMPP_PATH=C:\xampp
set PROJECT_PATH=%cd%
set ADMIN_PATH=%XAMPP_PATH%\htdocs\elite_quiz_admin

echo Current directory: %PROJECT_PATH%
echo XAMPP path: %XAMPP_PATH%
echo Admin panel will be installed at: %ADMIN_PATH%
echo.

:: Check if XAMPP is installed
if not exist "%XAMPP_PATH%" (
    echo ERROR: XAMPP not found at %XAMPP_PATH%
    echo Please install XAMPP first from https://www.apachefriends.org/
    pause
    exit /b 1
)

:: Check if admin panel exists
if not exist "adminpanel2.3.4" (
    echo ERROR: adminpanel2.3.4 directory not found!
    echo Please make sure you're running this script from the project root.
    pause
    exit /b 1
)

echo Step 1: Configuring admin panel...
:: Run PHP configuration script
php setup_local_config.php
if %errorLevel% neq 0 (
    echo ERROR: Failed to configure admin panel
    pause
    exit /b 1
)

echo.
echo Step 2: Copying admin panel to XAMPP...
:: Copy admin panel to XAMPP htdocs
if exist "%ADMIN_PATH%" (
    echo Removing existing admin panel...
    rmdir /s /q "%ADMIN_PATH%"
)

echo Copying files...
xcopy "adminpanel2.3.4" "%ADMIN_PATH%" /E /I /H /Y
if %errorLevel% neq 0 (
    echo ERROR: Failed to copy admin panel files
    pause
    exit /b 1
)

echo.
echo Step 3: Setting up database...
:: Start XAMPP services
echo Starting XAMPP services...
"%XAMPP_PATH%\xampp_start.exe"

:: Wait a moment for services to start
timeout /t 5 /nobreak >nul

:: Create database using MySQL command line
echo Creating database...
"%XAMPP_PATH%\mysql\bin\mysql.exe" -u root -e "CREATE DATABASE IF NOT EXISTS elite_quiz_local CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;"
if %errorLevel% neq 0 (
    echo WARNING: Could not create database automatically
    echo Please create database 'elite_quiz_local' manually in phpMyAdmin
)

:: Import database schema
echo Importing database schema...
"%XAMPP_PATH%\mysql\bin\mysql.exe" -u root elite_quiz_local < "adminpanel2.3.4\install\assets\quiz.php"
if %errorLevel% neq 0 (
    echo WARNING: Could not import database automatically
    echo Please import adminpanel2.3.4\install\assets\quiz.php manually in phpMyAdmin
)

echo.
echo Step 4: Configuring Flutter app...
:: Run Dart configuration script
dart setup_flutter_local.dart
if %errorLevel% neq 0 (
    echo ERROR: Failed to configure Flutter app
    echo Please run: dart setup_flutter_local.dart manually
)

echo.
echo Step 5: Installing Flutter dependencies...
cd elite_quiz_app-2.3.4
call flutter pub get
if %errorLevel% neq 0 (
    echo ERROR: Failed to install Flutter dependencies
    echo Please run 'flutter pub get' manually in elite_quiz_app-2.3.4 directory
)

:: Generate app icons
call dart run flutter_launcher_icons
cd ..

echo.
echo ========================================
echo Setup Complete!
echo ========================================
echo.
echo Admin Panel: http://localhost/elite_quiz_admin
echo Login: admin / admin123
echo Database: elite_quiz_local
echo.
echo Next Steps:
echo 1. Configure Firebase project and add config files
echo 2. Update IP address in Flutter config for physical device testing
echo 3. Run Flutter app: flutter run
echo.
echo Troubleshooting:
echo - If admin panel doesn't load, check XAMPP Apache service
echo - If database errors occur, import schema manually from phpMyAdmin
echo - For API connectivity issues, check network security config
echo.
pause
