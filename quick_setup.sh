#!/bin/bash

echo "========================================"
echo "Elite Quiz Local Development Setup"
echo "========================================"
echo

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

# Check if running on macOS or Linux
if [[ "$OSTYPE" == "darwin"* ]]; then
    # macOS
    XAMPP_PATH="/Applications/XAMPP"
    WEB_ROOT="/Applications/XAMPP/htdocs"
elif [[ "$OSTYPE" == "linux-gnu"* ]]; then
    # Linux
    XAMPP_PATH="/opt/lampp"
    WEB_ROOT="/opt/lampp/htdocs"
else
    print_error "Unsupported operating system. Please use Windows batch file or setup manually."
    exit 1
fi

PROJECT_PATH=$(pwd)
ADMIN_PATH="$WEB_ROOT/elite_quiz_admin"

echo "Current directory: $PROJECT_PATH"
echo "XAMPP path: $XAMPP_PATH"
echo "Admin panel will be installed at: $ADMIN_PATH"
echo

# Check if XAMPP is installed
if [ ! -d "$XAMPP_PATH" ]; then
    print_error "XAMPP not found at $XAMPP_PATH"
    print_info "Please install XAMPP first from https://www.apachefriends.org/"
    exit 1
fi

# Check if admin panel exists
if [ ! -d "adminpanel2.3.4" ]; then
    print_error "adminpanel2.3.4 directory not found!"
    print_info "Please make sure you're running this script from the project root."
    exit 1
fi

# Check if PHP is available
if ! command -v php &> /dev/null; then
    print_error "PHP not found. Please install PHP or add it to PATH."
    exit 1
fi

# Check if Flutter is available
if ! command -v flutter &> /dev/null; then
    print_warning "Flutter not found. Please install Flutter SDK."
fi

# Check if Dart is available
if ! command -v dart &> /dev/null; then
    print_warning "Dart not found. Please install Dart SDK."
fi

echo "Step 1: Configuring admin panel..."
# Run PHP configuration script
php setup_local_config.php
if [ $? -ne 0 ]; then
    print_error "Failed to configure admin panel"
    exit 1
fi

echo
echo "Step 2: Copying admin panel to XAMPP..."
# Copy admin panel to XAMPP htdocs
if [ -d "$ADMIN_PATH" ]; then
    print_info "Removing existing admin panel..."
    sudo rm -rf "$ADMIN_PATH"
fi

print_info "Copying files..."
sudo cp -r "adminpanel2.3.4" "$ADMIN_PATH"
if [ $? -ne 0 ]; then
    print_error "Failed to copy admin panel files"
    print_info "You may need to run with sudo: sudo ./quick_setup.sh"
    exit 1
fi

# Set proper permissions
sudo chmod -R 755 "$ADMIN_PATH"
sudo chmod -R 777 "$ADMIN_PATH/images"
sudo chmod -R 777 "$ADMIN_PATH/upload"

print_status "Admin panel copied successfully"

echo
echo "Step 3: Setting up database..."
# Start XAMPP services
print_info "Starting XAMPP services..."
if [[ "$OSTYPE" == "darwin"* ]]; then
    # macOS
    sudo "$XAMPP_PATH/xamppfiles/xampp" start
elif [[ "$OSTYPE" == "linux-gnu"* ]]; then
    # Linux
    sudo "$XAMPP_PATH/lampp" start
fi

# Wait for services to start
sleep 5

# Create database
print_info "Creating database..."
if [[ "$OSTYPE" == "darwin"* ]]; then
    MYSQL_PATH="$XAMPP_PATH/xamppfiles/bin/mysql"
else
    MYSQL_PATH="$XAMPP_PATH/bin/mysql"
fi

sudo "$MYSQL_PATH" -u root -e "CREATE DATABASE IF NOT EXISTS elite_quiz_local CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;"
if [ $? -eq 0 ]; then
    print_status "Database created successfully"
else
    print_warning "Could not create database automatically"
    print_info "Please create database 'elite_quiz_local' manually in phpMyAdmin"
fi

# Import database schema
print_info "Importing database schema..."
sudo "$MYSQL_PATH" -u root elite_quiz_local < "adminpanel2.3.4/install/assets/quiz.php"
if [ $? -eq 0 ]; then
    print_status "Database schema imported successfully"
else
    print_warning "Could not import database automatically"
    print_info "Please import adminpanel2.3.4/install/assets/quiz.php manually in phpMyAdmin"
fi

echo
echo "Step 4: Configuring Flutter app..."
# Run Dart configuration script
if command -v dart &> /dev/null; then
    dart setup_flutter_local.dart
    if [ $? -ne 0 ]; then
        print_error "Failed to configure Flutter app"
        print_info "Please run: dart setup_flutter_local.dart manually"
    fi
else
    print_warning "Dart not found, skipping Flutter configuration"
    print_info "Please run: dart setup_flutter_local.dart manually after installing Dart"
fi

echo
echo "Step 5: Installing Flutter dependencies..."
if command -v flutter &> /dev/null; then
    cd elite_quiz_app-2.3.4
    flutter pub get
    if [ $? -ne 0 ]; then
        print_error "Failed to install Flutter dependencies"
        print_info "Please run 'flutter pub get' manually in elite_quiz_app-2.3.4 directory"
    else
        print_status "Flutter dependencies installed"
    fi
    
    # Generate app icons
    dart run flutter_launcher_icons
    cd ..
else
    print_warning "Flutter not found, skipping dependency installation"
    print_info "Please install Flutter SDK and run 'flutter pub get' in elite_quiz_app-2.3.4 directory"
fi

echo
echo "========================================"
echo "Setup Complete!"
echo "========================================"
echo
print_status "Admin Panel: http://localhost/elite_quiz_admin"
print_status "Login: admin / admin123"
print_status "Database: elite_quiz_local"
echo
print_info "Next Steps:"
echo "1. Configure Firebase project and add config files"
echo "2. Update IP address in Flutter config for physical device testing"
echo "3. Run Flutter app: flutter run"
echo
print_info "Troubleshooting:"
echo "- If admin panel doesn't load, check XAMPP Apache service"
echo "- If database errors occur, import schema manually from phpMyAdmin"
echo "- For API connectivity issues, check network security config"
echo
print_info "Useful Commands:"
echo "- Start XAMPP: sudo $XAMPP_PATH/xampp start"
echo "- Stop XAMPP: sudo $XAMPP_PATH/xampp stop"
echo "- Restart XAMPP: sudo $XAMPP_PATH/xampp restart"
echo
