/*
 *  /MathJax/fonts/HTML-CSS/TeX/png/Main/Regular/SuppMathOperators.js
 *  
 *  Copyright (c) 2010-2013 The MathJax Consortium
 *
 *  Part of the MathJax library.
 *  See http://www.mathjax.org for details.
 * 
 *  Licensed under the Apache License, Version 2.0;
 *  you may not use this file except in compliance with the License.
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 */

MathJax.OutputJax["HTML-CSS"].defineImageData({MathJax_Main:{10815:[[5,5,0],[6,6,0],[8,7,0],[9,9,0],[10,10,0],[12,12,0],[15,14,0],[17,16,0],[20,19,0],[24,23,0],[29,27,0],[34,32,0],[40,38,0],[48,45,0]],10927:[[5,6,1],[6,7,1],[7,9,2],[9,10,2],[10,11,2],[12,14,3],[14,16,3],[17,18,3],[20,22,4],[23,26,5],[28,31,6],[33,37,7],[39,44,8],[46,52,10]],10928:[[5,6,1],[6,7,1],[7,9,2],[9,10,2],[10,11,2],[12,14,3],[14,16,3],[17,19,4],[20,22,4],[23,26,5],[28,31,6],[33,37,7],[39,44,8],[46,52,10]]}});MathJax.Ajax.loadComplete(MathJax.OutputJax["HTML-CSS"].imgDir+"/Main/Regular"+MathJax.OutputJax["HTML-CSS"].imgPacked+"/SuppMathOperators.js");

