/*
 *  /MathJax/fonts/HTML-CSS/TeX/png/Main/Bold/LatinExtendedA.js
 *  
 *  Copyright (c) 2010-2013 The MathJax Consortium
 *
 *  Part of the MathJax library.
 *  See http://www.mathjax.org for details.
 * 
 *  Licensed under the Apache License, Version 2.0;
 *  you may not use this file except in compliance with the License.
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 */

MathJax.OutputJax["HTML-CSS"].defineImageData({"MathJax_Main-bold":{305:[[3,3,0],[4,4,0],[4,5,0],[5,5,0],[6,6,0],[7,8,0],[8,9,0],[9,11,0],[11,13,0],[12,15,0],[15,18,0],[17,21,0],[21,25,0],[24,30,0]]}});MathJax.Ajax.loadComplete(MathJax.OutputJax["HTML-CSS"].imgDir+"/Main/Bold"+MathJax.OutputJax["HTML-CSS"].imgPacked+"/LatinExtendedA.js");

