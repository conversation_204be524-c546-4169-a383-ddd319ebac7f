import 'package:flutter/material.dart';

/// 🌞 Light Theme
const klBackgroundColor = Color(0xFFFFFFFF);           // Pure White
const klCanvasColor = Color(0xFFE0E0E0);               // Light Grey Canvas
const klPageBackgroundColor = Color(0xFFF9FAFB);       // Near-white page BG
const klPrimaryColor = Color(0xFF007BFF);              // Bootstrap-like Blue
const klPrimaryTextColor = Color(0xFF212529);          // Dark Text for readability

/// 🌙 Dark Theme
const kdBackgroundColor = Color(0xFF121212);           // True Black
const kdCanvasColor = Color(0xFF1E1E1E);               // Dark Grey Canvas
const kdPageBackgroundColor = Color(0xFF1A1A1A);       // Slightly lighter than background
const kdPrimaryColor = Color(0xFF0D6EFD);              // Lighter blue for visibility
const kdPrimaryTextColor = Color(0xFFF1F1F1);          // Soft white text

/// 🎨 Common
const Color kAddCoinColor = Color(0xFF28A745);         // Bootstrap success green
const Color kBadgeLockedColor = Color(0xFF6C757D);     // Muted Grey
const Color kHurryUpTimerColor = Color(0xFFFF4D4F);    // Alert Red
const Color kCorrectAnswerColor = Color(0xFF28A745);   // Same as AddCoin (success)
const Color kWrongAnswerColor = Color(0xFFFF4D4F);     // Same as HurryUp (error)
const Color kPendingColor = Color(0xFFFFB74D); // Soft Orange (Material Design)
const Color kCompletedColor = Color(0xFF28A745); // Bootstrap success green
const Color kFailedColor = Color(0xFFFF4D4F); // Alert Red

