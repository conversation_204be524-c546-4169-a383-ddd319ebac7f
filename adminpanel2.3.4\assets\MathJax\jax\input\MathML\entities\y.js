/*
 *  /MathJax/jax/input/MathML/entities/y.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

(function(a){MathJax.Hub.Insert(a.Parse.Entity,{YAcy:"\u042F",YIcy:"\u0407",YUcy:"\u042E",Yacute:"\u00DD",Ycirc:"\u0176",Ycy:"\u042B",Yuml:"\u0178",yacute:"\u00FD",yacy:"\u044F",ycirc:"\u0177",ycy:"\u044B",yicy:"\u0457",yucy:"\u044E",yuml:"\u00FF"});MathJax.Ajax.loadComplete(a.entityDir+"/y.js")})(MathJax.InputJax.MathML);
