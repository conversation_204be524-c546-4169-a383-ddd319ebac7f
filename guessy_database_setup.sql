-- Guessy Database Setup and Configuration
-- Run this script after importing the base schema

-- Update app settings for Guessy branding
UPDATE tbl_settings SET message = 'Guessy' WHERE type = 'app_name';
UPDATE tbl_settings SET message = 'The Ultimate Quiz Challenge' WHERE type = 'app_tagline';
UPDATE tbl_settings SET message = 'Welcome to Guessy - Test Your Knowledge!' WHERE type = 'welcome_message';
UPDATE tbl_settings SET message = 'com.guessy.quiz' WHERE type = 'package_name';

-- Update system configurations for Guessy
UPDATE tbl_settings SET message = 'Asia/Kolkata' WHERE type = 'system_timezone';
UPDATE tbl_settings SET message = '+05:30' WHERE type = 'system_timezone_gmt';
UPDATE tbl_settings SET message = '2.3.4' WHERE type = 'app_version';
UPDATE tbl_settings SET message = '50' WHERE type = 'refer_coin';
UPDATE tbl_settings SET message = '25' WHERE type = 'earn_coin';
UPDATE tbl_settings SET message = '10' WHERE type = 'reward_coin';

-- Insert Guessy-specific settings
INSERT INTO tbl_settings (type, message) VALUES 
('guessy_theme_color', '#6C63FF'),
('guessy_secondary_color', '#4CAF50'),
('guessy_accent_color', '#FF9800'),
('daily_quiz_reward', '20'),
('battle_win_reward', '15'),
('contest_participation_reward', '5'),
('perfect_score_bonus', '25'),
('streak_bonus_multiplier', '2'),
('max_daily_ads', '5'),
('ads_reward_coins', '3');

-- Clear existing categories and add Guessy categories
DELETE FROM tbl_category;
INSERT INTO tbl_category (id, category_name, image, maxlevel, no_of_que, is_premium, coins, status) VALUES
(1, 'General Knowledge', 'general_knowledge.png', 10, 15, 0, 0, 1),
(2, 'Word Puzzles', 'word_puzzles.png', 8, 12, 0, 0, 1),
(3, 'Math Challenges', 'math_challenges.png', 12, 10, 0, 0, 1),
(4, 'Science & Technology', 'science_tech.png', 10, 15, 0, 0, 1),
(5, 'Sports & Games', 'sports_games.png', 8, 12, 0, 0, 1),
(6, 'Entertainment', 'entertainment.png', 6, 10, 0, 0, 1),
(7, 'History & Geography', 'history_geo.png', 9, 12, 0, 0, 1),
(8, 'Current Affairs', 'current_affairs.png', 7, 10, 1, 50, 1),
(9, 'Brain Teasers', 'brain_teasers.png', 15, 8, 1, 75, 1),
(10, 'Mixed Bag', 'mixed_bag.png', 5, 20, 0, 0, 1);

-- Clear existing questions and add sample Guessy questions
DELETE FROM tbl_question;

-- General Knowledge Questions
INSERT INTO tbl_question (category, subcategory, language_id, question, question_type, optiona, optionb, optionc, optiond, answer, level, note) VALUES
(1, 0, 1, 'What is the capital of India?', 1, 'Mumbai', 'New Delhi', 'Kolkata', 'Chennai', 'b', 1, 'New Delhi is the capital and seat of government of India.'),
(1, 0, 1, 'Which planet is known as the Red Planet?', 1, 'Venus', 'Mars', 'Jupiter', 'Saturn', 'b', 1, 'Mars appears red due to iron oxide on its surface.'),
(1, 0, 1, 'Who wrote the play "Romeo and Juliet"?', 1, 'Charles Dickens', 'William Shakespeare', 'Jane Austen', 'Mark Twain', 'b', 2, 'Shakespeare wrote this famous tragedy in the early part of his career.'),
(1, 0, 1, 'What is the largest ocean on Earth?', 1, 'Atlantic Ocean', 'Indian Ocean', 'Pacific Ocean', 'Arctic Ocean', 'c', 1, 'The Pacific Ocean covers about 46% of the water surface.'),
(1, 0, 1, 'In which year did World War II end?', 1, '1944', '1945', '1946', '1947', 'b', 2, 'World War II ended in 1945 with the surrender of Japan.');

-- Word Puzzles Questions
INSERT INTO tbl_question (category, subcategory, language_id, question, question_type, optiona, optionb, optionc, optiond, answer, level, note) VALUES
(2, 0, 1, 'What is the opposite of "ancient"?', 1, 'Old', 'Modern', 'Historic', 'Vintage', 'b', 1, 'Modern is the antonym of ancient.'),
(2, 0, 1, 'Which word means "a person who studies stars"?', 1, 'Geologist', 'Astronomer', 'Biologist', 'Archaeologist', 'b', 2, 'An astronomer studies celestial objects and space.'),
(2, 0, 1, 'What does "bibliophile" mean?', 1, 'Book lover', 'Music lover', 'Art lover', 'Food lover', 'a', 3, 'A bibliophile is someone who loves and collects books.'),
(2, 0, 1, 'Which is the correct spelling?', 1, 'Recieve', 'Receive', 'Receve', 'Receeve', 'b', 2, 'Remember: i before e except after c.'),
(2, 0, 1, 'What is a synonym for "happy"?', 1, 'Sad', 'Joyful', 'Angry', 'Tired', 'b', 1, 'Joyful means feeling great pleasure and happiness.');

-- Math Challenges Questions
INSERT INTO tbl_question (category, subcategory, language_id, question, question_type, optiona, optionb, optionc, optiond, answer, level, note) VALUES
(3, 0, 1, 'What is 15 + 27?', 1, '42', '41', '43', '40', 'a', 1, 'Simple addition: 15 + 27 = 42'),
(3, 0, 1, 'What is 8 × 7?', 1, '54', '56', '58', '52', 'b', 1, 'Multiplication: 8 × 7 = 56'),
(3, 0, 1, 'What is the square root of 64?', 1, '6', '7', '8', '9', 'c', 2, 'Square root of 64 is 8 because 8 × 8 = 64'),
(3, 0, 1, 'If a triangle has angles of 60°, 60°, and 60°, what type of triangle is it?', 1, 'Right triangle', 'Equilateral triangle', 'Isosceles triangle', 'Scalene triangle', 'b', 3, 'An equilateral triangle has all angles equal to 60°'),
(3, 0, 1, 'What is 25% of 200?', 1, '25', '50', '75', '100', 'b', 2, '25% of 200 = (25/100) × 200 = 50');

-- Science & Technology Questions
INSERT INTO tbl_question (category, subcategory, language_id, question, question_type, optiona, optionb, optionc, optiond, answer, level, note) VALUES
(4, 0, 1, 'What is the chemical symbol for water?', 1, 'H2O', 'CO2', 'NaCl', 'O2', 'a', 1, 'Water is composed of two hydrogen atoms and one oxygen atom.'),
(4, 0, 1, 'Who invented the telephone?', 1, 'Thomas Edison', 'Alexander Graham Bell', 'Nikola Tesla', 'Benjamin Franklin', 'b', 2, 'Alexander Graham Bell patented the telephone in 1876.'),
(4, 0, 1, 'What does "WWW" stand for?', 1, 'World Wide Web', 'World Wide Website', 'World Web Wide', 'Wide World Web', 'a', 1, 'WWW stands for World Wide Web.'),
(4, 0, 1, 'How many bones are in the adult human body?', 1, '206', '208', '210', '212', 'a', 2, 'An adult human has 206 bones.'),
(4, 0, 1, 'What is the speed of light?', 1, '300,000 km/s', '299,792,458 m/s', '186,000 miles/s', 'All of the above', 'd', 3, 'The speed of light is approximately 299,792,458 meters per second.');

-- Sports & Games Questions
INSERT INTO tbl_question (category, subcategory, language_id, question, question_type, optiona, optionb, optionc, optiond, answer, level, note) VALUES
(5, 0, 1, 'How many players are on a basketball team on the court?', 1, '4', '5', '6', '7', 'b', 1, 'Each basketball team has 5 players on the court at a time.'),
(5, 0, 1, 'In which sport would you perform a slam dunk?', 1, 'Football', 'Basketball', 'Tennis', 'Baseball', 'b', 1, 'A slam dunk is a basketball move.'),
(5, 0, 1, 'How often are the Summer Olympics held?', 1, 'Every 2 years', 'Every 3 years', 'Every 4 years', 'Every 5 years', 'c', 2, 'The Summer Olympics are held every 4 years.'),
(5, 0, 1, 'What is the maximum score possible in ten-pin bowling?', 1, '250', '270', '290', '300', 'd', 2, 'A perfect game in bowling scores 300 points.'),
(5, 0, 1, 'Which country has won the most FIFA World Cups?', 1, 'Germany', 'Argentina', 'Brazil', 'Italy', 'c', 2, 'Brazil has won the FIFA World Cup 5 times.');

-- Update badges for Guessy
UPDATE tbl_badges SET badge_label = 'First Steps', badge_note = 'Welcome to Guessy! Play your first quiz.' WHERE type = 'dashing_debut';
UPDATE tbl_badges SET badge_label = 'Quiz Champion', badge_note = 'Win your first battle in Guessy!' WHERE type = 'combat_winner';
UPDATE tbl_badges SET badge_label = 'Team Player', badge_note = 'Win a group battle with friends!' WHERE type = 'clash_winner';
UPDATE tbl_badges SET badge_label = 'Contest Master', badge_note = 'Dominate a Guessy contest!' WHERE type = 'most_wanted_winner';
UPDATE tbl_badges SET badge_label = 'Knowledge Seeker', badge_note = 'Answer questions correctly and gain wisdom!' WHERE type = 'quiz_warrior';
UPDATE tbl_badges SET badge_label = 'Speed Demon', badge_note = 'Lightning fast answers in Guessy!' WHERE type = 'super_sonic';

-- Update admin user for Guessy (optional)
UPDATE tbl_authenticate SET auth_username = 'guessy_admin' WHERE auth_id = 1;

-- Add Guessy-specific notification templates
INSERT INTO tbl_settings (type, message) VALUES 
('welcome_notification', 'Welcome to Guessy! Start your quiz journey now!'),
('daily_quiz_reminder', 'Don''t forget your daily quiz challenge!'),
('battle_invitation', 'You''ve been challenged to a quiz battle!'),
('contest_announcement', 'New contest starting soon in Guessy!'),
('achievement_unlock', 'Congratulations! You''ve unlocked a new badge!'),
('coin_reward', 'You''ve earned coins! Keep playing to earn more!');

-- Create indexes for better performance
CREATE INDEX idx_question_category ON tbl_question(category);
CREATE INDEX idx_question_level ON tbl_question(level);
CREATE INDEX idx_user_coins ON tbl_users(coins);
CREATE INDEX idx_leaderboard_score ON tbl_leaderboard_daily(score);

-- Insert sample contest data
INSERT INTO tbl_contest (title, description, image, start_date, end_date, entry_fee, prize_pool, status) VALUES
('Guessy Weekly Challenge', 'Test your knowledge across all categories!', 'weekly_contest.png', NOW(), DATE_ADD(NOW(), INTERVAL 7 DAY), 10, 500, 1),
('Math Mania Contest', 'Show off your mathematical skills!', 'math_contest.png', DATE_ADD(NOW(), INTERVAL 1 DAY), DATE_ADD(NOW(), INTERVAL 8 DAY), 15, 750, 1),
('General Knowledge Championship', 'The ultimate test of general knowledge!', 'gk_contest.png', DATE_ADD(NOW(), INTERVAL 3 DAY), DATE_ADD(NOW(), INTERVAL 10 DAY), 20, 1000, 1);

-- Set up coin store items
INSERT INTO tbl_coin_store (title, description, image, coins, type, status) VALUES
('Remove Ads', 'Enjoy Guessy without any advertisements', 'remove_ads.png', 500, 'remove_ads', 1),
('Extra Lifelines', 'Get 5 additional lifelines for quizzes', 'lifelines.png', 100, 'lifelines', 1),
('Premium Categories', 'Unlock all premium quiz categories', 'premium.png', 1000, 'premium', 1),
('Double Coins', 'Earn double coins for 24 hours', 'double_coins.png', 200, 'double_coins', 1),
('Hint Pack', 'Get 10 hints for word puzzles', 'hints.png', 150, 'hints', 1);

-- Final message
SELECT 'Guessy database setup completed successfully!' as message;
