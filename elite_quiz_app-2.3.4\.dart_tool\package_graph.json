{"roots": ["guessy"], "packages": [{"name": "guessy", "version": "2.3.4+55", "dependencies": ["cached_network_image", "cloud_firestore", "connectivity_plus", "country_code_picker", "dotted_border", "encrypt", "firebase_analytics", "firebase_auth", "firebase_core", "firebase_messaging", "flutter", "flutter_bloc", "flutter_cached_pdfview", "flutter_local_notifications", "flutter_pdfview", "flutter_svg", "flutter_tex", "flutter_timezone", "flutter_widget_from_html", "google_fonts", "google_mobile_ads", "google_sign_in", "hive_flutter", "http", "iabtcf_consent_info", "image_cropper", "image_picker", "in_app_purchase", "in_app_purchase_android", "internet_connection_checker", "intl", "just_audio", "lottie", "package_info_plus", "path_provider", "permission_handler", "pin_code_fields", "scratcher", "screenshot", "share_plus", "sign_in_with_apple", "unity_ads_plugin", "url_launcher", "wakelock_plus", "webview_flutter_plus", "youtube_player_flutter"], "devDependencies": ["change_app_package_name", "dart_code_metrics_presets", "flutter_launcher_icons", "very_good_analysis"]}, {"name": "very_good_analysis", "version": "9.0.0", "dependencies": []}, {"name": "flutter_launcher_icons", "version": "0.14.4", "dependencies": ["args", "checked_yaml", "cli_util", "image", "json_annotation", "path", "yaml"]}, {"name": "dart_code_metrics_presets", "version": "2.24.0", "dependencies": []}, {"name": "change_app_package_name", "version": "1.5.0", "dependencies": []}, {"name": "youtube_player_flutter", "version": "9.1.1", "dependencies": ["flutter", "flutter_inappwebview"]}, {"name": "webview_flutter_plus", "version": "0.4.7", "dependencies": ["flutter", "mime", "webview_flutter"]}, {"name": "wakelock_plus", "version": "1.3.2", "dependencies": ["dbus", "flutter", "flutter_web_plugins", "meta", "package_info_plus", "wakelock_plus_platform_interface", "web", "win32"]}, {"name": "url_launcher", "version": "6.3.1", "dependencies": ["flutter", "url_launcher_android", "url_launcher_ios", "url_launcher_linux", "url_launcher_macos", "url_launcher_platform_interface", "url_launcher_web", "url_launcher_windows"]}, {"name": "unity_ads_plugin", "version": "0.3.25", "dependencies": ["flutter"]}, {"name": "sign_in_with_apple", "version": "7.0.1", "dependencies": ["flutter", "meta", "sign_in_with_apple_platform_interface", "sign_in_with_apple_web"]}, {"name": "share_plus", "version": "11.0.0", "dependencies": ["cross_file", "ffi", "file", "flutter", "flutter_web_plugins", "meta", "mime", "share_plus_platform_interface", "url_launcher_linux", "url_launcher_platform_interface", "url_launcher_web", "url_launcher_windows", "web", "win32"]}, {"name": "screenshot", "version": "3.0.0", "dependencies": ["flutter"]}, {"name": "scratcher", "version": "2.5.0", "dependencies": ["flutter"]}, {"name": "pin_code_fields", "version": "8.0.1", "dependencies": ["flutter"]}, {"name": "permission_handler", "version": "12.0.1", "dependencies": ["flutter", "meta", "permission_handler_android", "permission_handler_apple", "permission_handler_html", "permission_handler_platform_interface", "permission_handler_windows"]}, {"name": "path_provider", "version": "2.1.5", "dependencies": ["flutter", "path_provider_android", "path_provider_foundation", "path_provider_linux", "path_provider_platform_interface", "path_provider_windows"]}, {"name": "package_info_plus", "version": "8.3.0", "dependencies": ["clock", "ffi", "flutter", "flutter_web_plugins", "http", "meta", "package_info_plus_platform_interface", "path", "web", "win32"]}, {"name": "lottie", "version": "3.3.1", "dependencies": ["archive", "flutter", "http", "path", "vector_math"]}, {"name": "just_audio", "version": "0.9.46", "dependencies": ["async", "audio_session", "crypto", "flutter", "just_audio_platform_interface", "just_audio_web", "meta", "path", "path_provider", "rxdart", "uuid"]}, {"name": "intl", "version": "0.20.2", "dependencies": ["clock", "meta", "path"]}, {"name": "internet_connection_checker", "version": "3.0.1", "dependencies": ["connectivity_plus", "equatable", "flutter", "http"]}, {"name": "in_app_purchase_android", "version": "0.4.0+2", "dependencies": ["collection", "flutter", "in_app_purchase_platform_interface"]}, {"name": "in_app_purchase", "version": "3.2.3", "dependencies": ["flutter", "in_app_purchase_android", "in_app_purchase_platform_interface", "in_app_purchase_storekit"]}, {"name": "image_picker", "version": "1.1.2", "dependencies": ["flutter", "image_picker_android", "image_picker_for_web", "image_picker_ios", "image_picker_linux", "image_picker_macos", "image_picker_platform_interface", "image_picker_windows"]}, {"name": "image_cropper", "version": "9.1.0", "dependencies": ["flutter", "image_cropper_for_web", "image_cropper_platform_interface"]}, {"name": "iabtcf_consent_info", "version": "3.4.0+1", "dependencies": ["flutter", "iabtcf_consent_info_platform_interface", "iabtcf_consent_info_web", "meta", "rxdart"]}, {"name": "http", "version": "1.4.0", "dependencies": ["async", "http_parser", "meta", "web"]}, {"name": "hive_flutter", "version": "1.1.0", "dependencies": ["flutter", "hive", "path", "path_provider"]}, {"name": "google_sign_in", "version": "6.3.0", "dependencies": ["flutter", "google_sign_in_android", "google_sign_in_ios", "google_sign_in_platform_interface", "google_sign_in_web"]}, {"name": "google_mobile_ads", "version": "6.0.0", "dependencies": ["flutter", "meta", "webview_flutter", "webview_flutter_android", "webview_flutter_wkwebview"]}, {"name": "google_fonts", "version": "6.2.1", "dependencies": ["crypto", "flutter", "http", "path_provider"]}, {"name": "flutter_widget_from_html", "version": "0.16.0", "dependencies": ["flutter", "flutter_widget_from_html_core", "fwfh_cached_network_image", "fwfh_chewie", "fwfh_just_audio", "fwfh_svg", "fwfh_url_launcher", "fwfh_webview", "html"]}, {"name": "flutter_timezone", "version": "4.1.1", "dependencies": ["flutter", "flutter_web_plugins"]}, {"name": "flutter_tex", "version": "4.0.9", "dependencies": ["flutter", "markdown", "webview_flutter_plus"]}, {"name": "flutter_svg", "version": "2.2.0", "dependencies": ["flutter", "http", "vector_graphics", "vector_graphics_codec", "vector_graphics_compiler"]}, {"name": "flutter_pdfview", "version": "1.4.1+1", "dependencies": ["flutter"]}, {"name": "flutter_local_notifications", "version": "19.3.0", "dependencies": ["clock", "flutter", "flutter_local_notifications_linux", "flutter_local_notifications_platform_interface", "flutter_local_notifications_windows", "timezone"]}, {"name": "flutter_cached_pdfview", "version": "0.4.3", "dependencies": ["flutter", "flutter_cache_manager", "flutter_pdfview", "path", "path_provider"]}, {"name": "flutter_bloc", "version": "9.1.1", "dependencies": ["bloc", "flutter", "provider"]}, {"name": "flutter", "version": "0.0.0", "dependencies": ["characters", "collection", "material_color_utilities", "meta", "sky_engine", "vector_math"]}, {"name": "firebase_messaging", "version": "15.2.9", "dependencies": ["firebase_core", "firebase_core_platform_interface", "firebase_messaging_platform_interface", "firebase_messaging_web", "flutter", "meta"]}, {"name": "firebase_core", "version": "3.15.1", "dependencies": ["firebase_core_platform_interface", "firebase_core_web", "flutter", "meta"]}, {"name": "firebase_auth", "version": "5.6.2", "dependencies": ["firebase_auth_platform_interface", "firebase_auth_web", "firebase_core", "firebase_core_platform_interface", "flutter", "meta"]}, {"name": "firebase_analytics", "version": "11.5.2", "dependencies": ["firebase_analytics_platform_interface", "firebase_analytics_web", "firebase_core", "firebase_core_platform_interface", "flutter"]}, {"name": "encrypt", "version": "5.0.3", "dependencies": ["args", "asn1lib", "clock", "collection", "crypto", "pointycastle"]}, {"name": "dotted_border", "version": "3.1.0", "dependencies": ["flutter"]}, {"name": "country_code_picker", "version": "3.3.0", "dependencies": ["collection", "diacritic", "flutter"]}, {"name": "connectivity_plus", "version": "6.1.4", "dependencies": ["collection", "connectivity_plus_platform_interface", "flutter", "flutter_web_plugins", "meta", "nm", "web"]}, {"name": "cloud_firestore", "version": "5.6.11", "dependencies": ["cloud_firestore_platform_interface", "cloud_firestore_web", "collection", "firebase_core", "firebase_core_platform_interface", "flutter", "meta"]}, {"name": "cached_network_image", "version": "3.4.1", "dependencies": ["cached_network_image_platform_interface", "cached_network_image_web", "flutter", "flutter_cache_manager", "octo_image"]}, {"name": "yaml", "version": "3.1.3", "dependencies": ["collection", "source_span", "string_scanner"]}, {"name": "path", "version": "1.9.1", "dependencies": []}, {"name": "json_annotation", "version": "4.9.0", "dependencies": ["meta"]}, {"name": "image", "version": "4.5.4", "dependencies": ["archive", "meta", "xml"]}, {"name": "cli_util", "version": "0.4.2", "dependencies": ["meta", "path"]}, {"name": "checked_yaml", "version": "2.0.4", "dependencies": ["json_annotation", "source_span", "yaml"]}, {"name": "args", "version": "2.7.0", "dependencies": []}, {"name": "flutter_inappwebview", "version": "6.1.5", "dependencies": ["flutter", "flutter_inappwebview_android", "flutter_inappwebview_ios", "flutter_inappwebview_macos", "flutter_inappwebview_platform_interface", "flutter_inappwebview_web", "flutter_inappwebview_windows"]}, {"name": "mime", "version": "1.0.6", "dependencies": []}, {"name": "webview_flutter", "version": "4.13.0", "dependencies": ["flutter", "webview_flutter_android", "webview_flutter_platform_interface", "webview_flutter_wkwebview"]}, {"name": "web", "version": "1.1.1", "dependencies": []}, {"name": "dbus", "version": "0.7.11", "dependencies": ["args", "ffi", "meta", "xml"]}, {"name": "win32", "version": "5.14.0", "dependencies": ["ffi"]}, {"name": "wakelock_plus_platform_interface", "version": "1.2.3", "dependencies": ["flutter", "meta", "plugin_platform_interface"]}, {"name": "meta", "version": "1.16.0", "dependencies": []}, {"name": "flutter_web_plugins", "version": "0.0.0", "dependencies": ["characters", "collection", "flutter", "material_color_utilities", "meta", "vector_math"]}, {"name": "url_launcher_windows", "version": "3.1.4", "dependencies": ["flutter", "url_launcher_platform_interface"]}, {"name": "url_launcher_web", "version": "2.4.1", "dependencies": ["flutter", "flutter_web_plugins", "url_launcher_platform_interface", "web"]}, {"name": "url_launcher_platform_interface", "version": "2.3.2", "dependencies": ["flutter", "plugin_platform_interface"]}, {"name": "url_launcher_macos", "version": "3.2.2", "dependencies": ["flutter", "url_launcher_platform_interface"]}, {"name": "url_launcher_linux", "version": "3.2.1", "dependencies": ["flutter", "url_launcher_platform_interface"]}, {"name": "url_launcher_ios", "version": "6.3.3", "dependencies": ["flutter", "url_launcher_platform_interface"]}, {"name": "url_launcher_android", "version": "6.3.16", "dependencies": ["flutter", "url_launcher_platform_interface"]}, {"name": "sign_in_with_apple_web", "version": "3.0.0", "dependencies": ["flutter", "flutter_web_plugins", "sign_in_with_apple_platform_interface"]}, {"name": "sign_in_with_apple_platform_interface", "version": "2.0.0", "dependencies": ["flutter", "meta", "plugin_platform_interface"]}, {"name": "ffi", "version": "2.1.4", "dependencies": []}, {"name": "file", "version": "7.0.1", "dependencies": ["meta", "path"]}, {"name": "share_plus_platform_interface", "version": "6.0.0", "dependencies": ["cross_file", "flutter", "meta", "mime", "path_provider", "plugin_platform_interface", "uuid"]}, {"name": "cross_file", "version": "0.3.4+2", "dependencies": ["meta", "web"]}, {"name": "permission_handler_platform_interface", "version": "4.3.0", "dependencies": ["flutter", "meta", "plugin_platform_interface"]}, {"name": "permission_handler_windows", "version": "0.2.1", "dependencies": ["flutter", "permission_handler_platform_interface"]}, {"name": "permission_handler_html", "version": "0.1.3+5", "dependencies": ["flutter", "flutter_web_plugins", "permission_handler_platform_interface", "web"]}, {"name": "permission_handler_apple", "version": "9.4.7", "dependencies": ["flutter", "permission_handler_platform_interface"]}, {"name": "permission_handler_android", "version": "13.0.1", "dependencies": ["flutter", "permission_handler_platform_interface"]}, {"name": "path_provider_windows", "version": "2.3.0", "dependencies": ["ffi", "flutter", "path", "path_provider_platform_interface"]}, {"name": "path_provider_platform_interface", "version": "2.1.2", "dependencies": ["flutter", "platform", "plugin_platform_interface"]}, {"name": "path_provider_linux", "version": "2.2.1", "dependencies": ["ffi", "flutter", "path", "path_provider_platform_interface", "xdg_directories"]}, {"name": "path_provider_foundation", "version": "2.4.1", "dependencies": ["flutter", "path_provider_platform_interface"]}, {"name": "path_provider_android", "version": "2.2.17", "dependencies": ["flutter", "path_provider_platform_interface"]}, {"name": "clock", "version": "1.1.2", "dependencies": []}, {"name": "package_info_plus_platform_interface", "version": "3.2.0", "dependencies": ["flutter", "meta", "plugin_platform_interface"]}, {"name": "vector_math", "version": "2.1.4", "dependencies": []}, {"name": "archive", "version": "4.0.7", "dependencies": ["crypto", "path", "posix"]}, {"name": "crypto", "version": "3.0.6", "dependencies": ["typed_data"]}, {"name": "uuid", "version": "4.5.1", "dependencies": ["crypto", "fixnum", "meta", "sprintf"]}, {"name": "async", "version": "2.13.0", "dependencies": ["collection", "meta"]}, {"name": "rxdart", "version": "0.28.0", "dependencies": []}, {"name": "audio_session", "version": "0.1.25", "dependencies": ["flutter", "flutter_web_plugins", "meta", "rxdart"]}, {"name": "just_audio_web", "version": "0.4.16", "dependencies": ["flutter", "flutter_web_plugins", "just_audio_platform_interface", "synchronized", "web"]}, {"name": "just_audio_platform_interface", "version": "4.5.0", "dependencies": ["flutter", "plugin_platform_interface"]}, {"name": "equatable", "version": "2.0.7", "dependencies": ["collection", "meta"]}, {"name": "in_app_purchase_platform_interface", "version": "1.4.0", "dependencies": ["flutter", "plugin_platform_interface"]}, {"name": "collection", "version": "1.19.1", "dependencies": []}, {"name": "in_app_purchase_storekit", "version": "0.4.2", "dependencies": ["collection", "flutter", "in_app_purchase_platform_interface", "json_annotation"]}, {"name": "image_picker_windows", "version": "0.2.1+1", "dependencies": ["file_selector_platform_interface", "file_selector_windows", "flutter", "image_picker_platform_interface"]}, {"name": "image_picker_platform_interface", "version": "2.10.1", "dependencies": ["cross_file", "flutter", "http", "plugin_platform_interface"]}, {"name": "image_picker_macos", "version": "0.2.1+2", "dependencies": ["file_selector_macos", "file_selector_platform_interface", "flutter", "image_picker_platform_interface"]}, {"name": "image_picker_linux", "version": "0.2.1+2", "dependencies": ["file_selector_linux", "file_selector_platform_interface", "flutter", "image_picker_platform_interface"]}, {"name": "image_picker_ios", "version": "0.8.12+2", "dependencies": ["flutter", "image_picker_platform_interface"]}, {"name": "image_picker_for_web", "version": "3.0.6", "dependencies": ["flutter", "flutter_web_plugins", "image_picker_platform_interface", "mime", "web"]}, {"name": "image_picker_android", "version": "0.8.12+23", "dependencies": ["flutter", "flutter_plugin_android_lifecycle", "image_picker_platform_interface"]}, {"name": "image_cropper_for_web", "version": "6.1.0", "dependencies": ["flutter", "flutter_web_plugins", "image_cropper_platform_interface", "web"]}, {"name": "image_cropper_platform_interface", "version": "7.1.0", "dependencies": ["flutter", "http", "plugin_platform_interface"]}, {"name": "iabtcf_consent_info_web", "version": "3.0.0", "dependencies": ["flutter", "flutter_web_plugins", "iabtcf_consent_info_platform_interface", "js"]}, {"name": "iabtcf_consent_info_platform_interface", "version": "1.0.5", "dependencies": ["flutter", "meta", "plugin_platform_interface"]}, {"name": "http_parser", "version": "4.1.2", "dependencies": ["collection", "source_span", "string_scanner", "typed_data"]}, {"name": "hive", "version": "2.2.3", "dependencies": ["crypto", "meta"]}, {"name": "google_sign_in_web", "version": "0.12.4+4", "dependencies": ["flutter", "flutter_web_plugins", "google_identity_services_web", "google_sign_in_platform_interface", "http", "web"]}, {"name": "google_sign_in_platform_interface", "version": "2.5.0", "dependencies": ["flutter", "plugin_platform_interface"]}, {"name": "google_sign_in_ios", "version": "5.9.0", "dependencies": ["flutter", "google_sign_in_platform_interface"]}, {"name": "google_sign_in_android", "version": "6.2.1", "dependencies": ["flutter", "google_sign_in_platform_interface"]}, {"name": "webview_flutter_wkwebview", "version": "3.22.0", "dependencies": ["flutter", "meta", "path", "webview_flutter_platform_interface"]}, {"name": "webview_flutter_android", "version": "4.7.0", "dependencies": ["flutter", "meta", "webview_flutter_platform_interface"]}, {"name": "html", "version": "0.15.6", "dependencies": ["csslib", "source_span"]}, {"name": "fwfh_webview", "version": "0.15.4", "dependencies": ["flutter", "flutter_widget_from_html_core", "logging", "web", "webview_flutter", "webview_flutter_android", "webview_flutter_wkwebview"]}, {"name": "fwfh_url_launcher", "version": "0.16.0", "dependencies": ["flutter", "flutter_widget_from_html_core", "url_launcher"]}, {"name": "fwfh_svg", "version": "0.16.0", "dependencies": ["flutter", "flutter_svg", "flutter_widget_from_html_core"]}, {"name": "fwfh_just_audio", "version": "0.16.0", "dependencies": ["flutter", "flutter_widget_from_html_core", "just_audio"]}, {"name": "fwfh_chewie", "version": "0.16.0", "dependencies": ["chewie", "flutter", "flutter_widget_from_html_core", "video_player"]}, {"name": "fwfh_cached_network_image", "version": "0.16.0", "dependencies": ["cached_network_image", "flutter", "flutter_cache_manager", "flutter_widget_from_html_core"]}, {"name": "flutter_widget_from_html_core", "version": "0.16.0", "dependencies": ["csslib", "flutter", "html", "logging"]}, {"name": "markdown", "version": "7.3.0", "dependencies": ["args", "meta"]}, {"name": "vector_graphics_compiler", "version": "1.1.17", "dependencies": ["args", "meta", "path", "path_parsing", "vector_graphics_codec", "xml"]}, {"name": "vector_graphics_codec", "version": "1.1.13", "dependencies": []}, {"name": "vector_graphics", "version": "1.1.19", "dependencies": ["flutter", "http", "vector_graphics_codec"]}, {"name": "timezone", "version": "0.10.1", "dependencies": ["http", "path"]}, {"name": "flutter_local_notifications_platform_interface", "version": "9.1.0", "dependencies": ["plugin_platform_interface"]}, {"name": "flutter_local_notifications_windows", "version": "1.0.0", "dependencies": ["ffi", "flutter", "flutter_local_notifications_platform_interface", "meta", "timezone", "xml"]}, {"name": "flutter_local_notifications_linux", "version": "6.0.0", "dependencies": ["dbus", "ffi", "flutter", "flutter_local_notifications_platform_interface", "path", "xdg_directories"]}, {"name": "flutter_cache_manager", "version": "3.4.1", "dependencies": ["clock", "collection", "file", "flutter", "http", "path", "path_provider", "rxdart", "sqflite", "uuid"]}, {"name": "provider", "version": "6.1.5", "dependencies": ["collection", "flutter", "nested"]}, {"name": "bloc", "version": "9.0.0", "dependencies": ["meta"]}, {"name": "sky_engine", "version": "0.0.0", "dependencies": []}, {"name": "material_color_utilities", "version": "0.11.1", "dependencies": ["collection"]}, {"name": "characters", "version": "1.4.0", "dependencies": []}, {"name": "firebase_messaging_web", "version": "3.10.9", "dependencies": ["_flutterfire_internals", "firebase_core", "firebase_core_web", "firebase_messaging_platform_interface", "flutter", "flutter_web_plugins", "meta", "web"]}, {"name": "firebase_messaging_platform_interface", "version": "4.6.9", "dependencies": ["_flutterfire_internals", "firebase_core", "flutter", "meta", "plugin_platform_interface"]}, {"name": "firebase_core_platform_interface", "version": "6.0.0", "dependencies": ["collection", "flutter", "flutter_test", "meta", "plugin_platform_interface"]}, {"name": "firebase_core_web", "version": "2.24.1", "dependencies": ["firebase_core_platform_interface", "flutter", "flutter_web_plugins", "meta", "web"]}, {"name": "firebase_auth_web", "version": "5.15.2", "dependencies": ["firebase_auth_platform_interface", "firebase_core", "firebase_core_web", "flutter", "flutter_web_plugins", "http_parser", "meta", "web"]}, {"name": "firebase_auth_platform_interface", "version": "7.7.2", "dependencies": ["_flutterfire_internals", "collection", "firebase_core", "flutter", "meta", "plugin_platform_interface"]}, {"name": "firebase_analytics_web", "version": "0.5.10+15", "dependencies": ["_flutterfire_internals", "firebase_analytics_platform_interface", "firebase_core", "firebase_core_web", "flutter", "flutter_web_plugins"]}, {"name": "firebase_analytics_platform_interface", "version": "4.4.2", "dependencies": ["_flutterfire_internals", "firebase_core", "flutter", "meta", "plugin_platform_interface"]}, {"name": "pointycastle", "version": "3.9.1", "dependencies": ["collection", "convert", "js"]}, {"name": "asn1lib", "version": "1.6.4", "dependencies": []}, {"name": "diacritic", "version": "0.1.6", "dependencies": []}, {"name": "nm", "version": "0.5.0", "dependencies": ["dbus"]}, {"name": "connectivity_plus_platform_interface", "version": "2.0.1", "dependencies": ["flutter", "meta", "plugin_platform_interface"]}, {"name": "cloud_firestore_web", "version": "4.4.11", "dependencies": ["_flutterfire_internals", "cloud_firestore_platform_interface", "collection", "firebase_core", "firebase_core_web", "flutter", "flutter_web_plugins"]}, {"name": "cloud_firestore_platform_interface", "version": "6.6.11", "dependencies": ["_flutterfire_internals", "collection", "firebase_core", "flutter", "meta", "plugin_platform_interface"]}, {"name": "octo_image", "version": "2.1.0", "dependencies": ["flutter"]}, {"name": "cached_network_image_web", "version": "1.3.1", "dependencies": ["cached_network_image_platform_interface", "flutter", "flutter_cache_manager", "web"]}, {"name": "cached_network_image_platform_interface", "version": "4.1.1", "dependencies": ["flutter", "flutter_cache_manager"]}, {"name": "string_scanner", "version": "1.4.1", "dependencies": ["source_span"]}, {"name": "source_span", "version": "1.10.1", "dependencies": ["collection", "path", "term_glyph"]}, {"name": "xml", "version": "6.5.0", "dependencies": ["collection", "meta", "petitparser"]}, {"name": "flutter_inappwebview_windows", "version": "0.6.0", "dependencies": ["flutter", "flutter_inappwebview_platform_interface"]}, {"name": "flutter_inappwebview_web", "version": "1.1.2", "dependencies": ["flutter", "flutter_inappwebview_platform_interface", "flutter_web_plugins", "web"]}, {"name": "flutter_inappwebview_macos", "version": "1.1.2", "dependencies": ["flutter", "flutter_inappwebview_platform_interface"]}, {"name": "flutter_inappwebview_ios", "version": "1.1.2", "dependencies": ["flutter", "flutter_inappwebview_platform_interface"]}, {"name": "flutter_inappwebview_android", "version": "1.1.3", "dependencies": ["flutter", "flutter_inappwebview_platform_interface"]}, {"name": "flutter_inappwebview_platform_interface", "version": "1.3.0+1", "dependencies": ["flutter", "flutter_inappwebview_internal_annotations", "plugin_platform_interface"]}, {"name": "webview_flutter_platform_interface", "version": "2.13.1", "dependencies": ["flutter", "meta", "plugin_platform_interface"]}, {"name": "plugin_platform_interface", "version": "2.1.8", "dependencies": ["meta"]}, {"name": "platform", "version": "3.1.6", "dependencies": []}, {"name": "xdg_directories", "version": "1.1.0", "dependencies": ["meta", "path"]}, {"name": "posix", "version": "6.0.2", "dependencies": ["ffi", "meta", "path"]}, {"name": "typed_data", "version": "1.4.0", "dependencies": ["collection"]}, {"name": "fixnum", "version": "1.1.1", "dependencies": []}, {"name": "sprintf", "version": "7.0.0", "dependencies": []}, {"name": "synchronized", "version": "3.3.1", "dependencies": []}, {"name": "file_selector_windows", "version": "0.9.3+4", "dependencies": ["cross_file", "file_selector_platform_interface", "flutter"]}, {"name": "file_selector_platform_interface", "version": "2.6.2", "dependencies": ["cross_file", "flutter", "http", "plugin_platform_interface"]}, {"name": "file_selector_macos", "version": "0.9.4+3", "dependencies": ["cross_file", "file_selector_platform_interface", "flutter"]}, {"name": "file_selector_linux", "version": "0.9.3+2", "dependencies": ["cross_file", "file_selector_platform_interface", "flutter"]}, {"name": "flutter_plugin_android_lifecycle", "version": "2.0.28", "dependencies": ["flutter"]}, {"name": "js", "version": "0.6.7", "dependencies": ["meta"]}, {"name": "google_identity_services_web", "version": "0.3.3+1", "dependencies": ["meta", "web"]}, {"name": "csslib", "version": "1.0.2", "dependencies": ["source_span"]}, {"name": "logging", "version": "1.3.0", "dependencies": []}, {"name": "video_player", "version": "2.10.0", "dependencies": ["flutter", "html", "video_player_android", "video_player_avfoundation", "video_player_platform_interface", "video_player_web"]}, {"name": "chewie", "version": "1.11.3", "dependencies": ["cupertino_icons", "flutter", "provider", "video_player", "wakelock_plus"]}, {"name": "path_parsing", "version": "1.1.0", "dependencies": ["meta", "vector_math"]}, {"name": "sqflite", "version": "2.4.2", "dependencies": ["flutter", "path", "sqflite_android", "sqflite_common", "sqflite_darwin", "sqflite_platform_interface"]}, {"name": "nested", "version": "1.0.0", "dependencies": ["flutter"]}, {"name": "_flutterfire_internals", "version": "1.3.58", "dependencies": ["collection", "firebase_core", "firebase_core_platform_interface", "flutter", "meta"]}, {"name": "flutter_test", "version": "0.0.0", "dependencies": ["async", "boolean_selector", "characters", "clock", "collection", "fake_async", "flutter", "leak_tracker", "leak_tracker_flutter_testing", "leak_tracker_testing", "matcher", "material_color_utilities", "meta", "path", "source_span", "stack_trace", "stream_channel", "string_scanner", "term_glyph", "test_api", "vector_math", "vm_service"]}, {"name": "convert", "version": "3.1.2", "dependencies": ["typed_data"]}, {"name": "term_glyph", "version": "1.2.2", "dependencies": []}, {"name": "petitparser", "version": "6.1.0", "dependencies": ["collection", "meta"]}, {"name": "flutter_inappwebview_internal_annotations", "version": "1.2.0", "dependencies": []}, {"name": "video_player_web", "version": "2.3.5", "dependencies": ["flutter", "flutter_web_plugins", "video_player_platform_interface", "web"]}, {"name": "video_player_platform_interface", "version": "6.3.0", "dependencies": ["flutter", "plugin_platform_interface"]}, {"name": "video_player_avfoundation", "version": "2.7.1", "dependencies": ["flutter", "video_player_platform_interface"]}, {"name": "video_player_android", "version": "2.8.7", "dependencies": ["flutter", "video_player_platform_interface"]}, {"name": "cupertino_icons", "version": "1.0.8", "dependencies": []}, {"name": "sqflite_common", "version": "2.5.5", "dependencies": ["meta", "path", "synchronized"]}, {"name": "sqflite_platform_interface", "version": "2.4.0", "dependencies": ["flutter", "meta", "platform", "plugin_platform_interface", "sqflite_common"]}, {"name": "sqflite_darwin", "version": "2.4.2", "dependencies": ["flutter", "meta", "path", "sqflite_common", "sqflite_platform_interface"]}, {"name": "sqflite_android", "version": "2.4.1", "dependencies": ["flutter", "path", "sqflite_common", "sqflite_platform_interface"]}, {"name": "vm_service", "version": "15.0.0", "dependencies": []}, {"name": "stream_channel", "version": "2.1.4", "dependencies": ["async"]}, {"name": "leak_tracker_testing", "version": "3.0.1", "dependencies": ["leak_tracker", "matcher", "meta"]}, {"name": "leak_tracker", "version": "10.0.9", "dependencies": ["clock", "collection", "meta", "path", "vm_service"]}, {"name": "boolean_selector", "version": "2.1.2", "dependencies": ["source_span", "string_scanner"]}, {"name": "leak_tracker_flutter_testing", "version": "3.0.9", "dependencies": ["flutter", "leak_tracker", "leak_tracker_testing", "matcher", "meta"]}, {"name": "stack_trace", "version": "1.12.1", "dependencies": ["path"]}, {"name": "fake_async", "version": "1.3.3", "dependencies": ["clock", "collection"]}, {"name": "matcher", "version": "0.12.17", "dependencies": ["async", "meta", "stack_trace", "term_glyph", "test_api"]}, {"name": "test_api", "version": "0.7.4", "dependencies": ["async", "boolean_selector", "collection", "meta", "source_span", "stack_trace", "stream_channel", "string_scanner", "term_glyph"]}], "configVersion": 1}