/*
 *  /MathJax/fonts/HTML-CSS/TeX/png/Size4/Regular/Main.js
 *  
 *  Copyright (c) 2010-2013 The MathJax Consortium
 *
 *  Part of the MathJax library.
 *  See http://www.mathjax.org for details.
 * 
 *  Licensed under the Apache License, Version 2.0;
 *  you may not use this file except in compliance with the License.
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 */

MathJax.OutputJax["HTML-CSS"].defineImageData({MathJax_Size4:{32:[[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0]],40:[[6,21,9],[7,25,10],[8,30,12],[9,36,15],[11,42,17],[13,50,21],[15,59,24],[18,70,29],[21,83,35],[25,99,41],[30,118,49],[36,140,58],[42,166,69],[50,198,82]],41:[[4,22,9],[5,25,10],[6,30,12],[7,36,15],[8,42,17],[10,50,21],[11,59,24],[13,70,29],[16,84,35],[19,99,41],[22,118,49],[26,140,58],[31,166,69],[37,198,82]],47:[[9,21,9],[11,25,10],[12,30,12],[15,35,15],[17,42,17],[21,50,21],[24,59,24],[29,70,29],[34,83,35],[41,99,41],[48,118,49],[57,140,58],[68,166,69],[81,198,82]],91:[[4,21,9],[5,25,11],[6,30,12],[7,35,15],[8,42,17],[10,51,22],[12,60,25],[14,70,29],[16,83,35],[19,99,41],[23,117,49],[27,140,58],[32,166,69],[38,197,82]],92:[[9,21,9],[11,25,10],[12,30,12],[15,35,15],[17,42,17],[21,50,21],[24,59,24],[29,70,29],[34,83,35],[41,99,41],[48,118,49],[57,140,58],[68,166,69],[81,198,82]],93:[[3,21,9],[3,25,11],[4,30,12],[4,35,15],[5,42,17],[6,51,22],[7,60,25],[8,70,29],[9,83,35],[11,99,41],[13,117,49],[15,140,58],[18,166,69],[21,197,82]],123:[[5,21,9],[6,25,10],[7,30,12],[8,36,15],[10,42,17],[11,50,21],[13,59,24],[16,70,29],[19,83,35],[22,99,41],[26,118,49],[31,140,58],[37,166,69],[44,198,82]],125:[[5,21,9],[6,25,10],[7,30,12],[8,36,15],[10,42,17],[11,50,21],[13,59,24],[16,70,29],[19,83,35],[22,99,41],[26,118,49],[31,140,58],[37,166,69],[44,198,82]],160:[[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0],[1,1,0]],710:[[15,2,-4],[17,3,-5],[20,3,-6],[24,4,-7],[28,4,-8],[33,5,-9],[39,6,-11],[46,7,-13],[54,8,-16],[64,10,-18],[76,11,-22],[90,13,-26],[107,16,-31],[127,19,-37]],732:[[14,2,-4],[17,2,-5],[20,3,-5],[24,5,-6],[28,4,-8],[33,4,-10],[38,5,-11],[45,6,-14],[54,7,-16],[63,8,-19],[75,10,-23],[89,12,-27],[106,14,-32],[125,16,-38]],770:[[15,2,-4],[17,3,-5],[20,3,-6],[24,4,-7],[28,4,-8],[33,5,-9],[39,6,-11],[46,7,-13],[54,8,-16],[64,10,-18],[76,11,-22],[90,13,-26],[107,16,-31],[127,19,-37]],771:[[14,2,-4],[16,2,-5],[19,3,-5],[23,5,-6],[27,4,-8],[32,4,-10],[37,5,-11],[44,6,-14],[53,7,-16],[63,8,-19],[74,10,-23],[88,12,-27],[105,14,-32],[125,16,-38]],8730:[[8,22,9],[9,26,11],[10,31,13],[12,36,15],[15,43,18],[17,51,21],[20,60,25],[24,71,30],[29,84,35],[34,100,42],[40,118,49],[48,141,59],[57,167,70],[67,199,83]],8968:[[5,21,9],[6,26,11],[7,30,13],[8,36,15],[9,43,18],[11,51,21],[13,60,25],[15,70,29],[18,84,35],[21,100,42],[25,118,49],[30,141,59],[36,166,69],[42,199,83]],8969:[[3,21,9],[4,26,11],[4,30,13],[6,36,15],[6,43,18],[7,51,21],[8,60,25],[9,70,29],[11,84,35],[13,100,42],[15,118,49],[18,141,59],[21,166,69],[25,199,83]],8970:[[5,22,9],[6,26,11],[7,31,13],[8,36,15],[9,43,18],[11,50,21],[13,60,25],[15,70,29],[18,84,35],[21,100,42],[25,118,49],[30,140,58],[36,167,70],[42,199,83]],8971:[[3,22,9],[4,26,11],[4,31,13],[6,36,15],[6,43,18],[7,50,21],[8,60,25],[9,70,29],[11,84,35],[13,100,42],[15,118,49],[18,140,58],[21,167,70],[25,199,83]],9115:[[6,13,5],[7,16,6],[9,19,7],[10,22,8],[12,26,10],[14,31,11],[17,36,13],[20,43,16],[24,51,19],[28,60,22],[33,72,26],[40,85,31],[47,101,37],[56,120,44]],9116:[[3,6,1],[4,7,1],[5,7,1],[5,9,1],[6,10,1],[7,12,1],[9,13,1],[10,16,1],[12,18,1],[14,22,1],[17,25,1],[20,31,2],[24,36,2],[28,43,2]],9117:[[6,14,5],[7,16,6],[9,19,7],[10,22,8],[12,26,9],[14,31,11],[17,36,13],[20,43,15],[24,51,18],[28,61,22],[33,72,26],[40,85,30],[47,101,36],[56,120,43]],9118:[[5,13,5],[5,16,6],[6,19,7],[7,22,8],[9,26,10],[10,31,11],[12,36,13],[14,43,16],[17,51,19],[20,60,22],[23,72,26],[28,85,31],[33,101,37],[39,120,44]],9119:[[5,6,1],[5,7,1],[6,7,1],[7,9,1],[9,10,1],[10,12,1],[12,13,1],[14,16,1],[17,18,1],[20,22,1],[23,25,1],[28,31,2],[33,36,2],[39,43,2]],9120:[[5,14,5],[5,16,6],[6,19,7],[7,22,8],[9,26,9],[10,31,11],[12,36,13],[14,43,15],[17,51,18],[20,61,22],[23,72,26],[28,85,30],[33,101,36],[39,120,43]],9121:[[5,13,5],[6,16,6],[7,19,7],[8,22,8],[9,25,9],[11,31,11],[14,36,13],[16,42,15],[19,50,18],[22,60,22],[27,72,26],[32,84,30],[37,100,36],[44,119,43]],9122:[[3,5,0],[4,5,0],[4,6,0],[5,8,0],[6,9,0],[7,10,0],[8,12,0],[10,14,0],[12,17,0],[14,20,0],[16,24,0],[19,28,0],[23,34,0],[27,40,0]],9123:[[5,13,5],[6,16,6],[7,19,7],[8,22,8],[9,25,9],[11,31,11],[14,36,13],[16,42,15],[19,50,18],[22,61,22],[27,72,26],[32,84,30],[37,100,36],[44,119,43]],9124:[[3,13,5],[3,16,6],[5,19,7],[5,22,8],[5,25,9],[6,31,11],[8,36,13],[9,42,15],[11,50,18],[12,60,22],[15,72,26],[18,84,30],[21,100,36],[23,119,43]],9125:[[3,5,0],[3,5,0],[4,6,0],[5,8,0],[5,9,0],[6,10,0],[7,12,0],[9,14,0],[10,17,0],[12,20,0],[14,24,0],[17,28,0],[20,34,0],[23,40,0]],9126:[[3,13,5],[3,16,6],[5,19,7],[5,22,8],[5,25,9],[6,31,11],[8,36,13],[9,42,15],[11,50,18],[12,61,22],[15,72,26],[18,84,30],[21,100,36],[23,119,43]],9127:[[5,8,1],[6,9,1],[8,10,1],[9,12,1],[10,14,1],[12,16,1],[14,19,1],[17,22,1],[20,26,1],[24,31,1],[29,37,1],[34,44,2],[40,52,2],[48,62,2]],9128:[[4,14,5],[5,16,6],[5,19,7],[6,23,9],[7,27,10],[9,32,12],[10,37,14],[12,44,16],[15,52,19],[17,62,23],[20,73,27],[24,87,32],[28,103,38],[34,123,45]],9129:[[6,8,7],[6,9,8],[8,10,9],[9,12,11],[10,14,13],[12,16,15],[14,19,18],[17,22,21],[20,26,25],[24,31,30],[29,37,36],[34,43,42],[40,51,50],[48,61,60]],9130:[[4,4,1],[5,4,1],[5,5,1],[6,5,1],[7,6,1],[9,7,1],[10,8,1],[12,9,1],[14,10,1],[17,12,1],[20,14,1],[24,17,2],[28,19,1],[34,23,2]],9131:[[4,8,1],[5,9,1],[5,10,1],[6,12,1],[7,14,1],[9,16,1],[10,19,1],[12,22,1],[14,26,1],[17,31,1],[20,37,1],[24,43,1],[28,52,2],[34,62,2]],9132:[[6,14,5],[6,16,6],[8,19,7],[9,23,9],[10,27,10],[13,32,12],[14,37,14],[17,44,16],[20,52,19],[24,62,23],[29,73,27],[34,87,32],[40,103,38],[48,123,45]],9133:[[4,8,7],[5,9,8],[5,10,9],[6,12,11],[7,14,13],[9,16,15],[10,19,18],[12,22,21],[14,26,25],[17,31,30],[20,37,36],[24,44,42],[28,52,50],[34,62,60]],9143:[[5,14,7],[6,16,8],[8,19,9],[9,22,11],[11,26,13],[13,31,15],[15,37,18],[18,43,21],[21,51,25],[25,61,30],[30,72,35],[35,86,42],[42,101,49],[49,121,59]],10216:[[5,22,9],[6,26,11],[7,31,13],[9,36,15],[10,43,18],[12,51,21],[14,60,25],[17,70,29],[20,84,35],[24,100,42],[28,118,49],[33,140,58],[39,166,69],[47,199,83]],10217:[[5,22,9],[6,26,11],[7,31,13],[8,36,15],[10,43,18],[11,51,21],[13,60,25],[16,70,29],[19,84,35],[22,100,42],[26,118,49],[31,140,58],[37,167,70],[44,199,83]],57344:[[5,6,1],[6,7,1],[8,8,1],[9,9,1],[11,10,1],[13,12,1],[15,14,1],[18,16,1],[21,19,1],[25,22,1],[29,26,1],[35,30,1],[41,36,1],[49,43,1]],57345:[[8,5,0],[9,6,1],[11,7,1],[13,8,1],[15,9,0],[18,11,1],[22,13,1],[25,15,1],[30,18,1],[36,21,1],[42,25,1],[50,30,1],[60,35,1],[71,41,1]],57680:[[5,3,2],[5,3,2],[6,5,3],[7,5,3],[8,5,3],[9,6,4],[10,8,5],[12,8,5],[14,10,6],[17,12,8],[19,14,9],[24,16,10],[28,19,12],[33,23,15]],57681:[[5,3,2],[5,4,2],[6,5,3],[7,5,3],[8,5,3],[9,7,4],[11,8,5],[12,8,5],[15,10,6],[17,12,7],[20,14,9],[23,16,10],[28,19,12],[33,23,14]],57682:[[5,3,0],[5,3,0],[6,4,0],[7,4,0],[8,5,0],[9,6,0],[10,7,0],[12,8,0],[14,10,0],[17,11,0],[19,13,0],[24,16,0],[28,19,0],[33,22,0]],57683:[[5,3,0],[5,3,0],[6,4,0],[7,4,0],[8,5,0],[9,6,0],[11,7,0],[12,8,0],[15,10,0],[17,12,0],[20,14,0],[24,16,0],[28,19,0],[33,23,0]],57684:[[4,1,0],[5,1,0],[6,2,0],[6,2,0],[7,2,0],[8,2,0],[9,3,0],[11,3,0],[13,4,0],[15,4,0],[18,5,0],[21,6,0],[24,7,0],[28,8,0]]}});MathJax.Ajax.loadComplete(MathJax.OutputJax["HTML-CSS"].imgDir+"/Size4/Regular"+MathJax.OutputJax["HTML-CSS"].imgPacked+"/Main.js");

